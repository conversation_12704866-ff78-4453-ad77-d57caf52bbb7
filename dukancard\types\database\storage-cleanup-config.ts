export interface StorageCleanupConfigRow {
  id: number;
  batch_size: number | null;
  max_batches_per_run: number | null;
  last_processed_offset: number | null;
  last_run_at: string | null;
  total_users_processed: number | null;
  total_files_deleted: number | null;
  is_running: boolean | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface StorageCleanupConfigInsert {
  id?: number;
  batch_size?: number | null;
  max_batches_per_run?: number | null;
  last_processed_offset?: number | null;
  last_run_at?: string | null;
  total_users_processed?: number | null;
  total_files_deleted?: number | null;
  is_running?: boolean | null;
  created_at?: string | null;
  updated_at?: string | null;
}

export interface StorageCleanupConfigUpdate {
  id?: number;
  batch_size?: number | null;
  max_batches_per_run?: number | null;
  last_processed_offset?: number | null;
  last_run_at?: string | null;
  total_users_processed?: number | null;
  total_files_deleted?: number | null;
  is_running?: boolean | null;
  created_at?: string | null;
  updated_at?: string | null;
}

export type StorageCleanupConfig = StorageCleanupConfigRow;
