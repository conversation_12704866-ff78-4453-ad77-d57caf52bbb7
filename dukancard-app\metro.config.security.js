// Security-enhanced Metro configuration with JavaScript obfuscation
// This file will be used to replace metro.config.js when implementing security

const { getDefaultConfig } = require('expo/metro-config');

// JavaScript obfuscation configuration
const jsoMetroPlugin = require("obfuscator-io-metro-plugin")(
  {
    // Obfuscation options
    compact: false,
    sourceMap: false,
    controlFlowFlattening: true,
    controlFlowFlatteningThreshold: 1,
    numbersToExpressions: true,
    simplify: true,
    stringArrayShuffle: true,
    splitStrings: true,
    stringArrayThreshold: 1,
    identifiersRenaming: true,
    renameGlobals: false,
    selfDefending: true,
    stringArray: true,
    rotateStringArray: true,
    unicodeEscapeSequence: false,
    deadCodeInjection: true,
    deadCodeInjectionThreshold: 0.4,
    debugProtection: true,
    debugProtectionInterval: true,
    disableConsoleOutput: true,
    domainLock: [], // Add your domain if needed
    reservedNames: [], // Add any reserved names
    seed: 0, // Use random seed for production
  },
  {
    runInDev: false, // Only run in production builds
    logObfuscatedFiles: true,
    enableInDevelopment: false, // Disable in development for faster builds
  }
);

const config = getDefaultConfig(__dirname);

// Apply obfuscation plugin
module.exports = {
  ...config,
  transformer: {
    ...config.transformer,
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: false,
      },
    }),
  },
  ...jsoMetroPlugin,
};
