import React, { useState, useEffect } from "react";
import { View, Text, TouchableOpacity, Image } from "react-native";
import { useColorScheme } from "@/src/hooks/useColorScheme";
import { Colors } from "@/src/constants/Colors";
import { User } from "lucide-react-native";
import { createCustomerPostCreatorStyles } from "@/styles/feed/customer-post-creator-styles";
import { CustomerPostModal } from "./CustomerPostModal";
import { supabase } from "@/lib/supabase";
import { CustomerPostService } from "@/backend/supabase/services/customer/customerPostService";

interface CustomerPostCreatorProps {
  customerName?: string;
  onPostCreated?: () => void;
}

export function CustomerPostCreator({
  customerName = "Customer",
  onPostCreated,
}: CustomerPostCreatorProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === "dark";
  const styles = createCustomerPostCreatorStyles();

  // State
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [customerDisplayName, setCustomerDisplayName] = useState(customerName);
  const [customerAvatarUrl, setCustomerAvatarUrl] = useState<string | null>(null);

  useEffect(() => {
    const fetchCustomerProfile = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const { data: profile } = await CustomerPostService.getCustomerProfileById(user.id);
        if (profile) {
          setCustomerDisplayName(profile.name || "Customer");
          setCustomerAvatarUrl(profile.avatar_url);
        }
      }
    };
    fetchCustomerProfile();
  }, []);

  // Theme colors
  const backgroundColor = isDark ? "#000000" : "#FFFFFF";
  const textColor = isDark ? "#FFFFFF" : "#000000";
  const mutedTextColor = isDark ? "#A1A1AA" : "#71717A";
  const primaryColor = "#D4AF37";

  const handleOpenModal = () => {
    setIsModalVisible(true);
  };

  const handleCloseModal = () => {
    setIsModalVisible(false);
  };

  const handlePostCreated = () => {
    setIsModalVisible(false);
    if (onPostCreated) {
      onPostCreated();
    }
  };

  return (
    <View>
      {/* Collapsed State - Always Visible */}
      <TouchableOpacity
        style={[styles.container, { backgroundColor }]}
        onPress={handleOpenModal}
        activeOpacity={0.7}
      >
        <View style={styles.collapsedContent}>
          {customerAvatarUrl ? (
            <Image source={{ uri: customerAvatarUrl }} style={styles.avatar} />
          ) : (
            <View
              style={[
                styles.avatarPlaceholder,
                { backgroundColor: primaryColor },
              ]}
            >
              <Text style={styles.avatarText}>
                {customerDisplayName.charAt(0).toUpperCase()}
              </Text>
            </View>
          )}

          <View style={styles.textContainer}>
            <Text style={[styles.placeholderText, { color: mutedTextColor }]}>
              What&apos;s on your mind, {customerDisplayName}?
            </Text>
          </View>
        </View>
      </TouchableOpacity>

      {/* Full Screen Modal */}
      <CustomerPostModal
        visible={isModalVisible}
        onClose={handleCloseModal}
        customerName={customerDisplayName}
        onPostCreated={handlePostCreated}
      />
    </View>
  );
}
