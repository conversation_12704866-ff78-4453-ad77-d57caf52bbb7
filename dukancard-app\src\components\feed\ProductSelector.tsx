import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  Image,
  StyleSheet,
  ActivityIndicator,
  Alert,
  Modal,
} from 'react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { Colors } from '@/src/constants/Colors';
import { Search, Package, X, Check, ChevronUp, ChevronDown } from 'lucide-react-native';
import { searchBusinessProducts, getSelectedProducts, ProductData } from '@/lib/actions/products';

interface ProductSelectorProps {
  selectedProductIds: string[];
  onProductsChange: (productIds: string[]) => void;
}

interface ProductItemProps {
  product: ProductData;
  isSelected: boolean;
  onToggle: () => void;
  disabled?: boolean;
}

const ProductItem: React.FC<ProductItemProps> = ({ product, isSelected, onToggle, disabled }) => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const textColor = isDark ? '#FFFFFF' : '#000000';
  const mutedTextColor = isDark ? '#9CA3AF' : '#6B7280';
  const borderColor = isDark ? '#333333' : '#E5E7EB';
  const primaryColor = '#D4AF37';

  const formatPrice = (price: number | null | undefined) => {
    if (price === null || price === undefined) return 'N/A';
    return `₹${price.toLocaleString('en-IN')}`;
  };

  return (
    <TouchableOpacity
      style={[
        styles.productItem,
        { borderColor },
        disabled && styles.productItemDisabled
      ]}
      onPress={onToggle}
      disabled={disabled}
      activeOpacity={0.7}
    >
      {/* Product Image */}
      <View style={[styles.productImage, { backgroundColor: borderColor }]}>
        {product.image_url ? (
          <Image source={{ uri: product.image_url }} style={styles.productImageContent} />
        ) : (
          <Package size={20} color={mutedTextColor} />
        )}
      </View>

      {/* Product Details */}
      <View style={styles.productDetails}>
        <Text style={[styles.productName, { color: textColor }]} numberOfLines={1}>
          {product.name}
        </Text>
        <View style={styles.priceContainer}>
          {product.discounted_price ? (
            <>
              <Text style={[styles.discountedPrice, { color: primaryColor }]}>
                {formatPrice(product.discounted_price)}
              </Text>
              <Text style={[styles.originalPrice, { color: mutedTextColor }]}>
                {formatPrice(product.base_price)}
              </Text>
            </>
          ) : (
            <Text style={[styles.price, { color: textColor }]}>
              {formatPrice(product.base_price)}
            </Text>
          )}
        </View>
      </View>

      {/* Check Icon */}
      <View style={styles.checkContainer}>
        {isSelected && <Check size={16} color={primaryColor} />}
      </View>
    </TouchableOpacity>
  );
};

export function ProductSelector({ selectedProductIds, onProductsChange }: ProductSelectorProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<ProductData[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<ProductData[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isLoadingSelected, setIsLoadingSelected] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  const searchTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Theme colors
  const backgroundColor = isDark ? '#000000' : '#FFFFFF';
  const cardColor = isDark ? '#1A1A1A' : '#F8F9FA';
  const borderColor = isDark ? '#333333' : '#E5E7EB';
  const textColor = isDark ? '#FFFFFF' : '#000000';
  const mutedTextColor = isDark ? '#9CA3AF' : '#6B7280';
  const primaryColor = '#D4AF37';

  // Load selected products
  const loadSelectedProducts = useCallback(async () => {
    if (selectedProductIds.length === 0) {
      setSelectedProducts([]);
      return;
    }

    setIsLoadingSelected(true);
    try {
      const result = await getSelectedProducts(selectedProductIds);
      if (result.success && result.data) {
        // Maintain order of selectedProductIds
        const orderedProducts = selectedProductIds
          .map(id => result.data?.find((product: ProductData) => product.id === id))
          .filter(Boolean) as ProductData[];
        setSelectedProducts(orderedProducts);
      }
    } catch (error) {
      console.error('Error loading selected products:', error);
    } finally {
      setIsLoadingSelected(false);
    }
  }, [selectedProductIds]);

  useEffect(() => {
    loadSelectedProducts();
  }, [loadSelectedProducts]);

  // Search products with debouncing
  const searchProducts = useCallback(async (query: string) => {
    if (query.length < 2) {
      setSearchResults([]);
      setHasSearched(false);
      return;
    }

    setIsSearching(true);
    try {
      const result = await searchBusinessProducts(query);
      if (result.success && result.data) {
        setSearchResults(result.data);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Error searching products:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
      setHasSearched(true);
    }
  }, []);

  const handleSearchChange = (text: string) => {
    setSearchQuery(text);

    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      searchProducts(text);
    }, 300);
  };

  const toggleProduct = (product: ProductData) => {
    const isSelected = selectedProductIds.includes(product.id);
    let newSelectedIds: string[];

    if (isSelected) {
      newSelectedIds = selectedProductIds.filter(id => id !== product.id);
    } else {
      if (selectedProductIds.length >= 5) {
        Alert.alert('Limit Reached', 'You can only select up to 5 products per post.');
        return;
      }
      newSelectedIds = [...selectedProductIds, product.id];
    }

    onProductsChange(newSelectedIds);
  };

  const removeProduct = (productId: string) => {
    const newSelectedIds = selectedProductIds.filter(id => id !== productId);
    onProductsChange(newSelectedIds);
  };

  const moveProductUp = (productId: string) => {
    const currentIndex = selectedProductIds.indexOf(productId);
    if (currentIndex > 0) {
      const newSelectedIds = [...selectedProductIds];
      [newSelectedIds[currentIndex - 1], newSelectedIds[currentIndex]] =
        [newSelectedIds[currentIndex], newSelectedIds[currentIndex - 1]];
      onProductsChange(newSelectedIds);
    }
  };

  const moveProductDown = (productId: string) => {
    const currentIndex = selectedProductIds.indexOf(productId);
    if (currentIndex < selectedProductIds.length - 1) {
      const newSelectedIds = [...selectedProductIds];
      [newSelectedIds[currentIndex], newSelectedIds[currentIndex + 1]] =
        [newSelectedIds[currentIndex + 1], newSelectedIds[currentIndex]];
      onProductsChange(newSelectedIds);
    }
  };

  const renderSearchResult = ({ item }: { item: ProductData }) => (
    <ProductItem
      product={item}
      isSelected={selectedProductIds.includes(item.id)}
      onToggle={() => toggleProduct(item)}
      disabled={selectedProductIds.length >= 5 && !selectedProductIds.includes(item.id)}
    />
  );

  const renderSelectedProduct = ({ item, index }: { item: ProductData; index: number }) => (
    <View style={[styles.selectedProductItem, { backgroundColor: cardColor, borderColor }]}>
      <View style={[styles.productImage, { backgroundColor: borderColor }]}>
        {item.image_url ? (
          <Image source={{ uri: item.image_url }} style={styles.productImageContent} />
        ) : (
          <Package size={16} color={mutedTextColor} />
        )}
      </View>
      <View style={styles.selectedProductDetails}>
        <Text style={[styles.selectedProductName, { color: textColor }]} numberOfLines={1}>
          {item.name}
        </Text>
      </View>

      {/* Reorder buttons */}
      {selectedProducts.length > 1 && (
        <View style={styles.reorderButtons}>
          <TouchableOpacity
            style={[styles.reorderButton, { opacity: index === 0 ? 0.3 : 1 }]}
            onPress={() => moveProductUp(item.id)}
            disabled={index === 0}
          >
            <ChevronUp size={12} color={primaryColor} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.reorderButton, { opacity: index === selectedProducts.length - 1 ? 0.3 : 1 }]}
            onPress={() => moveProductDown(item.id)}
            disabled={index === selectedProducts.length - 1}
          >
            <ChevronDown size={12} color={primaryColor} />
          </TouchableOpacity>
        </View>
      )}

      <TouchableOpacity
        style={[styles.removeButton, { backgroundColor: '#EF4444' }]}
        onPress={() => removeProduct(item.id)}
      >
        <X size={12} color="#FFFFFF" />
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Trigger Button */}
      <TouchableOpacity
        style={[styles.triggerButton, { backgroundColor: cardColor, borderColor }]}
        onPress={() => setIsModalVisible(true)}
      >
        <Package size={16} color={primaryColor} />
        <Text style={[styles.triggerButtonText, { color: mutedTextColor }]}>
          {selectedProductIds.length > 0 
            ? `${selectedProductIds.length} product${selectedProductIds.length !== 1 ? 's' : ''} selected`
            : 'Link products to post'
          }
        </Text>
      </TouchableOpacity>

      {/* Selected Products List */}
      {selectedProducts.length > 0 && (
        <View style={styles.selectedProductsContainer}>
          <Text style={[styles.selectedProductsTitle, { color: textColor }]}>
            Linked Products ({selectedProducts.length}/5)
          </Text>
          <FlatList
            data={selectedProducts}
            renderItem={({ item, index }) => renderSelectedProduct({ item, index })}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.selectedProductsList}
          />
        </View>
      )}

      {/* Search Modal */}
      <Modal
        visible={isModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setIsModalVisible(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor }]}>
          {/* Header */}
          <View style={[styles.modalHeader, { borderBottomColor: borderColor }]}>
            <Text style={[styles.modalTitle, { color: textColor }]}>Select Products</Text>
            <TouchableOpacity onPress={() => setIsModalVisible(false)}>
              <X size={24} color={textColor} />
            </TouchableOpacity>
          </View>

          {/* Search Input */}
          <View style={[styles.searchContainer, { backgroundColor: cardColor, borderColor }]}>
            <Search size={16} color={mutedTextColor} />
            <TextInput
              style={[styles.searchInput, { color: textColor }]}
              placeholder="Search your products..."
              placeholderTextColor={mutedTextColor}
              value={searchQuery}
              onChangeText={handleSearchChange}
              autoFocus
            />
          </View>

          {/* Search Results */}
          <View style={styles.resultsContainer}>
            {isSearching ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={primaryColor} />
                <Text style={[styles.loadingText, { color: mutedTextColor }]}>
                  Searching products...
                </Text>
              </View>
            ) : searchQuery.length < 2 ? (
              <View style={styles.emptyContainer}>
                <Package size={48} color={mutedTextColor} />
                <Text style={[styles.emptyText, { color: mutedTextColor }]}>
                  Type at least 2 characters to search
                </Text>
              </View>
            ) : searchResults.length === 0 && hasSearched ? (
              <View style={styles.emptyContainer}>
                <Package size={48} color={mutedTextColor} />
                <Text style={[styles.emptyText, { color: mutedTextColor }]}>
                  No products found
                </Text>
                <Text style={[styles.emptySubtext, { color: mutedTextColor }]}>
                  Try a different search term
                </Text>
              </View>
            ) : (
              <FlatList
                data={searchResults}
                renderItem={renderSearchResult}
                keyExtractor={(item) => item.id}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.searchResultsList}
              />
            )}
          </View>

          {/* Footer */}
          <View style={[styles.modalFooter, { borderTopColor: borderColor }]}>
            <Text style={[styles.footerText, { color: mutedTextColor }]}>
              {selectedProductIds.length}/5 products selected
            </Text>
            {selectedProductIds.length >= 5 && (
              <Text style={[styles.limitText, { color: '#EF4444' }]}>
                Maximum limit reached
              </Text>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    gap: 12,
  },
  triggerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    gap: 8,
  },
  triggerButtonText: {
    fontSize: 14,
    flex: 1,
  },
  selectedProductsContainer: {
    gap: 8,
  },
  selectedProductsTitle: {
    fontSize: 14,
    fontWeight: '600',
  },
  selectedProductsList: {
    gap: 8,
  },
  selectedProductItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    borderWidth: 1,
    gap: 8,
    minWidth: 140,
  },
  selectedProductDetails: {
    flex: 1,
  },
  selectedProductName: {
    fontSize: 12,
    fontWeight: '500',
  },
  reorderButtons: {
    flexDirection: 'column',
    gap: 2,
  },
  reorderButton: {
    width: 16,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  removeButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 16,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  resultsContainer: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
  },
  loadingText: {
    fontSize: 14,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  searchResultsList: {
    gap: 8,
  },
  productItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    gap: 12,
  },
  productItemDisabled: {
    opacity: 0.5,
  },
  productImage: {
    width: 40,
    height: 40,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  productImageContent: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  productDetails: {
    flex: 1,
    gap: 4,
  },
  productName: {
    fontSize: 14,
    fontWeight: '500',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  discountedPrice: {
    fontSize: 12,
    fontWeight: '600',
  },
  originalPrice: {
    fontSize: 12,
    textDecorationLine: 'line-through',
  },
  price: {
    fontSize: 12,
    fontWeight: '500',
  },
  checkContainer: {
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
  },
  footerText: {
    fontSize: 12,
  },
  limitText: {
    fontSize: 12,
    fontWeight: '600',
  },
});
