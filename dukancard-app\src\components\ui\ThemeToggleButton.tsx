import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Pressable,
} from 'react-native';
import { Sun, Moon, Monitor, Check } from 'lucide-react-native';
import { useThemeContext, type ThemeMode } from '@/src/contexts/ThemeContext';

interface ThemeToggleButtonProps {
  variant?: 'default' | 'profile';
  showLabel?: boolean;
}

export function ThemeToggleButton({ 
  variant = 'default', 
  showLabel = false 
}: ThemeToggleButtonProps) {
  const { themeMode, setThemeMode, isDark } = useThemeContext();
  const [modalVisible, setModalVisible] = useState(false);

  const themeOptions: {
    mode: ThemeMode;
    label: string;
    icon: React.ReactNode;
    description: string;
  }[] = [
    {
      mode: 'system',
      label: 'System',
      icon: <Monitor size={20} color={isDark ? '#fff' : '#000'} />,
      description: 'Use device setting',
    },
    {
      mode: 'light',
      label: 'Light',
      icon: <Sun size={20} color={isDark ? '#fff' : '#000'} />,
      description: 'Light theme',
    },
    {
      mode: 'dark',
      label: 'Dark',
      icon: <Moon size={20} color={isDark ? '#fff' : '#000'} />,
      description: 'Dark theme',
    },
  ];

  const currentThemeOption = themeOptions.find(option => option.mode === themeMode);

  const handleThemeSelect = (mode: ThemeMode) => {
    setThemeMode(mode);
    setModalVisible(false);
  };

  const buttonStyle = variant === 'profile'
    ? [
        styles.profileButton,
        {
          backgroundColor: isDark ? '#1a1a1a' : '#f8f9fa',
          borderColor: isDark ? '#333' : '#e5e5e5',
        }
      ]
    : styles.defaultButton;

  return (
    <>
      <TouchableOpacity
        style={buttonStyle}
        onPress={() => setModalVisible(true)}
        activeOpacity={0.7}
      >
        {currentThemeOption?.icon}
        {showLabel && (
          <Text style={[styles.buttonText, { color: isDark ? '#fff' : '#000' }]}>
            {currentThemeOption?.label}
          </Text>
        )}
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <Pressable 
          style={styles.modalOverlay}
          onPress={() => setModalVisible(false)}
        >
          <View style={[styles.modalContent, { backgroundColor: isDark ? '#1a1a1a' : '#fff' }]}>
            <Text style={[styles.modalTitle, { color: isDark ? '#fff' : '#000' }]}>
              Choose Theme
            </Text>
            
            {themeOptions.map((option) => (
              <TouchableOpacity
                key={option.mode}
                style={[
                  styles.themeOption,
                  { borderBottomColor: isDark ? '#333' : '#e5e5e5' }
                ]}
                onPress={() => handleThemeSelect(option.mode)}
              >
                <View style={styles.themeOptionLeft}>
                  {option.icon}
                  <View style={styles.themeOptionText}>
                    <Text style={[styles.themeOptionLabel, { color: isDark ? '#fff' : '#000' }]}>
                      {option.label}
                    </Text>
                    <Text style={[styles.themeOptionDescription, { color: isDark ? '#999' : '#666' }]}>
                      {option.description}
                    </Text>
                  </View>
                </View>
                {themeMode === option.mode && (
                  <Check size={20} color="#D4AF37" />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </Pressable>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  defaultButton: {
    padding: 8,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 12,
    borderWidth: 1,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    borderRadius: 12,
    padding: 20,
    width: '100%',
    maxWidth: 300,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  themeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  themeOptionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  themeOptionText: {
    marginLeft: 12,
    flex: 1,
  },
  themeOptionLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  themeOptionDescription: {
    fontSize: 14,
    marginTop: 2,
  },
});
