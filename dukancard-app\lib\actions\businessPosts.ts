/**
 * Business Posts Actions for React Native
 * Handles CRUD operations for business posts
 */

import { supabase } from '@/lib/supabase';
import { checkBusinessProfile } from '@/backend/supabase/services/storage/businessPostImageUploadService';
import { deletePostMedia } from '@/src/utils/deletePostMedia';
import { BusinessPostService } from '@/backend/supabase/services/business/businessPostService';

import { businessPostSchema, BusinessPostFormData } from '@/lib/schemas/postSchemas';

export interface ActionResponse {
  success: boolean;
  message: string;
  error?: string;
  data?: any;
}

/**
 * Create a new business post
 */
export async function createBusinessPost(formData: BusinessPostFormData): Promise<ActionResponse> {
  // Get the current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: 'Authentication required',
      error: 'You must be logged in to create a post'
    };
  }

  try {
    // Validate form data
    try {
      await businessPostSchema.validate(formData, { abortEarly: false });
    } catch (validationError: any) {
      return {
        success: false,
        message: 'Validation failed',
        error: validationError.errors ? validationError.errors.join(', ') : 'Invalid data provided'
      };
    }
    // Check business profile before creating new posts (matching Next.js implementation)
    const hasValidProfile = await checkBusinessProfile();
    if (!hasValidProfile) {
      return {
        success: false,

        message: 'Complete business profile required',
        error: 'Please complete your business profile before creating posts'
      };
    }

    // Get the user's business profile
    const { data: businessProfile, error: profileError } = await BusinessPostService.getBusinessProfileById(user.id);

    if (profileError || !businessProfile) {
      return {
        success: false,
        message: 'Business profile not found',
        error: 'You must have a business profile to create a post'
      };
    }

    // Prepare post data
    const postData = {
      business_id: user.id,
      content: formData.content,
      image_url: formData.image_url || null,
      city_slug: businessProfile.city_slug,
      state_slug: businessProfile.state_slug,
      locality_slug: businessProfile.locality_slug,
      pincode: businessProfile.pincode,
      product_ids: formData.product_ids || [],
      mentioned_business_ids: formData.mentioned_business_ids || [],
      author_avatar: businessProfile.logo_url
    };

    // Insert the post
    const { data, error } = await BusinessPostService.insertBusinessPost(postData);

    if (error) {
      console.error('Error creating business post:', error);
      if (error.message.includes('product limit exceeded')) {
        return {
          success: false,
          message: 'Product limit reached',
          error: 'You have reached the maximum number of products allowed for your plan. Please upgrade your plan to add more products.'
        };
      }
      return {
        success: false,
        message: 'Failed to create post',
        error: error.message
      };
    }

    return {
      success: true,
      message: 'Post created successfully',
      data
    };
  } catch (error) {
    console.error('Error creating business post:', error);
    return {
      success: false,
      message: 'Failed to create post',
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Update an existing business post
 */
export async function updateBusinessPost(postId: string, formData: BusinessPostFormData): Promise<ActionResponse> {
  // Get the current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: 'Authentication required',
      error: 'You must be logged in to update a post'
    };
  }

  // Check if the post exists and belongs to the user
  const { data: existingPost, error: postError } = await BusinessPostService.getBusinessPostByIdAndUserId(postId, user.id);

  if (postError || !existingPost) {
    return {
      success: false,
      message: 'Post not found',
      error: 'The post does not exist or you do not have permission to update it'
    };
  }

  // Prepare update data
  const updateData = {
    content: formData.content,
    image_url: formData.image_url || null,
    product_ids: formData.product_ids || [],
    mentioned_business_ids: formData.mentioned_business_ids || [],
    updated_at: new Date().toISOString()
  };

  // Update the post
  const { data, error } = await BusinessPostService.updateBusinessPost(postId, updateData);

  if (error) {
    console.error('Error updating business post:', error);
    return {
      success: false,
      message: 'Failed to update post',
      error: error.message
    };
  }

  return {
    success: true,
    message: 'Post updated successfully',
    data
  };
}

/**
 * Delete a business post
 */
export async function deleteBusinessPost(postId: string): Promise<ActionResponse> {
  // Get the current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: 'Authentication required',
      error: 'You must be logged in to delete a post'
    };
  }

  // Check if the post exists and belongs to the user, get creation date for media deletion
  const { data: existingPost, error: postError } = await BusinessPostService.getBusinessPostByIdAndUserId(postId, user.id);

  if (postError || !existingPost) {
    return {
      success: false,
      message: 'Post not found',
      error: 'The post does not exist or you do not have permission to delete it'
    };
  }

  // Always attempt to delete the post folder from storage
  // This ensures we clean up any files that might exist, regardless of image_url status
  try {
    const mediaDeleteResult = await deletePostMedia(user.id, postId, existingPost.created_at);
    if (!mediaDeleteResult.success && mediaDeleteResult.error) {
      console.error('Error deleting post media:', mediaDeleteResult.error);
      // Continue with post deletion even if media deletion fails
    }
  } catch (mediaError) {
    console.error('Error deleting post media:', mediaError);
    // Continue with post deletion even if media deletion fails
  }

  // Delete the post
  const { error } = await BusinessPostService.deleteBusinessPost(postId);

  if (error) {
    console.error('Error deleting business post:', error);
    return {
      success: false,
      message: 'Failed to delete post',
      error: error.message
    };
  }

  return {
    success: true,
    message: 'Post deleted successfully'
  };
}

/**
 * Get business posts for a specific business
 */
export async function getBusinessPosts(businessId: string, page: number = 1, limit: number = 10): Promise<ActionResponse> {
  const offset = (page - 1) * limit;

  const { data, error } = await BusinessPostService.getBusinessPosts(businessId, offset, limit);

  if (error) {
    console.error('Error fetching business posts:', error);
    return {
      success: false,
      message: 'Failed to fetch posts',
      error: error.message
    };
  }

  return {
    success: true,
    message: 'Posts fetched successfully',
    data: {
      items: data,
      hasMore: data.length === limit
    }
  };
}

/**
 * Get a single business post by ID
 */
export async function getBusinessPost(postId: string): Promise<ActionResponse> {
  const { data, error } = await BusinessPostService.getSingleBusinessPost(postId);

  if (error) {
    console.error('Error fetching business post:', error);
    return {
      success: false,
      message: 'Failed to fetch post',
      error: error.message
    };
  }

  return {
    success: true,
    message: 'Post fetched successfully',
    data
  };
}

