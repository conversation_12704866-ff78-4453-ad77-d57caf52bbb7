import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
} from 'react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { User, Plus } from 'lucide-react-native';
import { supabase } from '@/lib/supabase';
import { createBusinessPostCreatorStyles } from '@/styles/feed/_business-post-creator-styles';
import { BusinessPostModal } from './BusinessPostModal';

interface BusinessPostCreatorProps {
  businessName?: string;
  onPostCreated?: () => void;
}

export function BusinessPostCreator({
  businessName = 'Business Owner',
  onPostCreated
}: BusinessPostCreatorProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createBusinessPostCreatorStyles();

  // State
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [userAvatar, setUserAvatar] = useState<string | null>(null);
  const [businessDisplayName, setBusinessDisplayName] = useState(businessName);

  // Theme colors
  const backgroundColor = isDark ? '#000000' : '#FFFFFF';
  const textColor = isDark ? '#FFFFFF' : '#000000';
  const mutedTextColor = isDark ? '#A1A1AA' : '#71717A';
  const primaryColor = '#D4AF37';

  // Fetch business profile data
  useEffect(() => {
    const fetchBusinessProfile = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return;

        const { data: profile } = await supabase
          .from('business_profiles')
          .select('business_name, logo_url')
          .eq('id', user.id)
          .single();

        if (profile) {
          setBusinessDisplayName(profile.business_name || businessName);
          setUserAvatar(profile.logo_url);
        }
      } catch (error) {
        console.error('Error fetching business profile:', error);
      }
    };

    fetchBusinessProfile();
  }, [businessName]);

  const handleOpenModal = () => {
    setIsModalVisible(true);
  };

  const handleCloseModal = () => {
    setIsModalVisible(false);
  };

  const handlePostCreated = () => {
    setIsModalVisible(false);
    if (onPostCreated) {
      onPostCreated();
    }
  };

  return (
    <View>
      {/* Collapsed State - Always Visible */}
      <TouchableOpacity
        style={[styles.container, { backgroundColor }]}
        onPress={handleOpenModal}
        activeOpacity={0.7}
      >
        <View style={styles.collapsedContent}>
          {userAvatar ? (
            <Image
              source={{ uri: userAvatar }}
              style={styles.avatar}
            />
          ) : (
            <View style={[styles.avatarPlaceholder, { backgroundColor: primaryColor }]}>
              <User size={20} color="#FFFFFF" />
            </View>
          )}

          <View style={styles.textContainer}>
            <Text style={[styles.placeholderText, { color: mutedTextColor }]}>
              What&apos;s happening at {businessDisplayName}?
            </Text>
          </View>

          <View style={[styles.addButton, { backgroundColor: primaryColor }]}>
            <Plus size={20} color="#FFFFFF" />
          </View>
        </View>
      </TouchableOpacity>

      {/* Full Screen Modal */}
      <BusinessPostModal
        visible={isModalVisible}
        onClose={handleCloseModal}
        businessName={businessDisplayName}
        onPostCreated={handlePostCreated}
      />
    </View>
  );
}
