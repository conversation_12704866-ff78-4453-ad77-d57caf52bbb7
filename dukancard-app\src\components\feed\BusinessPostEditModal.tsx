import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Image,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { ArrowLeft, Image as ImageIcon, X, Package } from 'lucide-react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { Colors } from '@/src/constants/Colors';
import { updateBusinessPost } from '@/lib/actions/businessPosts';
import { uploadBusinessPostImage } from '@/backend/supabase/services/storage/businessPostImageUploadService';
import { supabase } from '@/lib/supabase';
import ImagePickerBottomSheet, { ImagePickerBottomSheetRef } from '@/src/components/pickers/ImagePickerBottomSheet';
import { ProductSelectorModal } from './ProductSelectorModal';
import { getSelectedProducts } from '@/lib/actions/products';
import { createBusinessPostModalStyles } from '@/styles/feed/_business-post-modal-styles';
import { useToast } from '@/src/components/ui/Toast';
import { router } from 'expo-router';

interface BusinessPostEditModalProps {
  visible: boolean;
  onClose: () => void;
  postId: string;
  initialContent: string;
  initialImageUrl?: string | null;
  initialProductIds?: string[];
  businessName?: string;
  onPostUpdated?: (postId: string, newContent: string, newImageUrl?: string | null, newProductIds?: string[]) => void;
}

export function BusinessPostEditModal({
  visible,
  onClose,
  postId,
  initialContent,
  initialImageUrl,
  initialProductIds = [],
  businessName = 'Business Owner',
  onPostUpdated
}: BusinessPostEditModalProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createBusinessPostModalStyles();
  const toast = useToast();

  // State
  const [content, setContent] = useState(initialContent);
  const [imageUri, setImageUri] = useState<string | null>(initialImageUrl || null);
  const [imageDimensions, setImageDimensions] = useState<{ width: number; height: number } | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedProductIds, setSelectedProductIds] = useState<string[]>(initialProductIds);
  const [selectedProducts, setSelectedProducts] = useState<any[]>([]);
  const [userAvatar, setUserAvatar] = useState<string | null>(null);
  const [businessDisplayName, setBusinessDisplayName] = useState(businessName);
  const [showProductSelector, setShowProductSelector] = useState(false);
  const imagePickerRef = React.useRef<ImagePickerBottomSheetRef>(null);

  // Colors
  const backgroundColor = isDark ? Colors.dark.background : Colors.light.background;
  const textColor = isDark ? Colors.dark.text : Colors.light.text;
  const mutedTextColor = isDark ? Colors.dark.tabIconDefault : Colors.light.tabIconDefault;
  const borderColor = isDark ? Colors.dark.border : Colors.light.border;
  const primaryColor = '#D4AF37';

  // Reset state when modal opens
  useEffect(() => {
    if (visible) {
      setContent(initialContent);
      setImageUri(initialImageUrl || null);
      setImageDimensions(null); // Reset dimensions for existing images
      setSelectedProductIds(initialProductIds);
    }
  }, [visible, initialContent, initialImageUrl, initialProductIds]);

  // Fetch business profile data
  useEffect(() => {
    if (!visible) return;

    const fetchBusinessProfile = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return;

        const { data: profile } = await supabase
          .from('business_profiles')
          .select('business_name, logo_url')
          .eq('id', user.id)
          .single();

        if (profile) {
          setBusinessDisplayName(profile.business_name || businessName);
          setUserAvatar(profile.logo_url);
        }
      } catch (error) {
        console.error('Error fetching business profile:', error);
      }
    };

    fetchBusinessProfile();
  }, [visible, businessName]);

  // Load selected products
  useEffect(() => {
    const loadSelectedProducts = async () => {
      if (selectedProductIds.length === 0) {
        setSelectedProducts([]);
        return;
      }

      try {
        const result = await getSelectedProducts(selectedProductIds);
        if (result.success && result.data) {
          // Maintain order of selectedProductIds
          const orderedProducts = selectedProductIds
            .map(id => result.data?.find((product: any) => product.id === id))
            .filter(Boolean);
          setSelectedProducts(orderedProducts);
        }
      } catch (error) {
        console.error('Error loading selected products:', error);
      }
    };

    loadSelectedProducts();
  }, [selectedProductIds]);

  const handleRemoveProduct = (productId: string) => {
    const updatedProductIds = selectedProductIds.filter(id => id !== productId);
    setSelectedProductIds(updatedProductIds);
  };

  const handleClose = () => {
    setContent(initialContent);
    setImageUri(initialImageUrl || null);
    setImageDimensions(null); // Reset dimensions when closing
    setSelectedProductIds(initialProductIds);
    onClose();
  };

  const handleCameraSelection = async () => {
    try {
      const { openCamera } = await import('@/backend/supabase/services/storage/imageUploadService');
      const result = await openCamera({ allowsEditing: false }); // Don't allow editing to preserve original dimensions
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        setImageUri(asset.uri);
        // Store original dimensions
        if (asset.width && asset.height) {
          setImageDimensions({ width: asset.width, height: asset.height });
        }
      }
    } catch (error) {
      console.error('Camera error:', error);
      Alert.alert('Camera Error', 'Failed to take photo. Please try again.');
    }
  };

  const handleGallerySelection = async () => {
    try {
      const { openImageGallery } = await import('@/backend/supabase/services/storage/imageUploadService');
      const result = await openImageGallery({ allowsEditing: false }); // Don't allow editing to preserve original dimensions
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        setImageUri(asset.uri);
        // Store original dimensions
        if (asset.width && asset.height) {
          setImageDimensions({ width: asset.width, height: asset.height });
        }
      }
    } catch (error) {
      console.error('Gallery error:', error);
      Alert.alert('Gallery Error', 'Failed to select image. Please try again.');
    }
  };



  const handleSubmit = async () => {
    if (!content.trim() && !imageUri && selectedProductIds.length === 0) {
      Alert.alert('Error', 'Please add some content, an image, or select products for your post.');
      return;
    }

    setIsSubmitting(true);

    try {
      const finalData = {
        content: content.trim(),
        image_url: imageUri && imageUri.startsWith('http') ? imageUri : null,
        product_ids: selectedProductIds,
      };

      let result;

      if (imageUri && !imageUri.startsWith('http')) {
        // Upload new image
        const uploadResult = await uploadBusinessPostImage(imageUri, postId, new Date().toISOString());
        if (uploadResult.success && uploadResult.url) {
          finalData.image_url = uploadResult.url;
        } else {
          Alert.alert('Warning', 'Post will be updated but image upload failed.');
        }
      }

      result = await updateBusinessPost(postId, finalData);

      if (result.success) {
        // Show success toast
        toast.success('Post updated successfully!');

        // Close modal and navigate back to previous screen
        handleClose();
        if (onPostUpdated) {
          onPostUpdated(postId, finalData.content, finalData.image_url, finalData.product_ids);
        }

        // Navigate back to previous screen (feed or single post)
        if (router.canGoBack()) {
          router.back();
        }
      } else {
        Alert.alert(
          'Error',
          result.message || result.error || 'Failed to update post',
          [
            {
              text: 'Try Again',
              style: 'default'
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error updating post:', error);
      Alert.alert('Error', 'Failed to update post. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!visible) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={handleClose}
    >
      <StatusBar style={isDark ? "light" : "dark"} backgroundColor={backgroundColor} />
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: borderColor }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleClose}
            disabled={isSubmitting}
          >
            <ArrowLeft color={textColor} size={24} />
          </TouchableOpacity>

          <Text style={[styles.headerTitle, { color: textColor }]}>Edit Post</Text>

          <TouchableOpacity
            style={[
              styles.postButton,
              {
                backgroundColor: content.trim() || imageUri || selectedProductIds.length > 0 ? primaryColor : borderColor,
              }
            ]}
            onPress={handleSubmit}
            disabled={(!content.trim() && !imageUri && selectedProductIds.length === 0) || isSubmitting}
          >
            <Text style={styles.postButtonText}>
              {isSubmitting ? 'Updating...' : 'Update'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Content */}
        <KeyboardAvoidingView
          style={styles.content}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
            {/* User Info */}
            <View style={styles.userInfo}>
              {userAvatar ? (
                <Image
                  source={{ uri: userAvatar }}
                  style={styles.avatar}
                />
              ) : (
                <View style={[styles.avatarPlaceholder, { backgroundColor: primaryColor }]}>
                  <Text style={styles.avatarText}>
                    {businessDisplayName.charAt(0).toUpperCase()}
                  </Text>
                </View>
              )}
              <Text style={[styles.userName, { color: textColor }]}>
                {businessDisplayName}
              </Text>
            </View>

            {/* Text Input with Character Counter */}
            <View style={styles.textInputContainer}>
              <TextInput
                style={[styles.textInput, { color: textColor, borderColor }]}
                placeholder={`What's happening at ${businessDisplayName}?`}
                placeholderTextColor={mutedTextColor}
                value={content}
                onChangeText={setContent}
                multiline
                textAlignVertical="top"
                autoFocus
                maxLength={2000}
              />
              <View style={styles.characterCounter}>
                <Text style={[
                  styles.characterCountText,
                  {
                    color: content.length > 2000 ? '#EF4444' : mutedTextColor
                  }
                ]}>
                  {content.length}/2000
                </Text>
              </View>
            </View>

            {/* Image Preview */}
            {imageUri && (
              <View style={styles.imageContainer}>
                <Image
                  source={{ uri: imageUri }}
                  style={[
                    styles.imagePreview,
                    imageDimensions && {
                      aspectRatio: imageDimensions.width / imageDimensions.height
                    }
                  ]}
                />
              </View>
            )}

            {/* Selected Products Display (if any) */}
            {selectedProducts.length > 0 && (
              <View style={styles.selectedProductsContainer}>
                <Text style={[styles.selectedProductsHeader, { color: textColor }]}>
                  Selected Products ({selectedProducts.length})
                </Text>
                {selectedProducts.map((product) => (
                  <View key={product.id} style={[styles.selectedProductItem, { borderColor }]}>
                    {product.image_url && (
                      <Image
                        source={{ uri: product.image_url }}
                        style={styles.selectedProductImage}
                      />
                    )}
                    <View style={styles.selectedProductDetails}>
                      <Text style={[styles.selectedProductName, { color: textColor }]} numberOfLines={1}>
                        {product.name}
                      </Text>
                      <View style={styles.selectedProductPriceContainer}>
                        {product.discounted_price ? (
                          <>
                            <Text style={[styles.selectedProductDiscountedPrice, { color: primaryColor }]}>
                              ₹{product.discounted_price.toLocaleString('en-IN')}
                            </Text>
                            <Text style={[styles.selectedProductOriginalPrice, { color: mutedTextColor }]}>
                              ₹{product.base_price?.toLocaleString('en-IN')}
                            </Text>
                          </>
                        ) : (
                          <Text style={[styles.selectedProductPrice, { color: textColor }]}>
                            ₹{product.base_price?.toLocaleString('en-IN') || 'N/A'}
                          </Text>
                        )}
                      </View>
                    </View>
                    <TouchableOpacity
                      style={styles.removeProductButton}
                      onPress={() => handleRemoveProduct(product.id)}
                    >
                      <X color="#EF4444" size={16} />
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            )}
          </ScrollView>

          {/* Bottom Actions */}
          <View style={[styles.bottomActions, { borderTopColor: borderColor }]}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => imagePickerRef.current?.present()}
            >
              <ImageIcon color={primaryColor} size={24} />
              <Text style={[styles.actionButtonText, { color: textColor }]}>
                {imageUri ? 'Change Photo' : 'Add Photo'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => setShowProductSelector(true)}
            >
              <Package color={primaryColor} size={24} />
              <Text style={[styles.actionButtonText, { color: textColor }]}>
                {selectedProductIds.length > 0
                  ? `Products (${selectedProductIds.length})`
                  : 'Products'
                }
              </Text>
            </TouchableOpacity>
          </View>
        </KeyboardAvoidingView>

        {/* Image Picker Bottom Sheet */}
        <ImagePickerBottomSheet
          ref={imagePickerRef}
          onCameraPress={handleCameraSelection}
          onGalleryPress={handleGallerySelection}
          title="Update Photo"
          cameraLabel="Take Photo"
          galleryLabel="Choose from Gallery"
        />

        {/* Product Selector Modal */}
        <ProductSelectorModal
          visible={showProductSelector}
          selectedProductIds={selectedProductIds}
          onProductsChange={setSelectedProductIds}
          onClose={() => setShowProductSelector(false)}
        />
      </SafeAreaView>
    </Modal>
  );
}
