  /**
   * Business Onboarding Service for React Native
   * 
   * Handles business onboarding operations using direct Supabase client calls
   * with Row Level Security (RLS) policies for security.
   * 
   * This service replaces the need for Next.js proxy API routes by leveraging:
   * - RLS policies for security (users can only create their own profiles)
   * - Direct Supabase client calls for better performance
   * - Production-ready business onboarding logic
   */
  
  import { supabase } from '@/lib/supabase';
  import { SUBSCRIPTION_STATUS } from '@/lib/constants/subscription';
  
  // Types for business onboarding operations
  export interface BusinessOnboardingData {
    // Step 1: Business Details
    businessName: string;
    email: string;
  
    // Step 2: Card Information
    memberName: string;
    title: string;
    phone: string;
    businessCategory: string;
    businessSlug: string;
  
    // Step 3: Address Information
    addressLine: string;
    pincode: string;
    city: string;
    state: string;
    locality: string;
    latitude?: number;
    longitude?: number;
    businessStatus: 'online' | 'offline';
  
    // Step 4: Plan Selection
    planId: string;
    isTrial: boolean;
  }
  
  export interface BusinessProfile {
    id: string;
    business_name: string;
    business_slug: string;
    contact_email: string;
    member_name: string;
    title: string;
    phone: string;
    business_category: string;
    address_line: string;
    pincode: string;
    city: string;
    state: string;
    locality: string;
    status: 'online' | 'offline';
    logo_url?: string;
    created_at: string;
    updated_at: string;
  }
  
  export interface ServiceResult<T = any> {
    success: boolean;
    data?: T;
    error?: string;
  }
  
  /**
   * Create business profile and subscription atomically using RPC
   * @param businessData The business profile data
   * @param subscriptionData The subscription data
   * @returns ServiceResult
   */
  async function createBusinessProfileAtomic(
    businessData: any,
    subscriptionData: any
  ): Promise<ServiceResult> {
    try {
      const { data: result, error } = await supabase.rpc('create_business_profile_atomic', {
        p_business_data: businessData,
        p_subscription_data: subscriptionData
      });

      if (error) {
        console.error('[BUSINESS_ONBOARDING_SERVICE] RPC error:', error);
        return { success: false, error: 'Failed to create business profile atomically' };
      }

      if (!result?.success) {
        console.error('[BUSINESS_ONBOARDING_SERVICE] RPC returned error:', result?.error);
        return { success: false, error: result?.error || 'Failed to create business profile' };
      }

      return { success: true, data: result.business_profile };
    } catch (error) {
      console.error('[BUSINESS_ONBOARDING_SERVICE] Unexpected error in createBusinessProfileAtomic:', error);
      return { success: false, error: 'An unexpected error occurred during business creation' };
    }
  }
  
  /**
   * Complete business onboarding process
   * This replaces the /api/business/onboarding route
   */
  export async function completeBusinessOnboarding(
    onboardingData: BusinessOnboardingData
  ): Promise<ServiceResult<BusinessProfile>> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        return { success: false, error: 'Authentication required' };
      }
  
      // Validate required fields
      const requiredFields = [
        'businessName', 'email', 'memberName', 'title', 'phone',
        'businessCategory', 'businessSlug', 'addressLine', 'pincode',
        'city', 'state', 'locality', 'businessStatus', 'planId'
      ];
  
      for (const field of requiredFields) {
        if (!onboardingData[field as keyof BusinessOnboardingData]) {
          return { success: false, error: `Missing required field: ${field}` };
        }
      }
  
      // Validate business slug format
      const slugRegex = /^[a-z0-9-]+$/;
      if (!slugRegex.test(onboardingData.businessSlug)) {
        return { success: false, error: 'Business slug must contain only lowercase letters, numbers, and hyphens' };
      }
  
      // Check if business slug is available
      const { data: existingBusiness, error: slugCheckError } = await supabase
        .from('business_profiles')
        .select('id')
        .eq('business_slug', onboardingData.businessSlug)
        .maybeSingle();
  
      if (slugCheckError) {
        console.error('[BUSINESS_ONBOARDING_SERVICE] Error checking slug availability:', slugCheckError);
        return { success: false, error: 'Failed to check business slug availability' };
      }
  
      if (existingBusiness) {
        return { success: false, error: 'Business slug is already taken' };
      }
  
      // Validate plan exists (you might want to add this check)
      // const planExists = await validatePlan(onboardingData.planId);
      // if (!planExists) {
      //   return { success: false, error: 'Invalid plan selected' };
      // }
  
      // Calculate trial end date for paid plans (30 days from now)
      // FIXED: Calculate trial end date (30 days from now using milliseconds)
      // This approach avoids month boundary issues that can occur with setDate()
      let trialEndDate: Date | null = null;
      if (onboardingData.planId !== "free") {
        trialEndDate = new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)); // 30 days in milliseconds
      }

      // Prepare business profile data
      const businessProfileData = {
        id: user.id,
        business_name: onboardingData.businessName,
        business_slug: onboardingData.businessSlug,
        contact_email: onboardingData.email,
        has_active_subscription: onboardingData.planId === "free", // Use same logic as Next.js: Free plan is active, paid plans start as trial
        member_name: onboardingData.memberName,
        title: onboardingData.title,
        phone: onboardingData.phone,
        business_category: onboardingData.businessCategory,
        address_line: onboardingData.addressLine,
        pincode: onboardingData.pincode,
        city: onboardingData.city,
        state: onboardingData.state,
        locality: onboardingData.locality,
        status: onboardingData.businessStatus,
        latitude: onboardingData.latitude || null, // React Native can use precise GPS coordinates
        longitude: onboardingData.longitude || null, // React Native can use precise GPS coordinates
        ...(trialEndDate && { trial_end_date: trialEndDate.toISOString() }) // Only add trial_end_date for paid plans
      };

      // Prepare subscription data
      const subscriptionData = {
        plan_id: onboardingData.planId,
        plan_cycle: "monthly", // Always monthly on onboarding
        subscription_status: onboardingData.planId === "free" ? SUBSCRIPTION_STATUS.ACTIVE : SUBSCRIPTION_STATUS.TRIAL,
        ...(onboardingData.planId === "free" && { subscription_start_date: new Date().toISOString() }) // For free plan, set start date
      };

      // Create business profile and subscription atomically using RPC
      const result = await createBusinessProfileAtomic(businessProfileData, subscriptionData);

      if (!result.success) {
        return { success: false, error: result.error };
      }

      return { success: true, data: result.data };
    } catch (error) {
      console.error('[BUSINESS_ONBOARDING_SERVICE] Unexpected error:', error);
      return { success: false, error: 'An unexpected error occurred during onboarding' };
    }
  }
  
  /**
   * Check if business slug is available
   */
  export async function checkBusinessSlugAvailability(
    slug: string
  ): Promise<ServiceResult<boolean>> {
    try {
      // Validate slug format
      const slugRegex = /^[a-z0-9-]+$/;
      if (!slugRegex.test(slug)) {
        return { success: false, error: 'Business slug must contain only lowercase letters, numbers, and hyphens' };
      }
  
      // Check if slug is available
      const { data: existingBusiness, error } = await supabase
        .from('business_profiles')
        .select('id')
        .eq('business_slug', slug)
        .maybeSingle();
  
      if (error) {
        console.error('[BUSINESS_ONBOARDING_SERVICE] Error checking slug availability:', error);
        return { success: false, error: 'Failed to check slug availability' };
      }
  
      return { success: true, data: !existingBusiness };
    } catch (error) {
      console.error('[BUSINESS_ONBOARDING_SERVICE] Unexpected error:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }
  
  /**
   * Get existing business data for pre-filling forms
   */
  export async function getExistingBusinessData(): Promise<ServiceResult<Partial<BusinessOnboardingData> | null>> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        return { success: false, error: 'Authentication required' };
      }
  
      const { data: profile, error } = await supabase
        .from('business_profiles')
        .select(`
          business_name,
          contact_email,
          member_name,
          title,
          phone,
          business_category,
          business_slug,
          address_line,
          pincode,
          city,
          state,
          locality,
          status,
          latitude,
          longitude
        `)
        .eq('id', user.id)
        .maybeSingle();
  
      if (error) {
        console.error('[BUSINESS_ONBOARDING_SERVICE] Error fetching existing data:', error);
        return { success: false, error: 'Failed to fetch existing business data' };
      }
  
      if (!profile) {
        return { success: true, data: null };
      }
  
      // Map database fields to onboarding data format
      const onboardingData: Partial<BusinessOnboardingData> = {
        businessName: profile.business_name,
        email: profile.contact_email,
        memberName: profile.member_name,
        title: profile.title,
        phone: profile.phone,
        businessCategory: profile.business_category,
        businessSlug: profile.business_slug,
        addressLine: profile.address_line,
        pincode: profile.pincode,
        city: profile.city,
        state: profile.state,
        locality: profile.locality,
        businessStatus: profile.status,
        latitude: profile.latitude || undefined,
        longitude: profile.longitude || undefined,
      };
  
      return { success: true, data: onboardingData };
    } catch (error) {
      console.error('[BUSINESS_ONBOARDING_SERVICE] Unexpected error:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }
  
  /**
   * Update business profile during onboarding
   */
  export async function updateBusinessProfile(
    updates: Partial<BusinessOnboardingData>
  ): Promise<ServiceResult<BusinessProfile>> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        return { success: false, error: 'Authentication required' };
      }
  
      // Map onboarding data to database fields
      const profileUpdates: any = {};
      
      if (updates.businessName) profileUpdates.business_name = updates.businessName;
      if (updates.email) profileUpdates.contact_email = updates.email;
      if (updates.memberName) profileUpdates.member_name = updates.memberName;
      if (updates.title) profileUpdates.title = updates.title;
      if (updates.phone) profileUpdates.phone = updates.phone;
      if (updates.businessCategory) profileUpdates.business_category = updates.businessCategory;
      if (updates.businessSlug) profileUpdates.business_slug = updates.businessSlug;
      if (updates.addressLine) profileUpdates.address_line = updates.addressLine;
      if (updates.pincode) profileUpdates.pincode = updates.pincode;
      if (updates.city) profileUpdates.city = updates.city;
      if (updates.state) profileUpdates.state = updates.state;
      if (updates.locality) profileUpdates.locality = updates.locality;
      if (updates.businessStatus) profileUpdates.status = updates.businessStatus;
      if (updates.latitude !== undefined) profileUpdates.latitude = updates.latitude;
      if (updates.longitude !== undefined) profileUpdates.longitude = updates.longitude;
  
      // Add updated timestamp
      profileUpdates.updated_at = new Date().toISOString();
  
      // Update business profile (RLS policy ensures user can only update their own profile)
      const { data: profile, error } = await supabase
        .from('business_profiles')
        .update(profileUpdates)
        .eq('id', user.id)
        .select()
        .single();
  
      if (error) {
        console.error('[BUSINESS_ONBOARDING_SERVICE] Error updating business profile:', error);
        return { success: false, error: 'Failed to update business profile' };
      }
  
      return { success: true, data: profile };
    } catch (error) {
      console.error('[BUSINESS_ONBOARDING_SERVICE] Unexpected error:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }
  
  /**
   * Validate business onboarding data
   */
  export function validateBusinessOnboardingData(
    data: Partial<BusinessOnboardingData>
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
  
    // Validate business name
    if (!data.businessName || data.businessName.trim().length < 2) {
      errors.push('Business name must be at least 2 characters long');
    }
  
    // Validate email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!data.email || !emailRegex.test(data.email)) {
      errors.push('Please enter a valid email address');
    }
  
    // Validate phone
    const phoneRegex = /^\d{10}$/;
    if (!data.phone || !phoneRegex.test(data.phone)) {
      errors.push('Please enter a valid 10-digit mobile number');
    }
  
    // Validate business slug
    if (data.businessSlug) {
      const slugRegex = /^[a-z0-9-]+$/;
      if (!slugRegex.test(data.businessSlug)) {
        errors.push('Business slug must contain only lowercase letters, numbers, and hyphens');
      }
      if (data.businessSlug.length < 3) {
        errors.push('Business slug must be at least 3 characters long');
      }
    }
  
    // Validate pincode
    const pincodeRegex = /^\d{6}$/;
    if (data.pincode && !pincodeRegex.test(data.pincode)) {
      errors.push('Please enter a valid 6-digit pincode');
    }
  
    return {
      isValid: errors.length === 0,
      errors,
    };
  }
