import React, { createContext, useContext, useState, useEffect, useRef, useCallback, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { activityService, ActivityData } from '@/backend/supabase/services/activities/activityService';
import { realtimeService } from '@/backend/supabase/services/realtime/realtimeService';

import { AppState } from 'react-native';

interface NotificationContextType {
  notifications: ActivityData[];
  unreadCount: number;
  loading: boolean;
  refreshing: boolean;
  hasMore: boolean;
  isModalVisible: boolean;
  showModal: () => void;
  hideModal: () => void;
  refreshNotifications: () => Promise<void>;
  loadMoreNotifications: () => Promise<void>;
  markAsRead: (activityId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);



interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const { user, profileStatus } = useAuth();
  const userType = profileStatus.roleStatus?.role;
  const [notifications, setNotifications] = useState<ActivityData[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [page, setPage] = useState(1);
  
  const intervalRef = useRef<number | null>(null);
  const isMountedRef = useRef(true);

  // Function declarations first

  const refreshNotifications = useCallback(async () => {
    if (!user?.id || userType !== 'business') return;

    try {
      setRefreshing(true);
      setPage(1);

      // Fetch only 10 notifications for periodic updates (battery optimized)
      const response = await activityService.getBusinessActivities(
        user.id,
        1,
        10,
        'all'
      );

      if (response.success && response.data) {
        const newNotifications = response.data || [];
        setNotifications(newNotifications);
        setHasMore(newNotifications.length === 10);

        // Get unread count
        const unreadResponse = await activityService.getUnreadActivitiesCount(user.id);
        const newUnreadCount = unreadResponse.count || 0;
        setUnreadCount(newUnreadCount);

        // No caching for frequent periodic updates
      }
    } catch (error) {
      console.error('Error refreshing notifications:', error);
    } finally {
      setRefreshing(false);
    }
  }, [user?.id, userType]);

  const setupPeriodicFetching = useCallback(() => {
    if (!user?.id || intervalRef.current) return;

    // Set up periodic fetching every 2 minutes when app is active (battery optimized)
    intervalRef.current = setInterval(() => {
      if (!isMountedRef.current) return;
      refreshNotifications();
    }, 120000); // 2 minutes interval for better battery life
  }, [user?.id, refreshNotifications]);



  const cleanupPeriodicFetching = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  // Load notifications on mount (first time with skeleton loader)
  useEffect(() => {
    if (user?.id && userType === 'business') {
      setLoading(true);
      refreshNotifications().finally(() => setLoading(false));
    }
  }, [user?.id, userType, refreshNotifications]);

  // Set up periodic fetching when app is active (for business users only)
  useEffect(() => {
    if (!user?.id || userType !== 'business') return;

    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'active') {
        setupPeriodicFetching();
      } else {
        cleanupPeriodicFetching();
      }
    };

    // Set up initial periodic fetching if app is active
    if (AppState.currentState === 'active') {
      setupPeriodicFetching();
    }

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription?.remove();
      cleanupPeriodicFetching();
    };
  }, [user?.id, userType, setupPeriodicFetching]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      cleanupPeriodicFetching();
    };
  }, []);

  const loadMoreNotifications = async () => {
    // Only allow infinite scroll when modal is open to avoid unnecessary database calls
    if (!user?.id || userType !== 'business' || loading || !hasMore || !isModalVisible) return;

    try {
      setLoading(true);
      const nextPage = page + 1;

      const response = await activityService.getBusinessActivities(
        user.id,
        nextPage,
        20,
        'all'
      );

      if (response.success && response.data) {
        const newNotifications = response.data || [];
        setNotifications(prev => [...prev, ...newNotifications]);
        setHasMore(newNotifications.length === 20);
        setPage(nextPage);
      } else {
        // No more data available or error occurred
        setHasMore(false);
      }
    } catch (error) {
      console.error('Error loading more notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (activityId: string) => {
    if (!user?.id) return;

    try {
      await activityService.markActivitiesAsRead(user.id, [activityId]);
      
      // Update local state
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === activityId 
            ? { ...notification, is_read: true }
            : notification
        )
      );
      
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    if (!user?.id) return;

    try {
      await activityService.markActivitiesAsRead(user.id, 'all');
      
      // Update local state
      setNotifications(prev => 
        prev.map(notification => ({ ...notification, is_read: true }))
      );
      
      setUnreadCount(0);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  const showModal = async () => {
    setIsModalVisible(true);
    // Don't mark as read immediately - let user view all unread notifications first
  };

  const hideModal = () => {
    setIsModalVisible(false);
  };

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    loading,
    refreshing,
    hasMore,
    isModalVisible,
    showModal,
    hideModal,
    refreshNotifications,
    loadMoreNotifications,
    markAsRead,
    markAllAsRead,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};
