/**
 * Customer address validation utility for React Native
 * Based on dukancard/lib/utils/addressValidation.ts
 * Checks if customer has complete address information
 */

export interface CustomerAddressData {
  pincode?: string | null;
  state?: string | null;
  city?: string | null;
  locality?: string | null;
  // address is optional as per requirements
  address?: string | null;
}

/**
 * Validates if customer address is complete
 * Address field is optional, but pincode, state, city, and locality are required
 */
export function isCustomerAddressComplete(addressData: CustomerAddressData): boolean {
  const { pincode, state, city, locality } = addressData;

  // Check if required fields are present and not empty
  return !!(
    pincode && pincode.trim() !== '' &&
    state && state.trim() !== '' &&
    city && city.trim() !== '' &&
    locality && locality.trim() !== ''
  );
}

/**
 * Gets missing address fields for customer
 */
export function getMissingAddressFields(addressData: CustomerAddressData): string[] {
  const missing: string[] = [];

  if (!addressData.pincode || addressData.pincode.trim() === '') {
    missing.push('pincode');
  }
  if (!addressData.state || addressData.state.trim() === '') {
    missing.push('state');
  }
  if (!addressData.city || addressData.city.trim() === '') {
    missing.push('city');
  }
  if (!addressData.locality || addressData.locality.trim() === '') {
    missing.push('locality');
  }

  return missing;
}

/**
 * Generates a user-friendly message for missing address fields
 */
export function getAddressValidationMessage(missingFields: string[]): string {
  if (missingFields.length === 0) {
    return '';
  }

  const fieldNames = missingFields.map(field => {
    switch (field) {
      case 'pincode': return 'Pincode';
      case 'state': return 'State';
      case 'city': return 'City';
      case 'locality': return 'Locality';
      default: return field;
    }
  });

  if (fieldNames.length === 1) {
    return `Please update your ${fieldNames[0]} in your profile.`;
  } else if (fieldNames.length === 2) {
    return `Please update your ${fieldNames.join(' and ')} in your profile.`;
  } else {
    const lastField = fieldNames.pop();
    return `Please update your ${fieldNames.join(', ')}, and ${lastField} in your profile.`;
  }
}

/**
 * Validates pincode format (6 digits)
 */
export function isValidPincode(pincode: string): boolean {
  return /^\d{6}$/.test(pincode);
}

/**
 * Validates if all required address fields are present and valid
 */
export function validateAddressFields(addressData: CustomerAddressData): {
  isValid: boolean;
  errors: Record<string, string>;
} {
  const errors: Record<string, string> = {};

  if (!addressData.pincode || addressData.pincode.trim() === '') {
    errors.pincode = 'Pincode is required';
  } else if (!isValidPincode(addressData.pincode)) {
    errors.pincode = 'Please enter a valid 6-digit pincode';
  }

  if (!addressData.state || addressData.state.trim() === '') {
    errors.state = 'State is required';
  }

  if (!addressData.city || addressData.city.trim() === '') {
    errors.city = 'City is required';
  }

  if (!addressData.locality || addressData.locality.trim() === '') {
    errors.locality = 'Locality is required';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

/**
 * Clean and format address data
 */
export function cleanAddressData(addressData: CustomerAddressData): CustomerAddressData {
  return {
    pincode: addressData.pincode?.trim() || null,
    state: addressData.state?.trim() || null,
    city: addressData.city?.trim() || null,
    locality: addressData.locality?.trim() || null,
    address: addressData.address?.trim() || null,
  };
}

/**
 * Check if address data has any values
 */
export function hasAnyAddressData(addressData: CustomerAddressData): boolean {
  return !!(
    addressData.pincode ||
    addressData.state ||
    addressData.city ||
    addressData.locality ||
    addressData.address
  );
}

/**
 * Get completion percentage of address data
 */
export function getAddressCompletionPercentage(addressData: CustomerAddressData): number {
  const requiredFields = ['pincode', 'state', 'city', 'locality'];
  const completedFields = requiredFields.filter(field => {
    const value = addressData[field as keyof CustomerAddressData];
    return value && value.trim() !== '';
  });

  return Math.round((completedFields.length / requiredFields.length) * 100);
}

/**
 * Format address for display
 */
export function formatAddressForDisplay(addressData: CustomerAddressData): string {
  const parts: string[] = [];

  if (addressData.address && addressData.address.trim()) {
    parts.push(addressData.address.trim());
  }

  if (addressData.locality && addressData.locality.trim()) {
    parts.push(addressData.locality.trim());
  }

  if (addressData.city && addressData.city.trim()) {
    parts.push(addressData.city.trim());
  }

  if (addressData.state && addressData.state.trim()) {
    parts.push(addressData.state.trim());
  }

  if (addressData.pincode && addressData.pincode.trim()) {
    parts.push(addressData.pincode.trim());
  }

  return parts.join(', ');
}
