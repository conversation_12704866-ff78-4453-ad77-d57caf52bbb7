import React, { useState } from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  View,
} from 'react-native';
import { RefreshCw } from 'lucide-react-native';

interface RetryButtonProps {
  onRetry: () => Promise<void> | void;
  text?: string;
  disabled?: boolean;
  style?: any;
  textStyle?: any;
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  showIcon?: boolean;
}

export const RetryButton: React.FC<RetryButtonProps> = ({
  onRetry,
  text = 'Retry',
  disabled = false,
  style,
  textStyle,
  variant = 'primary',
  size = 'medium',
  showIcon = true,
}) => {
  const [isRetrying, setIsRetrying] = useState(false);

  const handleRetry = async () => {
    if (isRetrying || disabled) return;

    try {
      setIsRetrying(true);
      await onRetry();
    } catch (error) {
      console.error('Retry failed:', error);
    } finally {
      setIsRetrying(false);
    }
  };

  const getButtonStyle = () => {
    const baseStyle: any[] = [styles.button, styles[size]];

    switch (variant) {
      case 'primary':
        baseStyle.push(styles.primary);
        break;
      case 'secondary':
        baseStyle.push(styles.secondary);
        break;
      case 'ghost':
        baseStyle.push(styles.ghost);
        break;
    }

    if (disabled || isRetrying) {
      baseStyle.push(styles.disabled);
    }

    return baseStyle;
  };

  const getTextStyle = () => {
    const baseStyle: any[] = [styles.text, styles[`${size}Text` as keyof typeof styles]];

    switch (variant) {
      case 'primary':
        baseStyle.push(styles.primaryText);
        break;
      case 'secondary':
        baseStyle.push(styles.secondaryText);
        break;
      case 'ghost':
        baseStyle.push(styles.ghostText);
        break;
    }

    if (disabled || isRetrying) {
      baseStyle.push(styles.disabledText);
    }

    return baseStyle;
  };

  const getIconSize = () => {
    switch (size) {
      case 'small':
        return 16;
      case 'large':
        return 24;
      default:
        return 20;
    }
  };

  const getIconColor = () => {
    if (disabled || isRetrying) {
      return '#9CA3AF';
    }

    switch (variant) {
      case 'primary':
        return '#FFFFFF';
      case 'secondary':
        return '#6B7280';
      case 'ghost':
        return '#D4AF37';
      default:
        return '#FFFFFF';
    }
  };

  return (
    <TouchableOpacity
      style={[...getButtonStyle(), style]}
      onPress={handleRetry}
      disabled={disabled || isRetrying}
      activeOpacity={0.8}
    >
      <View style={styles.content}>
        {isRetrying ? (
          <ActivityIndicator 
            size="small" 
            color={getIconColor()} 
            style={showIcon ? styles.iconSpacing : undefined}
          />
        ) : (
          showIcon && (
            <RefreshCw 
              color={getIconColor()} 
              size={getIconSize()} 
              style={styles.iconSpacing}
            />
          )
        )}
        <Text style={[...getTextStyle(), textStyle]}>
          {isRetrying ? 'Retrying...' : text}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconSpacing: {
    marginRight: 8,
  },
  
  // Sizes
  small: {
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  medium: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  large: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },

  // Variants
  primary: {
    backgroundColor: '#D4AF37',
  },
  secondary: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  ghost: {
    backgroundColor: 'transparent',
  },

  // Disabled state
  disabled: {
    opacity: 0.6,
  },

  // Text styles
  text: {
    fontWeight: '600',
  },
  smallText: {
    fontSize: 14,
  },
  mediumText: {
    fontSize: 16,
  },
  largeText: {
    fontSize: 18,
  },

  // Text variants
  primaryText: {
    color: '#FFFFFF',
  },
  secondaryText: {
    color: '#6B7280',
  },
  ghostText: {
    color: '#D4AF37',
  },
  disabledText: {
    color: '#9CA3AF',
  },
});
