/**
 * Supabase Error Handler
 * Utilities for handling Supabase errors
 */

export interface SupabaseError {
  message: string;
  details?: string;
  hint?: string;
  code?: string;
}

export function handleSupabaseError(error: any): string {
  if (!error) return 'An unknown error occurred';
  
  if (typeof error === 'string') return error;
  
  if (error.message) return error.message;
  
  if (error.error_description) return error.error_description;
  
  return 'An unexpected error occurred';
}

export function isAuthError(error: any): boolean {
  return error?.message?.includes('auth') || error?.code?.startsWith('auth');
}

export function isDatabaseError(error: any): boolean {
  return error?.code?.startsWith('23') || error?.code?.startsWith('42');
}
