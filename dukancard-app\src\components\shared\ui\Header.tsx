import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { useColorScheme } from "@/src/hooks/useColorScheme";
import { NotificationIcon } from "@/src/components/shared/NotificationIcon";
import { useNotifications } from "@/src/contexts/NotificationContext";

interface HeaderProps {
  userName?: string | null;
  businessName?: string | null;
  avatarUrl?: string | null;
  showNotifications?: boolean;
  onProfilePress?: () => void;
  onNotificationPress?: () => void; // Optional override for notification press
}

export const Header: React.FC<HeaderProps> = ({
  showNotifications = true,
  onNotificationPress,
}) => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === "dark";

  const goldColor = "#D4AF37";
  const backgroundColor = isDark ? "#000000" : "#FFFFFF";
  const textColor = isDark ? "#FFFFFF" : "#000000";
  const iconColor = isDark ? "#9CA3AF" : "#6B7280";

  // Use NotificationContext for modal control
  let showModal: (() => void) | undefined;
  try {
    const notifications = useNotifications();
    showModal = notifications.showModal;
  } catch (error) {
    // NotificationContext not available (e.g., for customer users)
    showModal = undefined;
  }

  const handleNotificationPress = () => {
    if (onNotificationPress) {
      onNotificationPress();
    } else if (showModal) {
      showModal();
    }
  };

  return (
    <View style={[styles.container, { backgroundColor }]}>
      {/* Left Section - Logo */}
      <View style={styles.leftSection}>
        <Text style={[styles.logoText, { color: goldColor }]}>
          Dukan
          <Text style={[styles.logoSecondary, { color: textColor }]}>card</Text>
        </Text>
      </View>

      {/* Right Section - Notifications Only */}
      <View style={styles.rightSection}>
        {/* Unified Notifications Icon */}
        {showNotifications && (
          <NotificationIcon
            size={24}
            color={iconColor}
            onPress={handleNotificationPress}
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 4, // Further reduced to 4px for minimal padding
    minHeight: 44, // Minimum touch target height
  },
  leftSection: {
    flex: 1,
  },
  logoText: {
    fontSize: 18, // Reduced from 20 to 18 for more compact header
    fontWeight: "bold",
  },
  logoSecondary: {
    fontWeight: "bold",
  },
  tagline: {
    fontSize: 10,
    marginTop: -2,
    opacity: 0.8,
  },
  rightSection: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  iconButton: {
    padding: 8,
  },
  notificationContainer: {
    position: "relative",
  },
  badge: {
    position: "absolute",
    top: -4,
    right: -4,
    minWidth: 16,
    height: 16,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 4,
  },
  badgeText: {
    color: "#FFFFFF",
    fontSize: 10,
    fontWeight: "600",
  },
  profileButton: {
    padding: 4,
  },
  avatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
  },
  avatarText: {
    fontSize: 14,
    fontWeight: "600",
  },
});

export default Header;
