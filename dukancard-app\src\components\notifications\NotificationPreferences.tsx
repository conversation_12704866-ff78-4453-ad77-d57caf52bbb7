import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Switch,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Bell, Heart, MessageCircle, Users, Star, Settings } from 'lucide-react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { useAuth } from '@/src/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import { LoadingSpinner } from '@/src/components/shared/ui/LoadingSpinner';

interface NotificationPreference {
  id: string;
  user_id: string;
  likes_enabled: boolean;
  comments_enabled: boolean;
  subscriptions_enabled: boolean;
  reviews_enabled: boolean;
  business_updates_enabled: boolean;
  email_notifications_enabled: boolean;
  created_at: string;
  updated_at: string;
}

interface PreferenceItemProps {
  icon: React.ComponentType<any>;
  title: string;
  description: string;
  value: boolean;
  onValueChange: (value: boolean) => void;
  iconColor: string;
}

const PreferenceItem: React.FC<PreferenceItemProps> = ({
  icon: Icon,
  title,
  description,
  value,
  onValueChange,
  iconColor,
}) => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const textColor = isDark ? '#FFFFFF' : '#000000';
  const subtitleColor = isDark ? '#9CA3AF' : '#6B7280';
  const borderColor = isDark ? '#374151' : '#E5E7EB';

  return (
    <View style={[styles.preferenceItem, { borderBottomColor: borderColor }]}>
      <View style={styles.preferenceContent}>
        <View style={styles.preferenceIcon}>
          <Icon size={24} color={iconColor} />
        </View>
        <View style={styles.preferenceText}>
          <Text style={[styles.preferenceTitle, { color: textColor }]}>{title}</Text>
          <Text style={[styles.preferenceDescription, { color: subtitleColor }]}>
            {description}
          </Text>
        </View>
      </View>
      <Switch
        value={value}
        onValueChange={onValueChange}
        trackColor={{ false: '#767577', true: '#D4AF37' }}
        thumbColor={value ? '#FFFFFF' : '#f4f3f4'}
        ios_backgroundColor="#3e3e3e"
      />
    </View>
  );
};

export function NotificationPreferences() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { user } = useAuth();

  const [preferences, setPreferences] = useState<NotificationPreference | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Theme colors
  const backgroundColor = isDark ? '#000000' : '#FFFFFF';
  const textColor = isDark ? '#FFFFFF' : '#000000';
  const subtitleColor = isDark ? '#9CA3AF' : '#6B7280';
  const borderColor = isDark ? '#374151' : '#E5E7EB';

  // Load notification preferences
  useEffect(() => {
    const loadPreferences = async () => {
      if (!user) return;

      try {
        setLoading(true);
        
        // Try to get existing preferences
        // Note: notification_preferences table doesn't exist yet
        // Using local storage for now until table is implemented
        console.log('Loading notification preferences from local storage (table not implemented yet)');

        const localPrefsKey = `notification_preferences_${user.id}`;
        const localPrefs = await AsyncStorage.getItem(localPrefsKey);

        if (localPrefs) {
          setPreferences(JSON.parse(localPrefs));
        } else {
          // Create default preferences
          const defaultPreferences: NotificationPreference = {
            id: `temp_${user.id}_${Date.now()}`, // Temporary ID for local storage
            user_id: user.id,
            likes_enabled: true,
            comments_enabled: true,
            subscriptions_enabled: true,
            reviews_enabled: true,
            business_updates_enabled: true,
            email_notifications_enabled: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };

          await AsyncStorage.setItem(localPrefsKey, JSON.stringify(defaultPreferences));
          setPreferences(defaultPreferences);
        }
      } catch (error) {
        console.error('Error in loadPreferences:', error);
        Alert.alert('Error', 'Failed to load notification preferences');
      } finally {
        setLoading(false);
      }
    };

    loadPreferences();
  }, [user]);

  const updatePreference = async (field: keyof NotificationPreference, value: boolean) => {
    if (!preferences || !user) return;

    try {
      setSaving(true);

      const updatedPreferences = {
        ...preferences,
        [field]: value,
        updated_at: new Date().toISOString(),
      };

      // Note: notification_preferences table doesn't exist yet
      // Using local storage for now until table is implemented
      const localPrefsKey = `notification_preferences_${user.id}`;
      await AsyncStorage.setItem(localPrefsKey, JSON.stringify(updatedPreferences));

      setPreferences(updatedPreferences);
    } catch (error) {
      console.error('Error in updatePreference:', error);
      Alert.alert('Error', 'Failed to update notification preferences');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor }]}>
        <LoadingSpinner size="large" color="#D4AF37" />
        <Text style={[styles.loadingText, { color: textColor }]}>
          Loading notification preferences...
        </Text>
      </View>
    );
  }

  if (!preferences) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor }]}>
        <Text style={[styles.errorText, { color: textColor }]}>
          Failed to load notification preferences
        </Text>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor }]}>
      <View style={styles.header}>
        <View style={styles.headerIcon}>
          <Settings size={32} color="#D4AF37" />
        </View>
        <Text style={[styles.headerTitle, { color: textColor }]}>
          Notification Preferences
        </Text>
        <Text style={[styles.headerSubtitle, { color: subtitleColor }]}>
          Choose what notifications you want to receive
        </Text>
      </View>

      <View style={[styles.section, { borderTopColor: borderColor }]}>
        <Text style={[styles.sectionTitle, { color: textColor }]}>
          Activity Notifications
        </Text>
        
        <PreferenceItem
          icon={Heart}
          title="Likes"
          description="When someone likes your posts or business"
          value={preferences.likes_enabled}
          onValueChange={(value) => updatePreference('likes_enabled', value)}
          iconColor="#EF4444"
        />

        <PreferenceItem
          icon={MessageCircle}
          title="Comments"
          description="When someone comments on your posts"
          value={preferences.comments_enabled}
          onValueChange={(value) => updatePreference('comments_enabled', value)}
          iconColor="#3B82F6"
        />

        <PreferenceItem
          icon={Users}
          title="Subscriptions"
          description="When someone subscribes to your business"
          value={preferences.subscriptions_enabled}
          onValueChange={(value) => updatePreference('subscriptions_enabled', value)}
          iconColor="#10B981"
        />

        <PreferenceItem
          icon={Star}
          title="Reviews"
          description="When someone reviews your business"
          value={preferences.reviews_enabled}
          onValueChange={(value) => updatePreference('reviews_enabled', value)}
          iconColor="#F59E0B"
        />

        <PreferenceItem
          icon={Bell}
          title="Business Updates"
          description="Updates from businesses you follow"
          value={preferences.business_updates_enabled}
          onValueChange={(value) => updatePreference('business_updates_enabled', value)}
          iconColor="#8B5CF6"
        />
      </View>

      <View style={[styles.section, { borderTopColor: borderColor }]}>
        <Text style={[styles.sectionTitle, { color: textColor }]}>
          Delivery Methods
        </Text>
        
        <PreferenceItem
          icon={MessageCircle}
          title="Email Notifications"
          description="Receive notifications via email"
          value={preferences.email_notifications_enabled}
          onValueChange={(value) => updatePreference('email_notifications_enabled', value)}
          iconColor="#6B7280"
        />

        {/* Push notifications removed - only email notifications available */}
      </View>

      {saving && (
        <View style={styles.savingIndicator}>
          <LoadingSpinner size="small" color="#D4AF37" />
          <Text style={[styles.savingText, { color: subtitleColor }]}>
            Saving preferences...
          </Text>
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 24,
    alignItems: 'center',
  },
  headerIcon: {
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  section: {
    borderTopWidth: 1,
    paddingTop: 24,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    paddingHorizontal: 24,
  },
  preferenceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  preferenceContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 16,
  },
  preferenceIcon: {
    marginRight: 16,
  },
  preferenceText: {
    flex: 1,
  },
  preferenceTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  preferenceDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
  savingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
  },
  savingText: {
    marginLeft: 8,
    fontSize: 14,
  },
});
