export interface CardVisitsRow {
  id: string;
  business_profile_id: string;
  visited_at: string;
  visitor_identifier: string;
  visit_date: string;
}

export interface CardVisitsInsert {
  id?: string;
  business_profile_id: string;
  visited_at?: string;
  visitor_identifier: string;
  visit_date?: string;
}

export interface CardVisitsUpdate {
  id?: string;
  business_profile_id?: string;
  visited_at?: string;
  visitor_identifier?: string;
  visit_date?: string;
}

export type CardVisits = CardVisitsRow;
