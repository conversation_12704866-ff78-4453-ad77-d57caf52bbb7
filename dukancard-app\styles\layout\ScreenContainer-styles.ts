import { StyleSheet } from 'react-native';
import { useTheme } from '@/src/hooks/useTheme';

interface ScreenContainerStyleParams {
  theme: ReturnType<typeof useTheme>;
  backgroundColor?: string;
}

export const createScreenContainerStyles = ({ theme, backgroundColor }: ScreenContainerStyleParams) => {
  return StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: backgroundColor || theme.colors.background,
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    inner: {
      flex: 1,
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.md,
      justifyContent: 'space-between',
    },
    scrollView: {
      flex: 1,
    },
    scrollContentContainer: {
      flexGrow: 1,
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.md,
    },
    contentContainer: {
      flex: 1,
      justifyContent: 'space-between',
    },
  });
};
