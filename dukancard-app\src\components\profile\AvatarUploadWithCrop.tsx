import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Image } from 'expo-image';
import { Camera, User } from 'lucide-react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { useAuth } from '@/src/contexts/AuthContext';
import { openCameraForAvatar, openGalleryForAvatar } from '@/backend/supabase/services/storage/avatarUploadService';
import ImagePickerBottomSheet, { ImagePickerBottomSheetRef } from '@/src/components/pickers/ImagePickerBottomSheet';

interface AvatarUploadWithCropProps {
  initialAvatarUrl?: string | null;
  userName?: string | null;
  size?: number;
  showLabel?: boolean;
  onAvatarUpdated?: (url: string) => void;
  onImageSelectForCrop: (imageUri: string) => void;
  imagePickerRef: React.RefObject<ImagePickerBottomSheetRef | null>; // Allow null for ref
}

export function AvatarUploadWithCrop({
  initialAvatarUrl,
  userName,
  size = 120,
  showLabel = true,
  onAvatarUpdated,
  onImageSelectForCrop,
  imagePickerRef, // Destructure the ref prop
}: AvatarUploadWithCropProps) {
  const { user } = useAuth();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  // imagePickerRef is now passed as a prop

  const [avatarUrl, setAvatarUrl] = useState<string | null>(initialAvatarUrl || null);
  const [isSelectingImage, setIsSelectingImage] = useState(false); // Renamed from isUploading
  const [localPreviewUrl, setLocalPreviewUrl] = useState<string | null>(null); 

  useEffect(() => {
    setAvatarUrl(initialAvatarUrl || null);
  }, [initialAvatarUrl]);

  // Get user initials for placeholder
  const getInitials = (name?: string | null): string => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const initials = getInitials(userName);

  // Color scheme
  const backgroundColor = isDark ? '#1F2937' : '#F9FAFB';
  const borderColor = isDark ? '#374151' : '#E5E7EB';
  const textColor = isDark ? '#F9FAFB' : '#111827';
  const mutedTextColor = isDark ? '#9CA3AF' : '#6B7280';
  const goldColor = '#D4AF37';

  const showImagePickerOptions = () => {
    imagePickerRef.current?.present();
  };

  // Handle camera selection - now just passes URI to parent
  const handleCameraSelection = async () => {
    setIsSelectingImage(true); // Set selecting state
    try {
      const result = await openCameraForAvatar();
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const imageUri = result.assets[0].uri;
        
        if (result.assets[0].fileSize && result.assets[0].fileSize > 15 * 1024 * 1024) {
          Alert.alert('Error', 'File size must be less than 15MB.');
          return;
        }

        onImageSelectForCrop(imageUri); // Pass to parent for cropping
      }
    } catch (error) {
      console.error('Camera error:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    } finally {
      setIsSelectingImage(false); // Reset selecting state
    }
  };

  // Handle gallery selection - now just passes URI to parent
  const handleGallerySelection = async () => {
    setIsSelectingImage(true); // Set selecting state
    try {
      const result = await openGalleryForAvatar();
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const imageUri = result.assets[0].uri;
        
        if (result.assets[0].fileSize && result.assets[0].fileSize > 15 * 1024 * 1024) {
          Alert.alert('Error', 'File size must be less than 15MB.');
          return;
        }

        onImageSelectForCrop(imageUri); // Pass to parent for cropping
      }
    } catch (error) {
      console.error('Gallery error:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
    } finally {
      setIsSelectingImage(false); // Reset selecting state
    }
  };

  const displayUrl = localPreviewUrl || avatarUrl;

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.avatarContainer,
          {
            width: size,
            height: size,
            borderRadius: size / 2,
            borderColor: borderColor,
            backgroundColor: backgroundColor,
          },
        ]}
        onPress={showImagePickerOptions}
        disabled={isSelectingImage} // Use new state
        activeOpacity={0.7}
      >
        {displayUrl ? (
          <>
            <Image
              source={{ uri: displayUrl }}
              style={[
                styles.avatarImage,
                {
                  width: size - 4,
                  height: size - 4,
                  borderRadius: (size - 4) / 2,
                },
              ]}
              contentFit="cover"
              transition={200}
            />
            {/* Overlay for camera icon or activity indicator */}
            <View
              style={[
                styles.uploadOverlay,
                {
                  width: size,
                  height: size,
                  borderRadius: size / 2,
                },
              ]}
            >
              {isSelectingImage ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Camera size={Math.max(16, size * 0.2)} color="#FFFFFF" />
              )}
            </View>
          </>
        ) : (
          <View
            style={[
              styles.avatarPlaceholder,
              {
                width: size - 4,
                height: size - 4,
                borderRadius: (size - 4) / 2,
                backgroundColor: goldColor + '20',
              },
            ]}
          >
            {isSelectingImage ? (
              <ActivityIndicator size="small" color={goldColor} />
            ) : (
              <>
                <User size={Math.max(24, size * 0.3)} color={goldColor} />
                
              </>
            )}
          </View>
        )}
      </TouchableOpacity>

      {showLabel && (
        <Text style={[styles.hint, { color: mutedTextColor }]}>
          {isSelectingImage
            ? 'Selecting...'
            : 'Tap to change picture'}
        </Text>
      )}

      {/* ImagePickerBottomSheet is removed from here */}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  avatarContainer: {
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  avatarImage: {
    position: 'absolute',
  },
  uploadOverlay: {
    position: 'absolute',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarPlaceholder: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  initialsText: {
    fontWeight: '600',
    position: 'absolute',
  },
  hint: {
    fontSize: 12,
    marginTop: 8,
    textAlign: 'center',
  },
});
