[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:Clean up old GPS location fetch logic in DiscoverScreenNew DESCRIPTION:Remove any remnant auto GPS location fetch logic from DiscoverScreenNew.tsx that conflicts with the current profile-based location fetching approach
--[x] NAME:Remove auto GPS location detection from DiscoveryContext DESCRIPTION:Remove detectAndSaveCurrentLocation calls and auto GPS location logic from DiscoveryContext.tsx initialization
--[x] NAME:Update location initialization to only use profile data DESCRIPTION:Modify location initialization in DiscoveryContext to only fetch from customer/business profiles without GPS fallback
-[x] NAME:Add bottom padding to PublicCardView DESCRIPTION:Add bottom padding to PublicCardView component to provide spacing between content and bottom navigation bar
--[x] NAME:Add bottom padding styles to PublicCardView container DESCRIPTION:Update PublicCardViewStyles.ts to add bottom padding for navigation bar spacing
-[x] NAME:Implement global location context for current GPS location DESCRIPTION:Create a global location context that fetches and stores user's current GPS location when app opens and location permission is granted
--[x] NAME:Create LocationContext for global GPS location management DESCRIPTION:Create a new LocationContext that manages current GPS location state and fetches location on app start when permission is granted
--[x] NAME:Integrate LocationContext with app initialization DESCRIPTION:Add LocationContext provider to app root and initialize location fetching when user is not on login screen
-[ ] NAME:Add distance calculation and display functionality DESCRIPTION:Implement distance calculation between user location and business locations, and display distance in various components
--[x] NAME:Add distance calculation utility function DESCRIPTION:Create or enhance distance calculation utility to work with current GPS location and business coordinates
--[x] NAME:Update DiscoveryScreen to show distance in business cards DESCRIPTION:Modify BusinessCard component in discovery screen to display distance from user location
--[x] NAME:Update ProductCard to show distance DESCRIPTION:Add distance display to ProductCard component for products in discovery and other screens
--[ ] NAME:Update single product screen with distance DESCRIPTION:Add distance display to single product view screen
--[ ] NAME:Update feed page posts with distance DESCRIPTION:Add distance display to business posts in feed page
-[ ] NAME:Implement get directions functionality with open source maps DESCRIPTION:Add get directions button and full-screen map modal using open source map solution for navigation between user and business locations
--[ ] NAME:Research and select open source map solution DESCRIPTION:Research react-native-maps vs MapLibre vs other open source solutions and select the best option for directions
--[ ] NAME:Install and configure selected map library DESCRIPTION:Install the chosen open source map library and configure it for React Native
--[ ] NAME:Create DirectionsModal component DESCRIPTION:Create a full-screen modal component that shows directions between user location and business location
--[ ] NAME:Add get directions button to PublicCardView DESCRIPTION:Add get directions button to PublicCardView with proper validation and messaging
--[ ] NAME:Implement directions button logic and validation DESCRIPTION:Implement button state logic based on location availability and permission status