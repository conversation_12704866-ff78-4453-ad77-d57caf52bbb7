import { useState, useEffect, useCallback, useRef } from 'react';
import { fetchSinglePost, SinglePostResponse } from '@/lib/actions/posts/fetchSinglePost';
import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';

// Hook state interface
interface UseSinglePostState {
  post: UnifiedPost | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Custom hook for managing single post data in React Native
 * Handles loading states and error handling.
 */
export function useSinglePost(postId: string): UseSinglePostState {
  const [post, setPost] = useState<UnifiedPost | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const fetchPost = useCallback(async () => {
    if (!postId) {
      setError('Invalid post ID');
      setLoading(false);
      return;
    }

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    try {
      setLoading(true);
      setError(null);

      abortControllerRef.current = new AbortController();
      const result: SinglePostResponse = await fetchSinglePost(postId);

      // Check if request was aborted
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      if (result.success && result.data) {
        setPost(result.data);
        setError(null);
      } else {
        setPost(null);
        setError(result.message || 'Failed to fetch post');
      }
    } catch (err) {
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      console.error('Error in useSinglePost:', err);
      setPost(null);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      if (!abortControllerRef.current?.signal.aborted) {
        setLoading(false);
      }
    }
  }, [postId]);

  const refetch = useCallback(async () => {
    await fetchPost();
  }, [fetchPost]);

  useEffect(() => {
    fetchPost();

    // Cleanup on unmount
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [fetchPost]);

  return {
    post,
    loading,
    error,
    refetch,
  };
}
