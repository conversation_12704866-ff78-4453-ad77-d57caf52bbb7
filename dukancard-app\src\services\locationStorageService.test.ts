/**
 * Unit tests for locationStorageService
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  saveLocationData,
  loadLocationData,
  clearLocationData,
  savePincode,
  saveCity,
  saveLocality,
  saveGPSCoordinates,
  getStoredPincode,
  getStoredCity,
  getStoredLocality,
  getStoredGPSCoordinates,
  hasStoredLocationData,
} from './locationStorageService';
import { LocationData } from '@/src/types/discovery';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
  removeItem: jest.fn(),
}));

const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;

// Mock console methods to avoid noise in tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeAll(() => {
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

beforeEach(() => {
  jest.clearAllMocks();
});

describe('locationStorageService', () => {
  const testLocationData: LocationData = {
    pincode: '123456',
    city: 'Test City',
    state: 'Test State',
    locality: 'Test Locality',
    latitude: 12.34,
    longitude: 56.78,
  };

  describe('saveLocationData', () => {
    it('should save location data successfully', async () => {
      mockAsyncStorage.setItem.mockResolvedValueOnce();

      const result = await saveLocationData(testLocationData);

      expect(result.success).toBe(true);
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        '@dukancard_location_preferences',
        expect.stringContaining('"pincode":"123456"')
      );
    });

    it('should handle save errors', async () => {
      const error = new Error('Storage error');
      mockAsyncStorage.setItem.mockRejectedValueOnce(error);

      const result = await saveLocationData(testLocationData);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Storage error');
    });
  });

  describe('loadLocationData', () => {
    it('should load location data successfully', async () => {
      const storageData = {
        ...testLocationData,
        lastUpdated: new Date().toISOString(),
      };
      mockAsyncStorage.getItem.mockResolvedValueOnce(JSON.stringify(storageData));

      const result = await loadLocationData();

      expect(result.success).toBe(true);
      expect(result.data).toEqual(testLocationData);
    });

    it('should return undefined when no data exists', async () => {
      mockAsyncStorage.getItem.mockResolvedValueOnce(null);

      const result = await loadLocationData();

      expect(result.success).toBe(true);
      expect(result.data).toBeUndefined();
    });

    it('should handle invalid data by clearing storage', async () => {
      mockAsyncStorage.getItem.mockResolvedValueOnce('{"invalid": "data"}');
      mockAsyncStorage.removeItem.mockResolvedValueOnce();

      const result = await loadLocationData();

      expect(result.success).toBe(true);
      expect(result.data).toBeUndefined();
      expect(mockAsyncStorage.removeItem).toHaveBeenCalledWith('@dukancard_location_preferences');
    });

    it('should handle load errors', async () => {
      const error = new Error('Storage error');
      mockAsyncStorage.getItem.mockRejectedValueOnce(error);

      const result = await loadLocationData();

      expect(result.success).toBe(false);
      expect(result.error).toBe('Storage error');
    });
  });

  describe('clearLocationData', () => {
    it('should clear location data successfully', async () => {
      mockAsyncStorage.removeItem.mockResolvedValueOnce();

      const result = await clearLocationData();

      expect(result.success).toBe(true);
      expect(mockAsyncStorage.removeItem).toHaveBeenCalledWith('@dukancard_location_preferences');
    });

    it('should handle clear errors', async () => {
      const error = new Error('Storage error');
      mockAsyncStorage.removeItem.mockRejectedValueOnce(error);

      const result = await clearLocationData();

      expect(result.success).toBe(false);
      expect(result.error).toBe('Storage error');
    });
  });

  describe('savePincode', () => {
    it('should save pincode successfully', async () => {
      mockAsyncStorage.getItem.mockResolvedValueOnce(null);
      mockAsyncStorage.setItem.mockResolvedValueOnce();

      const result = await savePincode('654321');

      expect(result.success).toBe(true);
      expect(mockAsyncStorage.setItem).toHaveBeenCalled();
    });
  });

  describe('saveCity', () => {
    it('should save city successfully', async () => {
      mockAsyncStorage.getItem.mockResolvedValueOnce(null);
      mockAsyncStorage.setItem.mockResolvedValueOnce();

      const result = await saveCity('New City');

      expect(result.success).toBe(true);
      expect(mockAsyncStorage.setItem).toHaveBeenCalled();
    });
  });

  describe('saveLocality', () => {
    it('should save locality successfully', async () => {
      mockAsyncStorage.getItem.mockResolvedValueOnce(null);
      mockAsyncStorage.setItem.mockResolvedValueOnce();

      const result = await saveLocality('New Locality');

      expect(result.success).toBe(true);
      expect(mockAsyncStorage.setItem).toHaveBeenCalled();
    });
  });

  describe('saveGPSCoordinates', () => {
    it('should save GPS coordinates successfully', async () => {
      mockAsyncStorage.getItem.mockResolvedValueOnce(null);
      mockAsyncStorage.setItem.mockResolvedValueOnce();

      const result = await saveGPSCoordinates(98.76, 54.32);

      expect(result.success).toBe(true);
      expect(mockAsyncStorage.setItem).toHaveBeenCalled();
    });
  });

  describe('getStoredPincode', () => {
    it('should get stored pincode successfully', async () => {
      const storageData = {
        ...testLocationData,
        lastUpdated: new Date().toISOString(),
      };
      mockAsyncStorage.getItem.mockResolvedValueOnce(JSON.stringify(storageData));

      const result = await getStoredPincode();

      expect(result.success).toBe(true);
      expect(result.pincode).toBe('123456');
    });

    it('should return undefined when no pincode exists', async () => {
      mockAsyncStorage.getItem.mockResolvedValueOnce(null);

      const result = await getStoredPincode();

      expect(result.success).toBe(true);
      expect(result.pincode).toBeUndefined();
    });
  });

  describe('getStoredCity', () => {
    it('should get stored city successfully', async () => {
      const storageData = {
        ...testLocationData,
        lastUpdated: new Date().toISOString(),
      };
      mockAsyncStorage.getItem.mockResolvedValueOnce(JSON.stringify(storageData));

      const result = await getStoredCity();

      expect(result.success).toBe(true);
      expect(result.city).toBe('Test City');
    });
  });

  describe('getStoredLocality', () => {
    it('should get stored locality successfully', async () => {
      const storageData = {
        ...testLocationData,
        lastUpdated: new Date().toISOString(),
      };
      mockAsyncStorage.getItem.mockResolvedValueOnce(JSON.stringify(storageData));

      const result = await getStoredLocality();

      expect(result.success).toBe(true);
      expect(result.locality).toBe('Test Locality');
    });
  });

  describe('getStoredGPSCoordinates', () => {
    it('should get stored GPS coordinates successfully', async () => {
      const storageData = {
        ...testLocationData,
        lastUpdated: new Date().toISOString(),
      };
      mockAsyncStorage.getItem.mockResolvedValueOnce(JSON.stringify(storageData));

      const result = await getStoredGPSCoordinates();

      expect(result.success).toBe(true);
      expect(result.coordinates).toEqual({ latitude: 12.34, longitude: 56.78 });
    });

    it('should return undefined when no coordinates exist', async () => {
      const storageData = {
        pincode: '123456',
        lastUpdated: new Date().toISOString(),
      };
      mockAsyncStorage.getItem.mockResolvedValueOnce(JSON.stringify(storageData));

      const result = await getStoredGPSCoordinates();

      expect(result.success).toBe(true);
      expect(result.coordinates).toBeUndefined();
    });
  });

  describe('hasStoredLocationData', () => {
    it('should return true when data exists', async () => {
      const storageData = {
        ...testLocationData,
        lastUpdated: new Date().toISOString(),
      };
      mockAsyncStorage.getItem.mockResolvedValueOnce(JSON.stringify(storageData));

      const result = await hasStoredLocationData();

      expect(result.success).toBe(true);
      expect(result.hasData).toBe(true);
    });

    it('should return false when no data exists', async () => {
      mockAsyncStorage.getItem.mockResolvedValueOnce(null);

      const result = await hasStoredLocationData();

      expect(result.success).toBe(true);
      expect(result.hasData).toBe(false);
    });
  });
});
