import { ScrollView, View } from 'react-native';
import { ThemedText } from '@/src/components/ThemedText';
import { ThemedView } from '@/src/components/ThemedView';
import { createAnalyticsStyles } from '@/styles/dashboard/business/analytics-styles';

export default function BusinessAnalyticsScreen() {
  const styles = createAnalyticsStyles();

  return (
    <ThemedView style={styles.container}>
      <ScrollView contentContainerStyle={styles.content}>
        <View style={styles.header}>
          <ThemedText type="title">Analytics</ThemedText>
          <ThemedText style={styles.subtitle}>
            Track your business performance
          </ThemedText>
        </View>

        <View style={styles.metricsContainer}>
          <View style={styles.metricCard}>
            <ThemedText type="defaultSemiBold" style={styles.metricTitle}>
              Visits This Week
            </ThemedText>
            <ThemedText style={styles.metricValue}>156</ThemedText>
            <ThemedText style={styles.metricChange}>+12% from last week</ThemedText>
          </View>

          <View style={styles.metricCard}>
            <ThemedText type="defaultSemiBold" style={styles.metricTitle}>
              New Followers
            </ThemedText>
            <ThemedText style={styles.metricValue}>23</ThemedText>
            <ThemedText style={styles.metricChange}>+8% from last week</ThemedText>
          </View>

          <View style={styles.metricCard}>
            <ThemedText type="defaultSemiBold" style={styles.metricTitle}>
              Average Rating
            </ThemedText>
            <ThemedText style={styles.metricValue}>4.8</ThemedText>
            <ThemedText style={styles.metricChange}>+0.2 from last month</ThemedText>
          </View>
        </View>

        <View style={styles.chartPlaceholder}>
          <ThemedText style={styles.chartText}>
            📊 Charts and detailed analytics will be implemented here
          </ThemedText>
        </View>
      </ScrollView>
    </ThemedView>
  );
}


