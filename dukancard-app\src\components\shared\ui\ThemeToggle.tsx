import React from 'react';
import { TouchableOpacity, StyleSheet } from 'react-native';
import { Moon, Sun } from 'lucide-react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';

interface ThemeToggleProps {
  variant?: 'default' | 'dashboard';
  size?: number;
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({
  variant = 'default',
  size = 20,
}) => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const iconColor = isDark ? '#FBBF24' : '#6B7280';
  
  const toggleTheme = () => {
    // TODO: Implement theme switching logic
    // This would typically involve updating a theme context or AsyncStorage
  };

  return (
    <TouchableOpacity
      style={[
        styles.button,
        variant === 'dashboard' && styles.dashboardButton
      ]}
      onPress={toggleTheme}
      activeOpacity={0.7}
    >
      {isDark ? (
        <Sun color={iconColor} size={size} strokeWidth={1.5} />
      ) : (
        <Moon color={iconColor} size={size} strokeWidth={1.5} />
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    padding: 8,
    borderRadius: 8,
  },
  dashboardButton: {
    padding: 6,
  },
});

export default ThemeToggle;
