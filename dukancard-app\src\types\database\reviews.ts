import { CustomerProfiles } from "./customer-profiles";

export interface ReviewsRow {
  id: string;
  rating: number;
  review_text: string | null;
  created_at: string;
  updated_at: string;
  user_id: string;
  business_profile_id: string;
}

export interface ReviewsInsert {
  id?: string;
  rating: number;
  review_text?: string | null;
  created_at?: string;
  updated_at?: string;
  user_id: string;
  business_profile_id: string;
}

export interface ReviewsUpdate {
  id?: string;
  rating?: number;
  review_text?: string | null;
  created_at?: string;
  updated_at?: string;
  user_id?: string;
  business_profile_id?: string;
}

export type Reviews = ReviewsRow;

export type ReviewWithUser = Reviews & {
  user_profile?: Partial<CustomerProfiles> & {
    is_business?: boolean;
    business_slug?: string | null;
  };
};
