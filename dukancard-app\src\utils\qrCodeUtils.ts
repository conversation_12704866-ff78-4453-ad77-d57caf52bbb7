import { validateBusinessSlug } from '@/backend/supabase/utils/validation';
import { BACKEND_CONFIG } from '../../src/config/publicKeys';

export interface QRCodeValidationResult {
  isValid: boolean;
  error?: string;
  businessSlug?: string;
  url?: string;
}

/**
 * Validates if a QR code contains a valid dukancard.in URL
 * @param qrData - The raw data from the QR code scan
 * @returns Validation result with business slug if valid
 */
export function validateQRCodeUrl(qrData: string): QRCodeValidationResult {
  if (!qrData || typeof qrData !== 'string') {
    return {
      isValid: false,
      error: 'Invalid QR code data'
    };
  }

  // Clean the data
  const cleanData = qrData.trim();

  if (!cleanData) {
    return {
      isValid: false,
      error: 'Empty QR code data'
    };
  }

  // Check if it's a valid URL
  let url: URL;
  try {
    // Handle cases where the QR code might not have a protocol
    const urlString = cleanData.startsWith('http') ? cleanData : `https://${cleanData}`;
    url = new URL(urlString);
  } catch (error) {
    return {
      isValid: false,
      error: 'QR code does not contain a valid URL'
    };
  }

  // Check if it's a dukancard.in domain
  const validDomains = ['dukancard.in', 'www.dukancard.in'];
  if (!validDomains.includes(url.hostname.toLowerCase())) {
    return {
      isValid: false,
      error: 'QR code is not from Dukancard'
    };
  }

  // Extract the business slug from the path
  const pathSegments = url.pathname.split('/').filter(segment => segment.length > 0);
  
  if (pathSegments.length === 0) {
    return {
      isValid: false,
      error: 'QR code does not contain a business profile URL'
    };
  }

  const businessSlug = pathSegments[0];

  // Validate the business slug format
  const slugValidation = validateBusinessSlug(businessSlug);
  if (!slugValidation.isValid) {
    return {
      isValid: false,
      error: slugValidation.error || 'Invalid business URL format'
    };
  }

  return {
    isValid: true,
    businessSlug,
    url: url.toString()
  };
}

/**
 * Generates a dukancard.in URL for a business slug
 * @param businessSlug - The business slug
 * @returns Complete dukancard.in URL
 */
export function generateDukancardUrl(businessSlug: string): string {
  return `${BACKEND_CONFIG.baseUrl}/${businessSlug}`;
}

/**
 * Extracts business slug from a dukancard.in URL
 * @param url - The dukancard.in URL
 * @returns Business slug if valid, null otherwise
 */
export function extractBusinessSlugFromUrl(url: string): string | null {
  const validation = validateQRCodeUrl(url);
  return validation.isValid ? validation.businessSlug! : null;
}

/**
 * Checks if a URL is a valid dukancard.in business URL
 * @param url - The URL to check
 * @returns True if it's a valid dukancard business URL
 */
export function isDukancardBusinessUrl(url: string): boolean {
  const validation = validateQRCodeUrl(url);
  return validation.isValid;
}

/**
 * Validates QR code data and provides user-friendly error messages
 * @param qrData - The raw QR code data
 * @returns User-friendly validation result
 */
export function validateQRCodeForUser(qrData: string): QRCodeValidationResult {
  const result = validateQRCodeUrl(qrData);
  
  if (!result.isValid && result.error) {
    // Convert technical errors to user-friendly messages
    const userFriendlyErrors: Record<string, string> = {
      'Invalid QR code data': 'Unable to read QR code. Please try again.',
      'Empty QR code data': 'QR code appears to be empty. Please try scanning again.',
      'QR code does not contain a valid URL': 'This QR code does not contain a valid web address.',
      'QR code is not from Dukancard': 'This QR code is not from Dukancard. Please scan a Dukancard business QR code.',
      'QR code does not contain a business profile URL': 'This QR code does not link to a business profile.',
      'Invalid business URL format': 'This business URL format is not valid.'
    };

    const userFriendlyError = userFriendlyErrors[result.error] || result.error;
    
    return {
      ...result,
      error: userFriendlyError
    };
  }

  return result;
}
