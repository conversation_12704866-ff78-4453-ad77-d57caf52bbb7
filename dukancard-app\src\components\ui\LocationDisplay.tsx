import React, { useState, useEffect } from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { MapPin } from 'lucide-react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/src/contexts/AuthContext';

interface LocationDisplayProps {
  style?: any;
}

interface LocationData {
  city?: string;
  state?: string;
  locality?: string;
  pincode?: string;
}

export function LocationDisplay({ style }: LocationDisplayProps) {
  const [location, setLocation] = useState<LocationData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const colorScheme = useColorScheme();
  const { user } = useAuth();
  const isDark = colorScheme === 'dark';

  useEffect(() => {
    const fetchUserLocation = async () => {
      if (!user?.id) {
        setError('Authentication required');
        setIsLoading(false);
        return;
      }

      try {
        // Try to get business profile first, then customer profile
        const { data: businessProfile } = await supabase
          .from('business_profiles')
          .select('city, state, locality, pincode')
          .eq('id', user.id)
          .single();

        if (businessProfile) {
          setLocation(businessProfile);
          setIsLoading(false);
          return;
        }

        // Fallback to customer profile
        const { data: customerProfile } = await supabase
          .from('customer_profiles')
          .select('city, state, locality, pincode')
          .eq('id', user.id)
          .single();

        if (customerProfile) {
          setLocation(customerProfile);
        } else {
          setError('Location not found in profile');
        }
      } catch (err) {
        console.error('Error fetching location:', err);
        setError('Failed to load location');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserLocation();
  }, [user?.id]);

  const textColor = isDark ? '#FFFFFF' : '#000000';
  const mutedTextColor = isDark ? '#9CA3AF' : '#6B7280';
  const backgroundColor = isDark ? '#1F2937' : '#F9FAFB';
  const borderColor = isDark ? '#374151' : '#E5E7EB';

  if (isLoading) {
    return (
      <View style={[
        {
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: 12,
          paddingVertical: 8,
          backgroundColor,
          borderRadius: 8,
          borderWidth: 1,
          borderColor,
          marginBottom: 12,
        },
        style
      ]}>
        <ActivityIndicator size="small" color="#D4AF37" style={{ marginRight: 8 }} />
        <Text style={{ color: mutedTextColor, fontSize: 14 }}>Loading location...</Text>
      </View>
    );
  }

  if (error || !location) {
    return (
      <View style={[
        {
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: 12,
          paddingVertical: 8,
          backgroundColor,
          borderRadius: 8,
          borderWidth: 1,
          borderColor,
          marginBottom: 12,
        },
        style
      ]}>
        <MapPin size={16} color="#D4AF37" style={{ marginRight: 8 }} />
        <Text style={{ color: mutedTextColor, fontSize: 14 }}>Location not available</Text>
      </View>
    );
  }

  // Format location string
  const locationParts = [
    location.locality,
    location.city,
    location.state,
    location.pincode
  ].filter(Boolean);

  const locationString = locationParts.join(', ');

  return (
    <View style={[
      {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 8,
        backgroundColor,
        borderRadius: 8,
        borderWidth: 1,
        borderColor,
        marginBottom: 12,
      },
      style
    ]}>
      <MapPin size={16} color="#D4AF37" style={{ marginRight: 8 }} />
      <Text style={{ color: mutedTextColor, fontSize: 14 }}>
        <Text style={{ fontWeight: '600' }}>Posting from:</Text> {locationString}
      </Text>
    </View>
  );
}
