import { useTheme } from '@/src/hooks/useTheme';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import { Camera, Image as ImageIcon, X } from 'lucide-react-native';
import React, { forwardRef, useCallback, useImperativeHandle, useMemo } from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';

interface ImagePickerBottomSheetProps {
  onCameraPress: () => void;
  onGalleryPress: () => void;
  title?: string;
  cameraLabel?: string;
  galleryLabel?: string;
}

export interface ImagePickerBottomSheetRef {
  present: () => void;
  dismiss: () => void;
}

const ImagePickerBottomSheet = forwardRef<ImagePickerBottomSheetRef, ImagePickerBottomSheetProps>(
  ({ 
    onCameraPress, 
    onGalleryPress, 
    title = "Select Photo",
    cameraLabel = "Take Photo",
    galleryLabel = "Choose from Gallery"
  }, ref) => {
    const { colors, isDark } = useTheme();
    const bottomSheetRef = React.useRef<BottomSheet>(null);

    // Snap points for the bottom sheet - provide better sizing for image selection
    const snapPoints = useMemo(() => ['50%', '65%'], []);

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      present: () => {
        bottomSheetRef.current?.snapToIndex(1); // Start at second snap point (65%) for better visibility
      },
      dismiss: () => {
        bottomSheetRef.current?.close();
      },
    }));

    const handleCameraPress = useCallback(() => {
      bottomSheetRef.current?.close();
      // Small delay to ensure sheet closes before opening camera
      setTimeout(() => {
        onCameraPress();
      }, 300);
    }, [onCameraPress]);

    const handleGalleryPress = useCallback(() => {
      bottomSheetRef.current?.close();
      // Small delay to ensure sheet closes before opening gallery
      setTimeout(() => {
        onGalleryPress();
      }, 300);
    }, [onGalleryPress]);

    const handleClose = useCallback(() => {
      bottomSheetRef.current?.close();
    }, []);

    return (
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        enablePanDownToClose
        backgroundStyle={{
          backgroundColor: isDark ? '#000000' : '#ffffff',
        }}
        handleIndicatorStyle={{
          backgroundColor: colors.textSecondary,
        }}
      >
        <BottomSheetView style={styles.container}>
          {/* Header */}
          <View style={[
            styles.header,
            {
              borderBottomColor: colors.border,
            }
          ]}>
            <Text style={[styles.headerTitle, { color: colors.textPrimary }]}>
              {title}
            </Text>
            <TouchableOpacity
              onPress={handleClose}
              style={styles.closeButton}
            >
              <X size={24} color={colors.textPrimary} />
            </TouchableOpacity>
          </View>

          {/* Options */}
          <View style={styles.optionsContainer}>
            {/* Camera Option */}
            <TouchableOpacity
              style={[
                styles.optionButton,
                {
                  backgroundColor: isDark ? '#1F2937' : '#F9FAFB',
                  borderColor: colors.border,
                }
              ]}
              onPress={handleCameraPress}
              activeOpacity={0.7}
            >
              <View style={[
                styles.optionIconContainer,
                { backgroundColor: colors.primary + '20' }
              ]}>
                <Camera size={24} color={colors.primary} />
              </View>
              <View style={styles.optionTextContainer}>
                <Text style={[styles.optionTitle, { color: colors.textPrimary }]}>
                  {cameraLabel}
                </Text>
                <Text style={[styles.optionDescription, { color: colors.textSecondary }]}>
                  Take a new photo with your camera
                </Text>
              </View>
            </TouchableOpacity>

            {/* Gallery Option */}
            <TouchableOpacity
              style={[
                styles.optionButton,
                {
                  backgroundColor: isDark ? '#1F2937' : '#F9FAFB',
                  borderColor: colors.border,
                }
              ]}
              onPress={handleGalleryPress}
              activeOpacity={0.7}
            >
              <View style={[
                styles.optionIconContainer,
                { backgroundColor: colors.primary + '20' }
              ]}>
                <ImageIcon size={24} color={colors.primary} />
              </View>
              <View style={styles.optionTextContainer}>
                <Text style={[styles.optionTitle, { color: colors.textPrimary }]}>
                  {galleryLabel}
                </Text>
                <Text style={[styles.optionDescription, { color: colors.textSecondary }]}>
                  Select from your photo library
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </BottomSheetView>
      </BottomSheet>
    );
  }
);

ImagePickerBottomSheet.displayName = 'ImagePickerBottomSheet';

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  optionsContainer: {
    padding: 20,
    gap: 12,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  optionIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  optionTextContainer: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
});

export default ImagePickerBottomSheet;
