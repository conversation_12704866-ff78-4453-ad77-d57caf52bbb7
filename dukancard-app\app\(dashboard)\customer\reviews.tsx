import { DashboardLayout } from '@/src/components/shared/layout/DashboardLayout';
import React, { useState, useEffect, useCallback } from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  FlatList, 
  RefreshControl, 
  ActivityIndicator,
  Alert
} from 'react-native';
import { supabase } from '@/lib/supabase';
import { ReviewCard } from '@/src/components/social/ReviewCard';
import { EmptyState } from '@/src/components/shared/ui/EmptyState';
import { LoadingSpinner } from '@/src/components/shared/ui/LoadingSpinner';
import { ReviewListSkeleton } from '@/src/components/social/SkeletonLoaders';
import { reviewsService, ReviewData } from '@/backend/supabase/services/posts/socialService';
import { SortSelector } from '@/src/components/social/SortSelector';

type ReviewSortOption = 'newest' | 'oldest' | 'rating_high' | 'rating_low';

export default function CustomerReviewsScreen() {
  const [reviews, setReviews] = useState<ReviewData[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [sortBy, setSortBy] = useState<ReviewSortOption>('newest');
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [user, setUser] = useState<any>(null);

  // Get current user
  useEffect(() => {
    const getCurrentUser = async () => {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error || !user) {
        Alert.alert('Error', 'Please log in to view your reviews');
        return;
      }
      setUser(user);
    };
    getCurrentUser();
  }, []);

  // Fetch reviews
  const fetchReviews = useCallback(async (page: number = 1, sort: ReviewSortOption = 'newest', isRefresh: boolean = false) => {
    if (!user) return;

    try {
      if (isRefresh) {
        setRefreshing(true);
      } else if (page === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const result = await reviewsService.fetchReviews(user.id, page, 10, sort);
      
      if (page === 1 || isRefresh) {
        setReviews(result.items);
      } else {
        setReviews(prev => [...prev, ...result.items]);
      }
      
      setTotalCount(result.totalCount);
      setHasMore(result.hasMore);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error fetching reviews:', error);
      Alert.alert('Error', 'Failed to load reviews. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
      setLoadingMore(false);
    }
  }, [user]);

  // Initial load
  useEffect(() => {
    if (user) {
      fetchReviews(1, sortBy);
    }
  }, [user, fetchReviews, sortBy]);

  // Handle sort change
  const handleSortChange = useCallback((newSort: ReviewSortOption) => {
    setSortBy(newSort);
    setCurrentPage(1);
    fetchReviews(1, newSort);
  }, [fetchReviews]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setCurrentPage(1);
    fetchReviews(1, sortBy, true);
  }, [fetchReviews, sortBy]);

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (!loadingMore && hasMore) {
      fetchReviews(currentPage + 1, sortBy);
    }
  }, [loadingMore, hasMore, currentPage, sortBy, fetchReviews]);

  // Handle review deletion
  const handleDeleteReview = useCallback(async (reviewId: string) => {
    try {
      await reviewsService.deleteReview(reviewId);
      setReviews(prev => prev.filter(review => review.id !== reviewId));
      setTotalCount(prev => prev - 1);
      Alert.alert('Success', 'Review deleted successfully');
    } catch (error) {
      console.error('Error deleting review:', error);
      Alert.alert('Error', 'Failed to delete review. Please try again.');
    }
  }, []);

  // Handle review update
  const handleUpdateReview = useCallback(async (reviewId: string, rating: number, reviewText: string) => {
    try {
      await reviewsService.updateReview(reviewId, rating, reviewText);
      // Update the review in the local state
      setReviews(prev => prev.map(review => 
        review.id === reviewId 
          ? { ...review, rating, review_text: reviewText, updated_at: new Date().toISOString() }
          : review
      ));
      Alert.alert('Success', 'Review updated successfully');
    } catch (error) {
      console.error('Error updating review:', error);
      Alert.alert('Error', 'Failed to update review. Please try again.');
    }
  }, []);

  // Render review item
  const renderReview = ({ item }: { item: ReviewData }) => (
    <ReviewCard
      review={item}
      onDelete={handleDeleteReview}
      onUpdate={handleUpdateReview}
    />
  );

  // Render footer
  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color="#D4AF37" />
      </View>
    );
  };

  if (loading) {
    return (
      <DashboardLayout
        userName={user?.user_metadata?.name || "User"}
        showNotifications={true}
      >
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.title}>⭐ My Reviews</Text>
            <Text style={styles.subtitle}>Loading your reviews...</Text>
          </View>
          <View style={styles.listContainer}>
            <ReviewListSkeleton count={3} />
          </View>
        </View>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      userName={user?.user_metadata?.name || "User"}
      showNotifications={true}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>⭐ My Reviews</Text>
          <Text style={styles.subtitle}>
            {totalCount} {totalCount === 1 ? 'review' : 'reviews'} written
          </Text>
        </View>

        <SortSelector
          value={sortBy}
          onValueChange={handleSortChange}
          options={[
            { label: 'Newest First', value: 'newest' },
            { label: 'Oldest First', value: 'oldest' },
            { label: 'Highest Rating', value: 'rating_high' },
            { label: 'Lowest Rating', value: 'rating_low' },
          ]}
        />

        {reviews.length === 0 ? (
          <EmptyState
            title="No reviews yet"
            description="You haven&apos;t written any reviews yet. Visit businesses and share your experience with others."
            actionText="Discover Businesses"
            onAction={() => {/* Navigate to discover */}}
            icon="star-outline"
          />
        ) : (
          <FlatList
            data={reviews}
            renderItem={renderReview}
            keyExtractor={(item) => item.id}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={['#D4AF37']}
                tintColor="#D4AF37"
              />
            }
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.1}
            ListFooterComponent={renderFooter}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContainer}
          />
        )}
      </View>
    </DashboardLayout>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  footerLoader: {
    paddingVertical: 20,
    alignItems: 'center',
  },
});
