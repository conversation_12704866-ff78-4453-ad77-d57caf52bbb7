import React, { useState } from "react";
import { View, Text, TouchableOpacity, Alert } from "react-native";
import { Mail, Loader2, Link, AlertCircle } from "lucide-react-native";
import { Input } from "@/src/components/ui/Input";
import { Button } from "@/src/components/ui/Button";
import { useTheme } from '@/src/hooks/useTheme';
import { createLinkEmailSectionStyles } from '@/styles/dashboard/customer/settings/link-email';

interface LinkEmailSectionProps {
  currentEmail?: string | null | undefined;
  currentPhone?: string | null | undefined;
  registrationType: 'google' | 'email' | 'phone';
}

export default function LinkEmailSection({
  currentEmail,
  currentPhone: _currentPhone,
  registrationType
}: LinkEmailSectionProps) {
  const theme = useTheme();
  const styles = createLinkEmailSectionStyles(theme);

  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  const [step, setStep] = useState<'email' | 'otp'>('email');
  const [email, setEmail] = useState(registrationType === 'email' ? (currentEmail || "") : "");
  const [otp, setOtp] = useState("");
  const [emailForOTP, setEmailForOTP] = useState<string>('');

  // Handle email linking
  const onEmailSubmit = async () => {
    if (!email || !email.includes('@')) {
      Alert.alert('Error', 'Please enter a valid email address');
      return;
    }

    setIsLoading(true);
    try {
      // TODO: Implement actual email linking logic
      // For now, simulate the flow
      if (registrationType === 'phone') {
        setEmailForOTP(email);
        setStep('otp');
        setMessage("We've sent a 6-digit verification code to your email address.");
        Alert.alert('Success', 'Verification code sent!');
      } else {
        setMessage("Please check your email for the verification link.");
        Alert.alert('Success', 'Verification email sent!');
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle OTP verification
  const onOTPSubmit = async () => {
    if (!otp || otp.length !== 6) {
      Alert.alert('Error', 'Please enter a valid 6-digit code');
      return;
    }

    setIsLoading(true);
    try {
      // TODO: Implement actual OTP verification logic
      // For now, simulate success
      Alert.alert('Success', 'Email linked successfully!');
      setMessage("Email address has been linked to your account.");
      setStep('email');
      setEmailForOTP('');
      setEmail('');
      setOtp('');
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle back to email step
  const onBackToEmail = () => {
    setStep('email');
    setEmailForOTP('');
    setMessage(null);
    setOtp('');
  };

  // Determine the behavior based on registration type
  const isGoogleUser = registrationType === 'google';
  const isEmailUser = registrationType === 'email';
  const _isPhoneUser = registrationType === 'phone';

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <View style={styles.iconContainer}>
            <Mail size={16} color={theme.colors.primary} />
          </View>
          <Text style={styles.title}>
            {isGoogleUser ? "Email Address (Google)" : isEmailUser ? "Update Email Address" : "Link Email Address"}
          </Text>
        </View>
        <Text style={styles.description}>
          {isGoogleUser
            ? "Your email is linked to your Google account and cannot be changed here."
            : isEmailUser
            ? "Update your email address. We'll send OTP verification to both old and new email addresses."
            : "Add an email address to your account for additional login options and notifications."
          }
        </Text>
      </View>
      <View style={styles.content}>
        {message && (
          <View style={styles.messageContainer}>
            <View style={styles.messageContent}>
              <AlertCircle size={16} color={theme.colors.primary} />
              <Text style={styles.messageText}>{message}</Text>
            </View>
          </View>
        )}

        {isGoogleUser ? (
          // Google users - show email as read-only
          <View style={styles.section}>
            <Text style={styles.label}>Email Address</Text>
            <View style={styles.readOnlyContainer}>
              <Text style={styles.readOnlyText}>{currentEmail}</Text>
            </View>
            <Text style={styles.helperText}>
              This email is managed by your Google account.
            </Text>
          </View>
        ) : step === 'email' ? (
          // Email step - show email form
          <View style={styles.section}>
            <Text style={styles.label}>Email Address</Text>
            <Input
              placeholder="<EMAIL>"
              value={email}
              onChangeText={setEmail}
              type="email"
              containerStyle={styles.inputContainer}
            />
            <Text style={styles.helperText}>
              {isEmailUser
                ? "We'll send verification codes to both your current and new email addresses."
                : "We'll send a verification code to this email address."
              }
            </Text>

            <View style={styles.buttonContainer}>
              <Button
                title={isLoading ? "Sending..." : (isEmailUser ? "Update Email" : "Link Email Address")}
                onPress={onEmailSubmit}
                disabled={isLoading || !email}
                loading={isLoading}
                variant="primary"
                style={styles.submitButton}
                icon={isLoading ? undefined : <Link size={16} color="#FFFFFF" />}
              />
            </View>
          </View>
        ) : (
          // OTP step - show OTP form
          <View style={styles.section}>
            <View style={styles.otpHeader}>
              <Text style={styles.description}>
                We&apos;ve sent a 6-digit code to
              </Text>
              <Text style={styles.emailDisplay}>{emailForOTP}</Text>
              <Text style={styles.helperText}>
                Code expires in 24 hours
              </Text>
            </View>

            <Text style={styles.label}>Enter Verification Code</Text>
            <Input
              placeholder="123456"
              value={otp}
              onChangeText={setOtp}
              keyboardType="numeric"
              maxLength={6}
              containerStyle={styles.inputContainer}
            />

            <View style={styles.buttonRow}>
              <TouchableOpacity
                onPress={onBackToEmail}
                disabled={isLoading}
                style={styles.backButton}
              >
                <Text style={styles.backButtonText}>← Back to Email</Text>
              </TouchableOpacity>

              <Button
                title={isLoading ? "Verifying..." : "Verify & Link Email"}
                onPress={onOTPSubmit}
                disabled={isLoading || otp.length !== 6}
                loading={isLoading}
                variant="primary"
                style={styles.submitButton}
                icon={isLoading ? undefined : <Link size={16} color="#FFFFFF" />}
              />
            </View>
          </View>
        )}
      </View>
    </View>
  );
}
