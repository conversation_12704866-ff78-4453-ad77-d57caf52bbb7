import { supabase } from '@/lib/supabase';

export interface SettingsServiceResponse {
  success: boolean;
  error?: string;
  data?: any;
  requiresOTP?: boolean;
}

export interface PasswordUpdateData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

/**
 * Link email address to customer account
 */
export async function linkCustomerEmail(email: string): Promise<SettingsServiceResponse> {
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return { success: false, error: 'Invalid email format' };
    }

    // Check if email is already in use by another user
    const { data: existingUser, error: checkError } = await supabase
      .from('auth.users')
      .select('id')
      .eq('email', email)
      .neq('id', user.id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking email availability:', checkError);
      return { success: false, error: 'Failed to check email availability' };
    }

    if (existingUser) {
      return { success: false, error: 'Email is already in use by another account' };
    }

    // For phone users, we need OTP verification
    if (user.phone && !user.email) {
      // Send OTP to the new email
      const { error: otpError } = await supabase.auth.signInWithOtp({
        email: email,
        options: {
          shouldCreateUser: false,
        },
      });

      if (otpError) {
        console.error('Error sending OTP:', otpError);
        return { success: false, error: 'Failed to send verification code' };
      }

      return { 
        success: true, 
        requiresOTP: true,
        data: { email }
      };
    }

    // For email users or users with no email, update directly
    const { error: updateError } = await supabase.auth.updateUser({
      email: email,
    });

    if (updateError) {
      console.error('Error updating email:', updateError);
      return { success: false, error: updateError.message };
    }

    return { success: true, requiresOTP: false };
  } catch (error) {
    console.error('Unexpected error linking email:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Verify email OTP for linking
 */
export async function verifyEmailOTP(email: string, otp: string): Promise<SettingsServiceResponse> {
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Verify OTP
    const { error: verifyError } = await supabase.auth.verifyOtp({
      email: email,
      token: otp,
      type: 'email',
    });

    if (verifyError) {
      console.error('Error verifying OTP:', verifyError);
      return { success: false, error: 'Invalid verification code' };
    }

    // Update user email
    const { error: updateError } = await supabase.auth.updateUser({
      email: email,
    });

    if (updateError) {
      console.error('Error updating email after OTP:', updateError);
      return { success: false, error: updateError.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Unexpected error verifying email OTP:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Link phone number to customer account
 */
export async function linkCustomerPhone(phone: string): Promise<SettingsServiceResponse> {
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Validate phone format (10 digits)
    if (!/^\d{10}$/.test(phone)) {
      return { success: false, error: 'Phone number must be 10 digits' };
    }

    // Check if phone is already in use by another user
    const { data: existingUser, error: checkError } = await supabase
      .from('auth.users')
      .select('id')
      .eq('phone', `+91${phone}`)
      .neq('id', user.id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking phone availability:', checkError);
      return { success: false, error: 'Failed to check phone availability' };
    }

    if (existingUser) {
      return { success: false, error: 'Phone number is already in use by another account' };
    }

    // Send OTP to the phone number
    const { error: otpError } = await supabase.auth.signInWithOtp({
      phone: `+91${phone}`,
      options: {
        shouldCreateUser: false,
      },
    });

    if (otpError) {
      console.error('Error sending SMS OTP:', otpError);
      return { success: false, error: 'Failed to send verification code' };
    }

    return { 
      success: true, 
      requiresOTP: true,
      data: { phone }
    };
  } catch (error) {
    console.error('Unexpected error linking phone:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Verify phone OTP for linking
 */
export async function verifyPhoneOTP(phone: string, otp: string): Promise<SettingsServiceResponse> {
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Verify OTP
    const { error: verifyError } = await supabase.auth.verifyOtp({
      phone: `+91${phone}`,
      token: otp,
      type: 'sms',
    });

    if (verifyError) {
      console.error('Error verifying SMS OTP:', verifyError);
      return { success: false, error: 'Invalid verification code' };
    }

    // Update user phone
    const { error: updateError } = await supabase.auth.updateUser({
      phone: `+91${phone}`,
    });

    if (updateError) {
      console.error('Error updating phone after OTP:', updateError);
      return { success: false, error: updateError.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Unexpected error verifying phone OTP:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Update user password
 */
export async function updatePassword(data: PasswordUpdateData): Promise<SettingsServiceResponse> {
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Validate password complexity
    if (data.newPassword.length < 8) {
      return { success: false, error: 'Password must be at least 8 characters' };
    }

    if (!/(?=.*[a-z])/.test(data.newPassword)) {
      return { success: false, error: 'Password must contain at least one lowercase letter' };
    }

    if (!/(?=.*[A-Z])/.test(data.newPassword)) {
      return { success: false, error: 'Password must contain at least one uppercase letter' };
    }

    if (!/(?=.*\d)/.test(data.newPassword)) {
      return { success: false, error: 'Password must contain at least one number' };
    }

    if (!/(?=.*[@$!%*?&])/.test(data.newPassword)) {
      return { success: false, error: 'Password must contain at least one special character' };
    }

    if (data.newPassword !== data.confirmPassword) {
      return { success: false, error: 'Passwords do not match' };
    }

    // First verify current password by attempting to sign in
    if (user.email) {
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user.email,
        password: data.currentPassword,
      });

      if (signInError) {
        return { success: false, error: 'Current password is incorrect' };
      }
    }

    // Update password
    const { error: updateError } = await supabase.auth.updateUser({
      password: data.newPassword,
    });

    if (updateError) {
      console.error('Error updating password:', updateError);
      return { success: false, error: updateError.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Unexpected error updating password:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Send password reset email
 */
export async function sendPasswordResetEmail(email: string): Promise<SettingsServiceResponse> {
  try {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: 'dukancardapp://auth/reset-password',
    });

    if (error) {
      console.error('Error sending password reset email:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Unexpected error sending password reset email:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}
