/**
 * SortBottomSheet Component
 * A bottom sheet dialog for selecting sort options for products and business cards
 * Based on CategoryBottomSheetPicker pattern
 */

import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useMemo,
  useCallback,
} from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import BottomSheet, {
  BottomSheetView,
  BottomSheetFlatList,
} from "@gorhom/bottom-sheet";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/src/hooks/useTheme";
import {
  ProductSortOption,
  BusinessSortBy,
  ViewType,
} from "@/src/types/discovery";

interface SortBottomSheetProps {
  viewType: ViewType;
  productSortBy: ProductSortOption;
  businessSortBy: BusinessSortBy;
  onProductSortSelect: (sort: ProductSortOption) => void;
  onBusinessSortSelect: (sort: BusinessSortBy) => void;
}

export interface SortBottomSheetRef {
  present: () => void;
  dismiss: () => void;
}

// Sort options for products
const PRODUCT_SORT_OPTIONS: {
  value: ProductSortOption;
  label: string;
  group: string;
}[] = [
  { value: "newest", label: "Newest First", group: "Date" },
  { value: "name_asc", label: "Name (A-Z)", group: "Name" },
  { value: "name_desc", label: "Name (Z-A)", group: "Name" },
  { value: "price_low", label: "Price (Low to High)", group: "Price" },
  { value: "price_high", label: "Price (High to Low)", group: "Price" },
];

// Sort options for business cards
const BUSINESS_SORT_OPTIONS: {
  value: BusinessSortBy;
  label: string;
  group: string;
}[] = [
  { value: "created_desc", label: "Newest First", group: "Date" },
  { value: "created_asc", label: "Oldest First", group: "Date" },
  { value: "name_asc", label: "Name (A-Z)", group: "Name" },
  { value: "name_desc", label: "Name (Z-A)", group: "Name" },
  { value: "likes_desc", label: "Most Liked", group: "Popularity" },
  {
    value: "subscriptions_desc",
    label: "Most Subscribed",
    group: "Popularity",
  },
  { value: "rating_desc", label: "Highest Rated", group: "Rating" },
];

const SortBottomSheet = forwardRef<SortBottomSheetRef, SortBottomSheetProps>(
  (
    {
      viewType,
      productSortBy,
      businessSortBy,
      onProductSortSelect,
      onBusinessSortSelect,
    },
    ref
  ) => {
    const { colors, isDark } = useTheme();
    const bottomSheetRef = useRef<BottomSheet>(null);

    // Snap points for the bottom sheet - using 75% as default
    const snapPoints = useMemo(() => ["75%"], []);

    useImperativeHandle(ref, () => ({
      present: () => {
        bottomSheetRef.current?.expand();
      },
      dismiss: () => {
        bottomSheetRef.current?.close();
      },
    }));

    const handleSheetChanges = useCallback((index: number) => {
      // Handle sheet changes if needed
    }, []);

    const currentSortOptions =
      viewType === "products" ? PRODUCT_SORT_OPTIONS : BUSINESS_SORT_OPTIONS;
    const currentSortValue =
      viewType === "products" ? productSortBy : businessSortBy;

    const handleSortSelect = useCallback(
      (sortValue: string) => {
        if (viewType === "products") {
          onProductSortSelect(sortValue as ProductSortOption);
        } else {
          onBusinessSortSelect(sortValue as BusinessSortBy);
        }
        bottomSheetRef.current?.close();
      },
      [viewType, onProductSortSelect, onBusinessSortSelect]
    );

    // Flatten options for FlatList with group headers
    const flattenedOptions = useMemo(() => {
      const groupedOptions = currentSortOptions.reduce((acc, option) => {
        if (!acc[option.group]) {
          acc[option.group] = [];
        }
        acc[option.group].push(option);
        return acc;
      }, {} as Record<string, { value: string; label: string; group: string }[]>);

      const flattened: { type: "header" | "option"; data: any }[] = [];
      Object.entries(groupedOptions).forEach(([groupName, options]) => {
        flattened.push({ type: "header", data: { groupName } });
        options.forEach((option) => {
          flattened.push({ type: "option", data: option });
        });
      });
      return flattened;
    }, [currentSortOptions]);

    const styles = createStyles(colors);

    // Render item function for FlatList
    const renderItem = useCallback(
      ({ item }: { item: { type: "header" | "option"; data: any } }) => {
        if (item.type === "header") {
          return <Text style={styles.groupTitle}>{item.data.groupName}</Text>;
        }

        const option = item.data;
        const isSelected = currentSortValue === option.value;

        return (
          <TouchableOpacity
            style={[styles.option, isSelected && styles.selectedOption]}
            onPress={() => handleSortSelect(option.value)}
          >
            <Text
              style={[
                styles.optionText,
                isSelected && styles.selectedOptionText,
              ]}
            >
              {option.label}
            </Text>
            {isSelected && (
              <Ionicons name="checkmark" size={20} color="#C29D5B" />
            )}
          </TouchableOpacity>
        );
      },
      [currentSortValue, handleSortSelect, styles]
    );

    return (
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        enablePanDownToClose
        enableDynamicSizing={false}
        enableContentPanningGesture={false}
        enableHandlePanningGesture={true}
        backgroundStyle={{
          backgroundColor: isDark ? "#000000" : "#ffffff",
        }}
        handleIndicatorStyle={{
          backgroundColor: colors.textSecondary,
        }}
      >
        <BottomSheetView style={styles.container}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>
              Sort {viewType === "products" ? "Products" : "Business Cards"}
            </Text>
            <TouchableOpacity
              onPress={() => bottomSheetRef.current?.close()}
              style={styles.closeButton}
            >
              <Ionicons name="close" size={24} color={colors.textPrimary} />
            </TouchableOpacity>
          </View>

          {/* Sort Options with FlatList */}
          <BottomSheetFlatList
            data={flattenedOptions}
            renderItem={renderItem}
            keyExtractor={(item, index) =>
              item.type === "header"
                ? `header-${item.data.groupName}`
                : `option-${item.data.value}-${index}`
            }
            showsVerticalScrollIndicator={true}
            contentContainerStyle={styles.listContent}
            keyboardShouldPersistTaps="handled"
          />
        </BottomSheetView>
      </BottomSheet>
    );
  }
);

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      paddingHorizontal: 20,
      paddingTop: 16,
      paddingBottom: 12,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    title: {
      fontSize: 18,
      fontWeight: "600",
      color: colors.textPrimary,
    },
    closeButton: {
      padding: 4,
    },
    listContent: {
      paddingTop: 16,
      paddingBottom: 140, // Increased padding to ensure bottom items are accessible and clear of the nav bar
    },
    groupTitle: {
      fontSize: 14,
      fontWeight: "600",
      color: colors.textSecondary,
      marginTop: 24,
      marginBottom: 12,
      marginHorizontal: 16,
      textTransform: "uppercase",
      letterSpacing: 0.5,
    },
    option: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: 16,
      paddingHorizontal: 16,
      borderRadius: 12,
      marginBottom: 8,
      backgroundColor: colors.cardBackground,
      borderWidth: 1,
      borderColor: colors.border,
    },
    selectedOption: {
      backgroundColor: "#C29D5B20",
      borderColor: "#C29D5B",
    },
    optionText: {
      fontSize: 16,
      color: colors.textPrimary,
      flex: 1,
    },
    selectedOptionText: {
      color: "#C29D5B",
      fontWeight: "600",
    },
  });

SortBottomSheet.displayName = "SortBottomSheet";

export default SortBottomSheet;
