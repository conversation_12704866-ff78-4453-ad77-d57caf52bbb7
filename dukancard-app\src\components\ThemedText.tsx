import { Text, type TextProps } from 'react-native';

import { useThemeColor } from '@/src/hooks/useThemeColor';
import { createThemedTextStyles } from '@/styles/components/ThemedText-styles';

export type ThemedTextProps = TextProps & {
  lightColor?: string;
  darkColor?: string;
  type?: 'default' | 'title' | 'defaultSemiBold' | 'subtitle' | 'link';
};

export function ThemedText({
  style,
  lightColor,
  darkColor,
  type = 'default',
  ...rest
}: ThemedTextProps) {
  const color = useThemeColor({ light: lightColor, dark: darkColor }, 'text');
  const styles = createThemedTextStyles();

  return (
    <Text
      style={[
        styles[type],
        { color: type === 'link' ? undefined : color },
        style,
      ]}
      {...rest}
    />
  );
}


