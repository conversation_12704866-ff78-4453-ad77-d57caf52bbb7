import { useTheme } from '@/src/hooks/useTheme';
import type { RoleOption } from '@/src/types/auth';
import { Briefcase, ChevronRight, User } from 'lucide-react-native';
import React from 'react';
import {
  Pressable,
  StyleSheet,
  Text,
  View,
} from 'react-native';

interface RoleCardProps {
  role: RoleOption;
  selected: boolean;
  onPress: () => void;
  disabled?: boolean;
}

export function RoleCard({ role, selected, onPress, disabled = false }: RoleCardProps) {
  const theme = useTheme();

  const styles = StyleSheet.create({
    borderContainer: {
      borderRadius: 20,
      borderWidth: selected ? 2 : 1,
      borderColor: theme.colors.border,
      ... (selected ? theme.shadows.md : {}), // Apply shadow only when selected
    },
    container: {
      borderRadius: 18,
      backgroundColor: theme.isDark ? '#000000' : '#FFFFFF',
      paddingVertical: 24,
      paddingHorizontal: 20,
      opacity: disabled ? 0.6 : 1,
    },
    pressable: {
      borderRadius: 20,
    },
    content: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    leftSection: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
      maxWidth: '85%',
    },
    iconContainer: {
      width: 52,
      height: 52,
      borderRadius: 16,
      backgroundColor: theme.colors.primary + '15', // Gold background with opacity
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 18,
      flexShrink: 0,
      ...theme.shadows.sm, // Apply small shadow to icon container
    },
    textSection: {
      flex: 1,
      minWidth: 0,
    },
    title: {
      fontSize: 18,
      fontWeight: '700',
      color: theme.colors.textPrimary,
      marginBottom: 6,
      letterSpacing: -0.3,
    },
    description: {
      fontSize: 15,
      color: theme.colors.textSecondary,
      fontWeight: '500',
      lineHeight: 22,
      opacity: 0.9,
    },
    chevronIcon: {
      marginRight: 6,
      flexShrink: 0,
      opacity: 0.8,
    },
  });

  const getIcon = (iconName: string) => {
    const iconSize = 24;
    const iconColor = theme.colors.primary; // Use original gold color

    switch (iconName) {
      case 'user':
        return <User size={iconSize} color={iconColor} />;
      case 'briefcase':
        return <Briefcase size={iconSize} color={iconColor} />;
      default:
        return <User size={iconSize} color={iconColor} />;
    }
  };

  return (
    <Pressable
      style={styles.pressable}
      onPress={onPress}
      disabled={disabled}
      android_ripple={{
        color: theme.isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
        borderless: false,
      }}
    >
      <View style={styles.borderContainer}>
        <View style={styles.container}>
          <View style={styles.content}>
            <View style={styles.leftSection}>
              <View style={styles.iconContainer}>
                {getIcon(role.icon)}
              </View>

              <View style={styles.textSection}>
                <Text style={styles.title}>
                  {role.title}
                </Text>
                <Text style={styles.description}>
                  {role.description}
                </Text>
              </View>
            </View>

            <View style={styles.chevronIcon}>
              <ChevronRight
                size={20}
                color={theme.colors.textSecondary}
              />
            </View>
          </View>
        </View>
      </View>
    </Pressable>
  );
}
