import { useSlugValidation, SlugValidationState } from '@/hooks/useSlugValidation';
import React, { createContext, useContext, useEffect, useState } from 'react';
import { 
  OnboardingFormData, 
  saveOnboardingData, 
  getOnboardingData, 
  clearOnboardingData,
} from '@/backend/supabase/services/common/onboardingService';
import { useAuth } from './AuthContext';

// Types for onboarding context
export interface OnboardingContextType {
  // Form data
  formData: Partial<OnboardingFormData>;
  updateFormData: (data: Partial<OnboardingFormData>) => Promise<void>;
  clearFormData: () => Promise<void>;
  
  // Step management
  currentStep: number;
  totalSteps: number;
  setCurrentStep: (step: number) => void;
  nextStep: () => void;
  prevStep: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
  
  // Validation
  validateStep: (step: number) => Promise<{ isValid: boolean; errors: string[] }>;
  isStepValid: (step: number) => boolean;
  
  // Slug validation
  slugValidation: SlugValidationState;
  validateSlug: (slug: string) => void;
  
  // Loading states
  isLoading: boolean;
  isSaving: boolean;
  
  // Progress
  progress: number;
  completedSteps: number[];
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

// Step field mappings
const STEP_FIELDS: (keyof OnboardingFormData)[][] = [
  ['businessName', 'businessCategory', 'memberName'], // Step 1
  ['businessSlug', 'title', 'phone', 'email'], // Step 2
  ['addressLine', 'pincode', 'locality', 'city', 'state'], // Step 3
  ['planId'], // Step 4
];

export function OnboardingProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const [formData, setFormData] = useState<Partial<OnboardingFormData>>({});
  const [currentStep, setCurrentStep] = useState(1);
  const { validateSlug, ...slugValidation } = useSlugValidation();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);

  const totalSteps = 4;

  // Load existing data on mount
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        const existingData = await getOnboardingData();
        if (existingData && Object.keys(existingData).length > 0) {
          setFormData(existingData);
          
          // Pre-fill email from user if not already set
          if (user?.email && !existingData.email) {
            const updatedData = { ...existingData, email: user.email };
            setFormData(updatedData);
            await saveOnboardingData(updatedData);
          }
          
          // Determine current step based on completed data
          const step = determineCurrentStep(existingData);
          setCurrentStep(step);
          
          // Mark completed steps
          const completed = [];
          for (let i = 1; i < step; i++) {
            completed.push(i);
          }
          setCompletedSteps(completed);
        } else if (user?.email) {
          // Initialize with user email
          const initialData = { email: user.email };
          setFormData(initialData);
          await saveOnboardingData(initialData);
        }
      } catch (error) {
        console.error('Error loading onboarding data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (user) {
      loadData();
    }
  }, [user]);

  // Determine current step based on completed data
  const determineCurrentStep = (data: Partial<OnboardingFormData>): number => {
    for (let step = 1; step <= totalSteps; step++) {
      const stepFields = STEP_FIELDS[step - 1];
      const hasAllFields = stepFields.every(field => data[field]);
      if (!hasAllFields) {
        return step;
      }
    }
    return totalSteps; // All steps completed
  };

  // Update form data and save to storage
  const updateFormData = async (newData: Partial<OnboardingFormData>) => {
    setIsSaving(true);
    try {
      const updatedData = { ...formData, ...newData };
      setFormData(updatedData);
      await saveOnboardingData(updatedData);
      
      // Update completed steps
      const step = determineCurrentStep(updatedData);
      const completed = [];
      for (let i = 1; i < step; i++) {
        completed.push(i);
      }
      setCompletedSteps(completed);
    } catch (error) {
      console.error('Error updating form data:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Clear all form data
  const clearFormData = async () => {
    try {
      await clearOnboardingData();
      setFormData({});
      setCurrentStep(1);
      setCompletedSteps([]);
    } catch (error) {
      console.error('Error clearing form data:', error);
    }
  };

  // Step navigation
  const nextStep = () => {
    if (currentStep < totalSteps) {
      const newStep = currentStep + 1;
      setCurrentStep(newStep);
      
      // Mark current step as completed
      if (!completedSteps.includes(currentStep)) {
        setCompletedSteps(prev => [...prev, currentStep]);
      }
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const setStep = (step: number) => {
    if (step >= 1 && step <= totalSteps) {
      setCurrentStep(step);
    }
  };

  // Validation
  const validateStep = async (step: number): Promise<{ isValid: boolean; errors: string[] }> => {
    const errors: string[] = [];
    const stepFields = STEP_FIELDS[step - 1];

    for (const field of stepFields) {
      const value = formData[field];
      
      if (!value || (typeof value === 'string' && value.trim() === '')) {
        errors.push(`${field} is required`);
        continue;
      }

      // Specific validations
      switch (field) {
        case 'email':
          if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value as string)) {
            errors.push('Please enter a valid email address');
          }
          break;
        case 'phone':
          if (!/^\d{10}$/.test((value as string).replace(/\D/g, ''))) {
            errors.push('Please enter a valid 10-digit phone number');
          }
          break;
        case 'pincode':
          if (!/^\d{6}$/.test(value as string)) {
            errors.push('Please enter a valid 6-digit pincode');
          }
          break;
        case 'businessSlug':
          if (!/^[a-z0-9-]+$/.test(value as string)) {
            errors.push('Business slug can only contain lowercase letters, numbers, and hyphens');
          } else if ((value as string).length < 3) {
            errors.push('Business slug must be at least 3 characters long');
          } else if (slugValidation.isAvailable === false) {
            errors.push('This business slug is not available');
          }
          break;
      }
    }

    return { isValid: errors.length === 0, errors };
  };

  const isStepValid = (step: number): boolean => {
    const stepFields = STEP_FIELDS[step - 1];
    return stepFields.every(field => {
      const value = formData[field];
      return value && (typeof value !== 'string' || value.trim() !== '');
    });
  };

  // Computed properties
  const isFirstStep = currentStep === 1;
  const isLastStep = currentStep === totalSteps;
  const progress = (currentStep / totalSteps) * 100;

  const value: OnboardingContextType = {
    formData,
    updateFormData,
    clearFormData,
    currentStep,
    totalSteps,
    setCurrentStep: setStep,
    nextStep,
    prevStep,
    isFirstStep,
    isLastStep,
    validateStep,
    isStepValid,
    slugValidation,
    validateSlug,
    isLoading,
    isSaving,
    progress,
    completedSteps,
  };

  return (
    <OnboardingContext.Provider value={value}>
      {children}
    </OnboardingContext.Provider>
  );
}

export function useOnboarding() {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
}
