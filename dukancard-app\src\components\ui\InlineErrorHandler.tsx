import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import {
  WifiOff,
  RefreshCw,
  AlertTriangle,
  X,
} from 'lucide-react-native';
import { useTheme } from '@/src/hooks/useTheme';
import { AppError } from '@/src/utils/errorHandling';

interface InlineErrorHandlerProps {
  error: AppError | null;
  onRetry?: () => void;
  onDismiss?: () => void;
  isRetrying?: boolean;
  showRetry?: boolean;
  showDismiss?: boolean;
  style?: any;
}

export const InlineErrorHandler: React.FC<InlineErrorHandlerProps> = ({
  error,
  onRetry,
  onDismiss,
  isRetrying = false,
  showRetry = true,
  showDismiss = true,
  style,
}) => {
  const theme = useTheme();

  if (!error) return null;

  const getErrorColor = () => {
    switch (error.type) {
      case 'network':
        return theme.colors.warning;
      case 'server':
      case 'unauthorized':
        return theme.colors.error;
      default:
        return theme.colors.error;
    }
  };

  const getErrorIcon = () => {
    switch (error.type) {
      case 'network':
        return WifiOff;
      default:
        return AlertTriangle;
    }
  };

  const ErrorIcon = getErrorIcon();
  const errorColor = getErrorColor();

  const styles = StyleSheet.create({
    container: {
      backgroundColor: errorColor + '10',
      borderLeftWidth: 3,
      borderLeftColor: errorColor,
      borderRadius: theme.borderRadius.sm,
      padding: theme.spacing.sm,
      marginVertical: theme.spacing.xs,
      flexDirection: 'row',
      alignItems: 'flex-start',
      gap: theme.spacing.sm,
    },
    iconContainer: {
      marginTop: 2,
    },
    contentContainer: {
      flex: 1,
    },
    title: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: theme.typography.fontWeight.semibold,
      color: errorColor,
      marginBottom: theme.spacing.xs,
    },
    message: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      lineHeight: theme.typography.lineHeight.normal * theme.typography.fontSize.sm,
      marginBottom: theme.spacing.sm,
    },
    actionsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.sm,
    },
    retryButton: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.xs,
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      backgroundColor: errorColor,
      borderRadius: theme.borderRadius.sm,
    },
    retryButtonDisabled: {
      opacity: 0.6,
    },
    retryButtonText: {
      fontSize: theme.typography.fontSize.xs,
      color: '#FFFFFF', // Changed from theme.colors.white
      fontWeight: theme.typography.fontWeight.medium,
    },
    dismissButton: {
      padding: theme.spacing.xs,
    },
    networkIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.xs,
      backgroundColor: theme.colors.warning + '20',
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.borderRadius.sm,
      marginBottom: theme.spacing.xs,
    },
    networkIndicatorText: {
      fontSize: theme.typography.fontSize.xs,
      color: theme.colors.warning,
      fontWeight: theme.typography.fontWeight.medium,
    },
  });

  return (
    <View style={[styles.container, style]}>
      {/* Error Icon */}
      <View style={styles.iconContainer}>
        <ErrorIcon size={16} color={errorColor} />
      </View>

      {/* Content */}
      <View style={styles.contentContainer}>
        {/* Network Status for Network Errors */}
        {error.type === 'network' && (
          <View style={styles.networkIndicator}>
            <WifiOff size={12} color={theme.colors.warning} />
            <Text style={styles.networkIndicatorText}>
              Connection Issue
            </Text>
          </View>
        )}

        {/* Error Title */}
        <Text style={styles.title}>{error.title}</Text>

        {/* Error Message */}
        <Text style={styles.message}>{error.message}</Text>

        {/* Actions */}
        <View style={styles.actionsContainer}>
          {/* Retry Button */}
          {showRetry && onRetry && (
            <TouchableOpacity
              style={[
                styles.retryButton,
                isRetrying && styles.retryButtonDisabled,
              ]}
              onPress={onRetry}
              disabled={isRetrying}
              activeOpacity={0.8}
            >
              {isRetrying ? (
                <ActivityIndicator size="small" color={'#FFFFFF'} />
              ) : (
                <RefreshCw size={12} color={'#FFFFFF'} />
              )}
              <Text style={styles.retryButtonText}>
                {isRetrying ? 'Retrying...' : 'Retry'}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Dismiss Button */}
      {showDismiss && onDismiss && (
        <TouchableOpacity
          style={styles.dismissButton}
          onPress={onDismiss}
          activeOpacity={0.8}
        >
          <X size={16} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      )}
    </View>
  );
};

export default InlineErrorHandler;
