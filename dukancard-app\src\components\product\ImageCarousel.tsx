import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import { ZoomIn } from 'lucide-react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import Carousel, { ICarouselInstance } from 'react-native-reanimated-carousel';
import FullScreenImageViewer from '../business/FullScreenImageViewer';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface ImageCarouselProps {
  images: string[];
  featuredImageIndex?: number;
  productName?: string;
  onImagePress?: (imageUrl: string, index: number) => void;
}

export default function ImageCarousel({
  images,
  featuredImageIndex = 0,
  productName = 'Product',
  onImagePress,
}: ImageCarouselProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const [currentImageIndex, setCurrentImageIndex] = useState(featuredImageIndex);
  const [imageLoading, setImageLoading] = useState<Record<string, boolean>>({});
  const [imageError, setImageError] = useState<Record<string, boolean>>({});
  const [isZoomModalOpen, setIsZoomModalOpen] = useState(false);

  const mainCarouselRef = useRef<ICarouselInstance>(null);
  const thumbnailScrollRef = useRef<FlatList<string>>(null);

  // Colors
  const backgroundColor = isDark ? '#000000' : '#FFFFFF';
  const cardBackgroundColor = isDark ? '#1F1F1F' : '#F8F9FA';
  const borderColor = isDark ? '#333333' : '#E5E7EB';
  const textColor = isDark ? '#FFFFFF' : '#000000';
  const subtitleColor = isDark ? '#9CA3AF' : '#6B7280';

  // Ensure we have valid images array
  const validImages = images && images.length > 0 ? images : [];
  const hasMultipleImages = validImages.length > 1;

  useEffect(() => {
    // Scroll to featured image on mount
    if (mainCarouselRef.current && hasMultipleImages) {
      setTimeout(() => {
        mainCarouselRef.current?.scrollTo({
          index: currentImageIndex,
          animated: false,
        });
      }, 100);
    }
  }, [currentImageIndex, hasMultipleImages]);

  const handleCarouselSnapToItem = (index: number) => {
    if (index !== currentImageIndex && index >= 0 && index < validImages.length) {
      setCurrentImageIndex(index);

      // Scroll thumbnail to center the selected image
      if (thumbnailScrollRef.current) {
        const thumbnailWidth = 80;
        const thumbnailSpacing = 8;
        const totalThumbnailWidth = thumbnailWidth + thumbnailSpacing;
        const scrollToX = Math.max(0, index * totalThumbnailWidth - (screenWidth / 2) + (thumbnailWidth / 2));

        thumbnailScrollRef.current.scrollToOffset({
          offset: scrollToX,
          animated: true,
        });
      }
    }
  };

  const handleThumbnailPress = (index: number) => {
    setCurrentImageIndex(index);
    mainCarouselRef.current?.scrollTo({
      index: index,
      animated: true,
    });
  };

  const handleImagePress = (imageUrl: string, index: number) => {
    if (onImagePress) {
      onImagePress(imageUrl, index);
    } else {
      setIsZoomModalOpen(true);
    }
  };

  const handleImageLoad = (imageUrl: string) => {
    setImageLoading(prev => ({ ...prev, [imageUrl]: false }));
  };

  const handleImageError = (imageUrl: string) => {
    setImageError(prev => ({ ...prev, [imageUrl]: true }));
    setImageLoading(prev => ({ ...prev, [imageUrl]: false }));
  };

  if (validImages.length === 0) {
    return (
      <View style={{
        width: screenWidth,
        height: screenWidth, // Square aspect ratio to match updated single product screen
        backgroundColor: cardBackgroundColor,
        justifyContent: 'center',
        alignItems: 'center',
      }}>
        <Text style={{ color: subtitleColor, fontSize: 16 }}>No images available</Text>
      </View>
    );
  }

  const renderMainImage = ({ item: imageUrl, index }: { item: string; index: number }) => (
    <TouchableOpacity
      style={{
        width: screenWidth,
        height: screenWidth, // Square aspect ratio to match updated single product screen
        position: 'relative',
      }}
      onPress={() => handleImagePress(imageUrl, index)}
      activeOpacity={0.9}
    >
      {!imageError[imageUrl] ? (
        <Image
          source={{ uri: imageUrl }}
          style={{
            width: '100%',
            height: '100%',
            resizeMode: 'cover',
          }}
          onLoadStart={() => setImageLoading(prev => ({ ...prev, [imageUrl]: true }))}
          onLoad={() => handleImageLoad(imageUrl)}
          onError={() => handleImageError(imageUrl)}
        />
      ) : (
        <View style={{
          width: '100%',
          height: '100%',
          backgroundColor: cardBackgroundColor,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
          <Text style={{ color: subtitleColor }}>Image not available</Text>
        </View>
      )}
      
      {imageLoading[imageUrl] && (
        <View style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'rgba(0, 0, 0, 0.3)',
        }}>
          <ActivityIndicator size="large" color="#D4AF37" />
        </View>
      )}

      {/* Zoom indicator */}
      <View style={{
        position: 'absolute',
        top: 12,
        right: 12,
        backgroundColor: 'rgba(0, 0, 0, 0.6)',
        borderRadius: 20,
        padding: 8,
      }}>
        <ZoomIn size={16} color="#FFFFFF" />
      </View>
    </TouchableOpacity>
  );

  const getThumbnailWidth = () => {
    // Calculate width for 5-grid layout with proper spacing
    const containerPadding = 32; // 16px on each side
    const spacing = 8 * 4; // 8px spacing between 5 items (4 gaps)
    return (screenWidth - containerPadding - spacing) / 5;
  };

  const renderThumbnail = ({ item: imageUrl, index }: { item: string; index: number }) => {
    const thumbnailWidth = getThumbnailWidth();

    return (
      <TouchableOpacity
        style={{
          width: thumbnailWidth,
          height: thumbnailWidth,
          marginRight: index < validImages.length - 1 ? 8 : 0,
          borderRadius: 8,
          borderWidth: 2,
          borderColor: index === currentImageIndex ? '#D4AF37' : 'transparent',
          overflow: 'hidden',
        }}
        onPress={() => handleThumbnailPress(index)}
        activeOpacity={0.7}
      >
      {!imageError[imageUrl] ? (
        <Image
          source={{ uri: imageUrl }}
          style={{
            width: '100%',
            height: '100%',
            resizeMode: 'cover',
          }}
          onLoad={() => handleImageLoad(imageUrl)}
          onError={() => handleImageError(imageUrl)}
        />
      ) : (
        <View style={{
          width: '100%',
          height: '100%',
          backgroundColor: cardBackgroundColor,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
          <Text style={{ color: subtitleColor, fontSize: 10 }}>N/A</Text>
        </View>
      )}
    </TouchableOpacity>
  );
  };

  return (
    <View>
      {/* Main Image Carousel */}
      <Carousel
        ref={mainCarouselRef}
        width={screenWidth}
        height={screenWidth}
        data={validImages}
        renderItem={({ item: imageUrl, index }: { item: string; index: number }) => renderMainImage({ item: imageUrl, index })}
        onSnapToItem={handleCarouselSnapToItem}
        mode="parallax"
        modeConfig={{
          parallaxScrollingScale: 0.9,
          parallaxScrollingOffset: 50,
        }}
        defaultIndex={currentImageIndex}
        loop={false}
        autoPlay={false}
        scrollAnimationDuration={300}
      />

      {/* Image Counter */}
      {hasMultipleImages && (
        <View style={{
          position: 'absolute',
          bottom: 12,
          left: 12,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          borderRadius: 16,
          paddingHorizontal: 12,
          paddingVertical: 6,
        }}>
          <Text style={{ color: '#FFFFFF', fontSize: 12, fontWeight: '500' }}>
            {currentImageIndex + 1} / {validImages.length}
          </Text>
        </View>
      )}

      {/* Thumbnail Grid */}
      {hasMultipleImages && (
        <View style={{ marginTop: 12, paddingHorizontal: 16 }}>
          <View style={{
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'center',
          }}>
            {validImages.slice(0, 5).map((imageUrl, index) => (
              <React.Fragment key={`thumbnail-${index}-${imageUrl}`}>
                {renderThumbnail({ item: imageUrl, index })}
              </React.Fragment>
            ))}
          </View>
        </View>
      )}

      {/* Full Screen Image Viewer with Pinch-to-Zoom */}
      <FullScreenImageViewer
        visible={isZoomModalOpen}
        images={validImages.map((url, index) => ({ id: index.toString(), url }))}
        initialIndex={currentImageIndex}
        onClose={() => setIsZoomModalOpen(false)}
      />
    </View>
  );
}
