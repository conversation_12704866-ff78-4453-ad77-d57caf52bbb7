import { PostSkeleton } from "@/src/components/feed/PostSkeleton";
import { UnifiedFeedList } from "@/src/components/feed/UnifiedFeedList";
import { DashboardLayout } from "@/src/components/shared/layout/DashboardLayout";

import { ErrorState } from "@/src/components/ui/ErrorState";
import { useAuth } from "@/src/contexts/AuthContext";
import { useColorScheme } from "@/src/hooks/useColorScheme";
import {
  getUnifiedFeedPostsWithAuthors,
  UnifiedPost,
} from "@/lib/actions/posts/unifiedFeed";

import { supabase } from "@/lib/supabase";
import { FeedFilterType } from "@/lib/types/posts";
import { handleNetworkError, logError } from "@/src/utils/errorHandling";
import { useCallback, useEffect, useState } from "react";
import { StyleSheet, View } from "react-native";

export default function BusinessFeedScreen() {
  const { user } = useAuth();
  const colorScheme = useColorScheme();

  const [initialPosts, setInitialPosts] = useState<UnifiedPost[]>([]);
  const [initialHasMore, setInitialHasMore] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<any>(null);
  const [userLocation, setUserLocation] = useState<{
    citySlug?: string;
    stateSlug?: string;
    localitySlug?: string;
    pincode?: string;
  }>({});

  // Use display name from user metadata
  const businessName = user?.user_metadata?.name || "Business User";

  // Theme colors - use pure black/white for feed
  const backgroundColor = colorScheme === "dark" ? "#000000" : "#FFFFFF";

  // Load user locaton context like Next.js version
  const loadUserLocation = useCallback(async () => {
    if (!user) return;

    try {
      // Get the user's city from subscribed businesses (fallback method)
      const { data: subscriptions } = await supabase
        .from("subscriptions")
        .select(
          `
            business_profiles (
              city_slug,
              state_slug,
              locality_slug,
              pincode
            )
          `
        )
        .eq("user_id", user.id)
        .limit(1);

      let locationFromSubscriptions;
      if (
        subscriptions &&
        subscriptions.length > 0 &&
        subscriptions[0].business_profiles
      ) {
        const profile = subscriptions[0].business_profiles;
        if (typeof profile === "object" && profile !== null) {
          locationFromSubscriptions = {
            citySlug:
              "city_slug" in profile
                ? (profile.city_slug as string)
                : undefined,
            stateSlug:
              "state_slug" in profile
                ? (profile.state_slug as string)
                : undefined,
            localitySlug:
              "locality_slug" in profile
                ? (profile.locality_slug as string)
                : undefined,
            pincode:
              "pincode" in profile ? (profile.pincode as string) : undefined,
          };
        }
      }

      // Try to get user's location from both customer and business profiles
      const [customerProfile, businessProfile] = await Promise.all([
        supabase
          .from("customer_profiles")
          .select("city_slug, state_slug, locality_slug, pincode")
          .eq("id", user.id)
          .single(),
        supabase
          .from("business_profiles")
          .select("city_slug, state_slug, locality_slug, pincode")
          .eq("id", user.id)
          .single(),
      ]);

      // Use customer profile first, then business profile, then subscription location
      const profileLocationData = customerProfile.data || businessProfile.data;

      if (profileLocationData) {
        setUserLocation({
          citySlug: profileLocationData.city_slug,
          stateSlug: profileLocationData.state_slug,
          localitySlug: profileLocationData.locality_slug,
          pincode: profileLocationData.pincode,
        });
      } else if (locationFromSubscriptions) {
        setUserLocation(locationFromSubscriptions);
      }
    } catch (err) {
      console.error("Error loading user location:", err);
      // Continue without location context
    }
  }, [user]);

  const loadInitialFeed = useCallback(async () => {
    if (!user) return;

    try {
      setError(null);

      // Use smart feed as the default filter
      const initialFilter: FeedFilterType = "smart";

      // No cache available, show loading and fetch fresh data
      setLoading(true);

      // Get initial posts using the unified smart feed algorithm with user location
      const initialFeedResult = await getUnifiedFeedPostsWithAuthors({
        filter: initialFilter,
        page: 1,
        limit: 10,
        city_slug: userLocation.citySlug,
        state_slug: userLocation.stateSlug,
        locality_slug: userLocation.localitySlug,
        pincode: userLocation.pincode,
      });

      if (initialFeedResult.success && initialFeedResult.data) {
        setInitialPosts(initialFeedResult.data.items);
        setInitialHasMore(initialFeedResult.data.hasMore);
      } else {
        const appError = handleNetworkError(
          new Error(initialFeedResult.error || "Failed to load feed")
        );
        setError(appError);
        logError(appError, "BusinessFeedScreen.loadInitialFeed");
      }
    } catch (err) {
      console.error("Error loading initial feed:", err);
      const appError = handleNetworkError(err);
      setError(appError);
      logError(appError, "BusinessFeedScreen.loadInitialFeed");
    } finally {
      setLoading(false);
    }
  }, [user, userLocation]);

  useEffect(() => {
    loadUserLocation();
  }, [loadUserLocation]);

  useEffect(() => {
    if (user) {
      loadInitialFeed();
    }
  }, [loadInitialFeed, user]);

  if (loading) {
    return (
      <DashboardLayout userName={businessName} showNotifications={true}>
        <View style={[styles.loadingContainer, { backgroundColor }]}>
          {/* Show skeleton loaders for better UX */}
          <PostSkeleton index={0} showImage={true} />
          <PostSkeleton index={1} showImage={false} />
          <PostSkeleton index={2} showImage={true} showProducts={true} />
        </View>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout userName={businessName} showNotifications={true}>
        <ErrorState
          type={error.type || "generic"}
          title={error.title}
          message={error.message}
          onRetry={loadInitialFeed}
          showRetry={true}
        />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      userName={businessName}
      showNotifications={true}
      fullWidth={true}
    >
      <UnifiedFeedList
        initialPosts={initialPosts}
        initialHasMore={initialHasMore}
        userName={businessName}
        userType="business"
        citySlug={userLocation.citySlug}
        stateSlug={userLocation.stateSlug}
        localitySlug={userLocation.localitySlug}
        pincode={userLocation.pincode}
      />
    </DashboardLayout>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
  },
});
