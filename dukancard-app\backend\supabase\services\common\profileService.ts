import { supabase } from "@/lib/supabase";

export interface ProfileUpdateData {
  name?: string;
  email?: string;
  phone?: string;
}

export interface AddressUpdateData {
  address?: string;
  pincode: string;
  city: string;
  state: string;
  locality: string;
}

export interface ProfileServiceResponse {
  success: boolean;
  error?: string;
  data?: any;
}

export interface CustomerProfile {
  id: string;
  name: string | null;
  email: string | null;
  phone: string | null;
  avatar_url: string | null;
  address: string | null;
  pincode: string | null;
  city: string | null;
  state: string | null;
  locality: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Update customer profile information
 */
export async function updateCustomerProfile(
  data: ProfileUpdateData
): Promise<ProfileServiceResponse> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: "Not authenticated" };
    }

    const updateData: Partial<CustomerProfile> = {};

    if (data.name !== undefined) {
      updateData.name = data.name.trim() || null;
    }

    if (data.email !== undefined) {
      updateData.email = data.email.trim() || null;
    }

    if (data.phone !== undefined) {
      updateData.phone = data.phone.trim() || null;
    }

    const { data: result, error } = await supabase
      .from("customer_profiles")
      .update(updateData)
      .eq("id", user.id)
      .select()
      .single();

    if (error) {
      console.error("Error updating customer profile:", error);
      return { success: false, error: error.message };
    }

    return { success: true, data: result };
  } catch (error) {
    console.error("Unexpected error updating profile:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Update customer address information
 */
export async function updateCustomerAddress(
  data: AddressUpdateData
): Promise<ProfileServiceResponse> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: "Not authenticated" };
    }

    // Validate pincode format
    if (!/^\d{6}$/.test(data.pincode)) {
      return { success: false, error: "Invalid pincode format" };
    }

    // GPS coordinates should be provided directly - no fallback to pincodes table
    const latitude: number | null = null;
    const longitude: number | null = null;

    const { data: result, error } = await supabase
      .from("customer_profiles")
      .update({
        address: data.address?.trim() || null,
        pincode: data.pincode.trim(),
        city: data.city.trim(),
        state: data.state.trim(),
        locality: data.locality.trim(),
        latitude: latitude,
        longitude: longitude,
      })
      .eq("id", user.id)
      .select()
      .single();

    if (error) {
      console.error("Error updating customer address:", error);
      return { success: false, error: error.message };
    }

    return { success: true, data: result };
  } catch (error) {
    console.error("Unexpected error updating address:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Get customer profile information with caching support
 */
export async function getCustomerProfile(): Promise<ProfileServiceResponse> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: "Not authenticated" };
    }

    // Fetch fresh data from database
    const { data: profile, error } = await supabase
      .from("customer_profiles")
      .select("*")
      .eq("id", user.id)
      .single();

    if (error) {
      console.error("Error fetching customer profile:", error);
      // Handle specific error codes
      if (error.code === "PGRST116") {
        return { success: false, error: "Profile not found" };
      }
      return { success: false, error: error.message };
    }

    return { success: true, data: profile };
  } catch (error) {
    console.error("Unexpected error fetching profile:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Get business profile information with caching support
 */
export async function getBusinessProfile(): Promise<ProfileServiceResponse> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: "Not authenticated" };
    }

    // Fetch fresh data from database
    const { data: profile, error } = await supabase
      .from("business_profiles")
      .select("*")
      .eq("id", user.id)
      .single();

    if (error) {
      console.error("Error fetching business profile:", error);
      // Handle specific error codes
      if (error.code === "PGRST116") {
        return { success: false, error: "Profile not found" };
      }
      return { success: false, error: error.message };
    }

    return { success: true, data: profile };
  } catch (error) {
    console.error("Unexpected error fetching profile:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Update customer avatar URL
 */
export async function updateCustomerAvatar(
  avatarUrl: string
): Promise<ProfileServiceResponse> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: "Not authenticated" };
    }

    const { data: result, error } = await supabase
      .from("customer_profiles")
      .update({ avatar_url: avatarUrl })
      .eq("id", user.id)
      .select()
      .single();

    if (error) {
      console.error("Error updating customer avatar:", error);
      return { success: false, error: error.message };
    }

    return { success: true, data: result };
  } catch (error) {
    console.error("Unexpected error updating avatar:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Validate pincode against Supabase pincodes table
 */
export async function validatePincode(
  pincode: string
): Promise<ProfileServiceResponse> {
  try {
    if (!/^\d{6}$/.test(pincode)) {
      return { success: false, error: "Invalid pincode format" };
    }

    const { data: pincodeData, error } = await supabase
      .from("pincodes")
      .select("pincode, city, state, locality")
      .eq("pincode", pincode)
      .single();

    if (error) {
      if (error.code === "PGRST116") {
        return { success: false, error: "Pincode not found" };
      }
      console.error("Error validating pincode:", error);
      return { success: false, error: error.message };
    }

    return { success: true, data: pincodeData };
  } catch (error) {
    console.error("Unexpected error validating pincode:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Validate if pincode belongs to the specified city
 */
export async function validatePincodeForCity(
  pincode: string,
  city: string
): Promise<ProfileServiceResponse> {
  try {
    if (!/^\d{6}$/.test(pincode)) {
      return { success: false, error: "Invalid pincode format" };
    }

    if (!city || city.trim().length === 0) {
      return { success: false, error: "City is required" };
    }

    // Normalize city name for comparison (case-insensitive)
    const normalizedCity = city.trim().toLowerCase();

    const { data: pincodeData, error } = await supabase
      .from("pincodes")
      .select("pincode, city, state, locality")
      .eq("pincode", pincode);

    if (error) {
      console.error("Error validating pincode for city:", error);
      return { success: false, error: error.message };
    }

    if (!pincodeData || pincodeData.length === 0) {
      return { success: false, error: "Pincode not found" };
    }

    // Check if any of the pincode entries match the specified city
    const cityMatch = pincodeData.some(
      (entry: any) => entry.city && entry.city.toLowerCase() === normalizedCity
    );

    if (!cityMatch) {
      return {
        success: false,
        error: `Pincode ${pincode} does not belong to ${city}`,
      };
    }

    return { success: true, data: pincodeData };
  } catch (error) {
    console.error("Unexpected error validating pincode for city:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Check if customer profile is complete
 */
export async function isProfileComplete(): Promise<ProfileServiceResponse> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: "Not authenticated" };
    }

    const { data: profile, error } = await supabase
      .from("customer_profiles")
      .select("name, pincode, city, state, locality")
      .eq("id", user.id)
      .single();

    if (error) {
      console.error("Error checking profile completeness:", error);
      return { success: false, error: error.message };
    }

    const isComplete = !!(
      profile.name &&
      profile.pincode &&
      profile.city &&
      profile.state &&
      profile.locality
    );

    return {
      success: true,
      data: {
        isComplete,
        profile,
      },
    };
  } catch (error) {
    console.error("Unexpected error checking profile completeness:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}
