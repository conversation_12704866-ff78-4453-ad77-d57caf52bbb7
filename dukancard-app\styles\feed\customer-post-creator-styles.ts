import { StyleSheet } from 'react-native';

// Theme constants (matching business post creator)
const spacing = {
  sm: 8,
  md: 16,
  lg: 24,
};

const borderRadius = {
  md: 8,
  lg: 12,
};

export const createCustomerPostCreatorStyles = () => {
  return StyleSheet.create({
    container: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderRadius: 0,
      borderWidth: 0,
    },
    collapsedContent: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    avatar: {
      width: 48,
      height: 48,
      borderRadius: 24,
      borderWidth: 1.5,
      borderColor: '#D4AF37', // Golden border to match brand
      marginRight: 12,
    },
    avatarPlaceholder: {
      width: 48,
      height: 48,
      borderRadius: 24,
      borderWidth: 1.5,
      borderColor: '#D4AF37', // Golden border to match brand
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12,
    },
    avatarText: {
      color: '#FFFFFF',
      fontSize: 18,
      fontWeight: 'bold',
    },
    textContainer: {
      flex: 1,
    },
    placeholderText: {
      fontSize: 16,
      fontWeight: '400',
    },

  });
};
