import { UserRoleStatus } from '@/src/contexts/AuthContext';

/**
 * Navigation utilities for handling authentication-based routing
 */

export interface NavigationState {
  isAuthenticated: boolean;
  roleStatus: UserRoleStatus | null;
  currentSegments: string[];
}

/**
 * Determines the correct redirect path based on user authentication and role status
 */
export function getRedirectPath(state: NavigationState): string | null {
  const { isAuthenticated, roleStatus, currentSegments } = state;

  const inAuthGroup = currentSegments[0] === '(auth)';
  const inOnboardingGroup = currentSegments[0] === '(onboarding)';
  const inDashboardGroup = currentSegments[0] === '(dashboard)';
  const inTabsGroup = currentSegments[0] === '(tabs)';
  const isIndexRoute = currentSegments.length === 0 || currentSegments[0] === 'index';

  // Not authenticated - redirect to login unless already in auth
  if (!isAuthenticated) {
    return inAuthGroup ? null : '/(auth)/login';
  }

  // Authenticated but no role status - stay put or go to login if error
  if (!roleStatus) {
    return null;
  }

  // User needs to select a role
  if (roleStatus.needsRoleSelection) {
    const currentAuthRoute = currentSegments.length > 1 ? currentSegments[1] : '';
    // Allow access to onboarding if user is coming from choose-role (similar to Next.js middleware)
    if (inOnboardingGroup) {
      return null; // Allow onboarding access for role selection flow
    }
    return (!inAuthGroup || currentAuthRoute !== 'choose-role') ? '/(auth)/choose-role' : null;
  }

  // Business user needs onboarding
  if (roleStatus.role === 'business' && roleStatus.needsOnboarding) {
    return !inOnboardingGroup ? '/(onboarding)/business-details' : null;
  }

  // Customer user needs profile completion
  if (roleStatus.role === 'customer' && roleStatus.needsProfileCompletion) {
    return currentSegments.join('/') !== '(auth)/complete-profile' ? '/(auth)/complete-profile' : null;
  }

  // User completed setup - redirect from auth/onboarding/index to dashboard
  if (inAuthGroup || inOnboardingGroup || inTabsGroup || isIndexRoute) {
    if (roleStatus.role === 'customer') {
      return '/(dashboard)/customer';
    } else if (roleStatus.role === 'business') {
      return '/(dashboard)/business';
    }
  }

  // Prevent access to wrong dashboard
  if (inDashboardGroup && currentSegments.length > 1) {
    const currentDashboard = currentSegments[1];
    if (roleStatus.role === 'customer' && currentDashboard !== 'customer') {
      return '/(dashboard)/customer';
    } else if (roleStatus.role === 'business' && currentDashboard !== 'business') {
      return '/(dashboard)/business';
    }
  }

  return null;
}

/**
 * Checks if the current user has completed onboarding
 */
export function checkOnboardingStatus(roleStatus: UserRoleStatus | null): {
  needsRoleSelection: boolean;
  needsOnboarding: boolean;
  isComplete: boolean;
} {
  if (!roleStatus) {
    return {
      needsRoleSelection: true,
      needsOnboarding: false,
      isComplete: false,
    };
  }

  return {
    needsRoleSelection: roleStatus.needsRoleSelection,
    needsOnboarding: roleStatus.needsOnboarding,
    isComplete: !roleStatus.needsRoleSelection && !roleStatus.needsOnboarding,
  };
}

/**
 * Handles authentication-based redirects with proper error handling
 */
export function handleAuthRedirect(
  redirectPath: string | null,
  router: { replace: (path: string) => void },
  options: { 
    onRedirect?: (path: string) => void;
    onError?: (error: Error) => void;
  } = {}
): void {
  if (!redirectPath) return;

  try {
    router.replace(redirectPath);
    options.onRedirect?.(redirectPath);
  } catch (error) {
    console.error('Navigation: Error during redirect:', error);
    options.onError?.(error as Error);
  }
}

/**
 * Validates if a user can access a specific route
 */
export function canAccessRoute(
  route: string,
  state: NavigationState
): { canAccess: boolean; reason?: string } {
  const { isAuthenticated, roleStatus } = state;

  // Public routes (auth screens)
  if (route.startsWith('/(auth)/')) {
    if (isAuthenticated && roleStatus && !roleStatus.needsRoleSelection) {
      return { 
        canAccess: false, 
        reason: 'Authenticated users should not access auth screens' 
      };
    }
    return { canAccess: true };
  }

  // Protected routes require authentication
  if (!isAuthenticated) {
    return { 
      canAccess: false, 
      reason: 'Authentication required' 
    };
  }

  // Role selection required
  if (roleStatus?.needsRoleSelection && !route.includes('choose-role')) {
    return { 
      canAccess: false, 
      reason: 'Role selection required' 
    };
  }

  // Onboarding required for business users
  if (roleStatus?.needsOnboarding && !route.startsWith('/(onboarding)/')) {
    return { 
      canAccess: false, 
      reason: 'Onboarding required' 
    };
  }

  // Dashboard access validation
  if (route.startsWith('/(dashboard)/')) {
    const dashboardType = route.split('/')[2]; // customer or business
    if (roleStatus?.role && roleStatus.role !== dashboardType) {
      return { 
        canAccess: false, 
        reason: `Access denied: User role is ${roleStatus.role} but trying to access ${dashboardType} dashboard` 
      };
    }
  }

  return { canAccess: true };
}

/**
 * Gets the appropriate dashboard route for a user role
 */
export function getDashboardRoute(role: 'customer' | 'business' | null): string | null {
  switch (role) {
    case 'customer':
      return '/(dashboard)/customer';
    case 'business':
      return '/(dashboard)/business';
    default:
      return null;
  }
}

/**
 * Checks if the current route is a protected route
 */
export function isProtectedRoute(segments: string[]): boolean {
  const protectedGroups = ['(dashboard)', '(onboarding)'];
  return protectedGroups.includes(segments[0]);
}

/**
 * Checks if the current route is an auth route
 */
export function isAuthRoute(segments: string[]): boolean {
  return segments[0] === '(auth)';
}
