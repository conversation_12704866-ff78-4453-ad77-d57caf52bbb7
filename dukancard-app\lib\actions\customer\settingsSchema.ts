import { validateEmail, validateIndianMobile, validateRequired, validatePincode } from '@/backend/supabase/utils/validation';

// Customer profile validation
export const validateCustomerProfile = (data: { name: string }) => {
  const errors: Record<string, string> = {};

  if (!data.name || data.name.trim().length < 2) {
    errors.name = "Name must be at least 2 characters.";
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

// Email validation
export const validateCustomerEmail = (email: string) => {
  return validateEmail(email);
};

// Address validation
export const validateCustomerAddress = (data: {
  address?: string;
  pincode: string;
  city: string;
  state: string;
  locality: string;
}) => {
  const errors: Record<string, string> = {};

  if (data.address && data.address.length > 100) {
    errors.address = "Address cannot exceed 100 characters.";
  }

  const pincodeValidation = validatePincode(data.pincode);
  if (!pincodeValidation.isValid) {
    errors.pincode = pincodeValidation.error || "Invalid pincode";
  }

  const cityValidation = validateRequired(data.city, "City");
  if (!cityValidation.isValid) {
    errors.city = cityValidation.error || "City is required";
  } else if (data.city.length > 50) {
    errors.city = "City cannot exceed 50 characters.";
  }

  const stateValidation = validateRequired(data.state, "State");
  if (!stateValidation.isValid) {
    errors.state = stateValidation.error || "State is required";
  } else if (data.state.length > 50) {
    errors.state = "State cannot exceed 50 characters.";
  }

  const localityValidation = validateRequired(data.locality, "Locality");
  if (!localityValidation.isValid) {
    errors.locality = localityValidation.error || "Locality is required";
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

// Password validation
export const validateCustomerPassword = (data: {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}) => {
  const errors: Record<string, string> = {};

  if (!data.currentPassword) {
    errors.currentPassword = "Current password is required.";
  }

  if (!data.newPassword) {
    errors.newPassword = "New password is required";
  } else {
    if (data.newPassword.length < 6) {
      errors.newPassword = "Password must be at least 6 characters";
    } else if (!/[A-Z]/.test(data.newPassword)) {
      errors.newPassword = "Password must contain at least one capital letter";
    } else if (!/[a-z]/.test(data.newPassword)) {
      errors.newPassword = "Password must contain at least one lowercase letter.";
    } else if (!/[0-9]/.test(data.newPassword)) {
      errors.newPassword = "Password must contain at least one number";
    } else if (!/[^A-Za-z0-9]/.test(data.newPassword)) {
      errors.newPassword = "Password must contain at least one symbol";
    }
  }

  if (!data.confirmPassword) {
    errors.confirmPassword = "Please confirm your password";
  } else if (data.newPassword !== data.confirmPassword) {
    errors.confirmPassword = "Passwords do not match.";
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};
