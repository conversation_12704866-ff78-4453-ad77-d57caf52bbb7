/**
 * Dukancard brand colors extracted from the Next.js project globals.css
 * Using the gold-based theme with OKLCH color values converted to hex
 */

// Brand Gold Colors (converted from OKLCH values)
const brandGold = '#C29D5B'; // oklch(0.769 0.11 85)
const brandGoldLight = '#D4B373'; // oklch(0.82 0.1 85)
const brandGoldDark = '#B08A4A'; // oklch(0.7 0.12 85)
const brandGoldForeground = '#2D2D2D'; // oklch(0.18 0.01 90)

const tintColorLight = brandGold;
const tintColorDark = brandGoldLight;

export const Colors = {
  light: {
    // Base colors
    text: '#2D2D2D', // oklch(0.18 0.01 90)
    background: '#FEFEFE', // oklch(0.98 0.005 90)
    tint: tintColorLight,
    icon: '#808080', // oklch(0.5 0.005 90)
    tabIconDefault: '#808080',
    tabIconSelected: tintColorLight,

    // Dukancard brand colors (from globals.css)
    primary: brandGold,
    primaryForeground: brandGoldForeground,
    secondary: '#F5F5F5', // oklch(0.96 0.005 90)
    secondaryForeground: '#404040', // oklch(0.25 0.01 90)
    accent: brandGoldLight,
    accentForeground: brandGoldForeground,
    muted: '#F5F5F5', // oklch(0.96 0.005 90)
    mutedForeground: '#808080', // oklch(0.5 0.005 90)

    // Status colors
    success: '#22C55E',
    warning: '#F59E0B',
    error: '#EF4444', // oklch(0.577 0.245 27.325)
    destructive: '#EF4444',

    // Surface colors
    card: '#FFFFFF',
    cardForeground: '#2D2D2D',
    popover: '#FFFFFF',
    popoverForeground: '#2D2D2D',
    border: '#E5E5E5', // oklch(0.9 0.005 90)
    input: '#E5E5E5',
    ring: `${brandGold}80`, // Gold with opacity

    // Additional brand colors
    surface: '#F8F9FA',
    brandGold,
    brandGoldLight,
    brandGoldDark,
  },
  dark: {
    // Base colors
    text: '#F2F2F2', // oklch(0.95 0.005 90)
    background: '#262626', // oklch(0.15 0.01 90)
    tint: tintColorDark,
    icon: '#A6A6A6', // oklch(0.65 0.005 90)
    tabIconDefault: '#A6A6A6',
    tabIconSelected: tintColorDark,

    // Dukancard brand colors for dark mode
    primary: brandGold,
    primaryForeground: brandGoldForeground,
    secondary: '#404040', // oklch(0.25 0.01 90)
    secondaryForeground: '#F2F2F2', // oklch(0.95 0.005 90)
    accent: brandGoldLight,
    accentForeground: brandGoldForeground,
    muted: '#404040', // oklch(0.25 0.01 90)
    mutedForeground: '#A6A6A6', // oklch(0.65 0.005 90)

    // Status colors
    success: '#16A34A',
    warning: '#D97706',
    error: '#DC2626', // oklch(0.704 0.191 22.216)
    destructive: '#DC2626',

    // Surface colors
    card: '#000000',
    cardForeground: '#F2F2F2',
    popover: '#333333', // oklch(0.2 0.01 90)
    popoverForeground: '#F2F2F2',
    border: '#FFFFFF26', // oklch(1 0 0 / 0.15)
    input: '#FFFFFF2E', // oklch(1 0 0 / 0.18)
    ring: `${brandGold}99`, // Gold with opacity

    // Additional brand colors
    surface: '#1F2937',
    brandGold,
    brandGoldLight,
    brandGoldDark,
  },
};
