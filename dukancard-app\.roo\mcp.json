{"mcpServers": {"supabase": {"command": "cmd", "args": ["/c", "npx", "-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"], "alwaysAllow": ["list_projects", "list_tables", "execute_sql", "list_functions", "list_edge_functions"]}, "code-index": {"command": "py", "args": ["-m", "code_index_mcp"], "alwaysAllow": ["set_project_path", "get_settings_info", "search_code", "find_files", "get_file_summary", "index_codebase", "clear_settings", "refresh_index", "get_stats"]}, "github.com/upstash/context7-mcp": {"command": "cmd", "args": ["/c", "npx", "-y", "@upstash/context7-mcp@latest"], "disabled": false, "autoApprove": [], "alwaysAllow": ["get-library-docs", "resolve-library-id"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "alwaysAllow": ["sequentialthinking"]}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "C:/Users/<USER>/Documents"]}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}}