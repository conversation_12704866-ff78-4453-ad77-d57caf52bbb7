import React, { useEffect, useRef, useState, useCallback } from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { useTheme } from '@/src/hooks/useTheme';
import { responsiveFontSize } from '@/lib/theme/colors';
import { Star, MessageSquare, Users, Heart } from 'lucide-react-native';
import { CustomerAnimatedMetricCard } from '@/src/components/metrics/CustomerAnimatedMetricCard';
import { getCustomerMetrics, CustomerMetrics } from '@/backend/supabase/services/common/metricsService';
import { formatIndianNumberShort } from '@/lib/utils';

import { useRouter } from 'expo-router';

interface CustomerMetricsOverviewProps {
  initialReviewCount: number;
  initialSubscriptionCount: number;
  initialLikesCount: number;
  userId: string;
}

export default function CustomerMetricsOverview({
  initialReviewCount,
  initialSubscriptionCount,
  initialLikesCount,
  userId,
}: CustomerMetricsOverviewProps) {
  const theme = useTheme();
  const router = useRouter();

  const styles = StyleSheet.create({
    container: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.lg,
    },
    activityScoreContainer: {
      marginBottom: theme.spacing.lg,
    },
    metricsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      gap: theme.spacing.sm,
    },
  });

  // State for metrics
  const [metrics, setMetrics] = useState<CustomerMetrics>({
    reviewCount: initialReviewCount,
    subscriptionCount: initialSubscriptionCount,
    likesCount: initialLikesCount,
    activityScore: initialReviewCount + initialSubscriptionCount * 2 + initialLikesCount,
  });

  const [isUpdated, setIsUpdated] = useState({
    reviews: false,
    subscriptions: false,
    likes: false,
  });

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  useEffect(() => {
    // Staggered animation for metrics cards
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, slideAnim]);

  // Callback for handling metrics updates
  const handleMetricsUpdate = useCallback(async () => {


    // Refresh metrics when any change occurs
    const result = await getCustomerMetrics();
    if (result.success && result.data) {
      setMetrics(result.data);

      // Trigger update animation for all metrics since we don't know which one changed
      setIsUpdated({
        reviews: true,
        subscriptions: true,
        likes: true,
      });

      // Reset update indicators after animation
      setTimeout(() => {
        setIsUpdated({
          reviews: false,
          subscriptions: false,
          likes: false,
        });
      }, 2000);
    }
  }, []);



  const backgroundColor = theme.colors.background;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor,
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        }
      ]}
    >
      {/* Activity Score - Above main metrics */}
      <View style={styles.activityScoreContainer}>
        <CustomerAnimatedMetricCard
          title="Activity Score"
          value={formatIndianNumberShort(metrics.activityScore)}
          icon={MessageSquare}
          description="Your engagement level"
          color="brand"
          index={0}
        />
      </View>

      {/* Customer Stats Section - 3 columns: Likes, Subscriptions, Reviews */}
      <View style={styles.metricsGrid}>
        {/* Likes Card */}
        <CustomerAnimatedMetricCard
          title="Likes"
          value={formatIndianNumberShort(metrics.likesCount)}
          icon={Heart}
          description="Businesses you've liked"
          color="red"
          index={1}
          onPress={() => {
            router.push('/(dashboard)/customer/activity/likes');
          }}
        />

        {/* Subscriptions Card */}
        <CustomerAnimatedMetricCard
          title="Followers"
          value={formatIndianNumberShort(metrics.subscriptionCount)}
          icon={Users}
          description="Businesses you're following"
          color="blue"
          index={2}
          onPress={() => {
            router.push('/(dashboard)/customer/activity/subscriptions');
          }}
        />

        {/* Reviews Card */}
        <CustomerAnimatedMetricCard
          title="Rating"
          value={formatIndianNumberShort(metrics.reviewCount)}
          icon={Star}
          description="Reviews you've left for businesses"
          color="yellow"
          index={3}
          onPress={() => {
            router.push('/(dashboard)/customer/activity/reviews');
          }}
        />
      </View>
    </Animated.View>
  );
}
