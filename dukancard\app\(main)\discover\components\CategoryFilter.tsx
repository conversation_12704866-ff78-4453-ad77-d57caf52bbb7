"use client";

import React, { useState } from "react";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { getCategories } from "@/lib/config/categories";

interface CategoryFilterProps {
  value?: string | null;
  onValueChange: (_value: string | null) => void;
  placeholder?: string;
  className?: string;
}

export default function CategoryFilter({
  value,
  onValueChange,
  placeholder = "Select category...",
  className,
}: CategoryFilterProps) {
  const [open, setOpen] = useState(false);

  // Get all categories from the shared config
  const categories = getCategories();

  // Find the selected category
  const selectedCategory = categories.find(
    (category) => category.name === value
  );

  const handleSelect = (categoryName: string) => {
    if (categoryName === value) {
      onValueChange(null); // Deselect if already selected
    } else {
      onValueChange(categoryName);
    }
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full md:w-[200px] h-12 min-h-[48px] justify-between bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-md px-4 text-base font-normal",
            !value && "text-neutral-500 dark:text-neutral-400",
            className
          )}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">
            {selectedCategory ? (
              <>
                <selectedCategory.icon className="h-4 w-4 text-[var(--brand-gold)] flex-shrink-0" />
                <span className="truncate">{selectedCategory.name}</span>
              </>
            ) : (
              <span className="truncate">{placeholder}</span>
            )}
          </div>
          <div className="flex items-center gap-1 flex-shrink-0">
            <ChevronsUpDown className="h-4 w-4 text-neutral-400" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0" align="start">
        <Command>
          <CommandInput placeholder="Search categories..." />
          <CommandList>
            <CommandEmpty>No category found.</CommandEmpty>
            <CommandGroup>
              {categories.map((category) => (
                <CommandItem
                  key={category.name}
                  value={category.name}
                  onSelect={() => handleSelect(category.name)}
                  className="flex items-center gap-2 cursor-pointer"
                >
                  <category.icon className="h-4 w-4 text-[var(--brand-gold)]" />
                  <span className="flex-1">{category.name}</span>
                  <Check
                    className={cn(
                      "h-4 w-4",
                      value === category.name ? "opacity-100" : "opacity-0"
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
