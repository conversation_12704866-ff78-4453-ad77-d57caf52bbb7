import { useRouter, useLocalSearchParams } from "expo-router";
import React, { useState, useEffect } from "react";
import { StyleSheet, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useColorScheme } from "@/src/hooks/useColorScheme";
import { useNetworkStatus } from "@/src/utils/networkStatus";
import { OfflineBanner } from "@/src/components/ui/OfflineComponents";
import UnifiedBottomNavigation from "@/src/components/shared/navigation/UnifiedBottomNavigation";
import { useAuth } from "@/src/contexts/AuthContext";

// Import screen components
import BusinessFeedScreen from "./index";
import DiscoverScreen from "@/src/components/shared/screens/DiscoverScreenNew";
import BusinessProfileScreen from "./profile";

export default function BusinessTabLayout() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const networkStatus = useNetworkStatus();
  const { user } = useAuth();
  const { tab } = useLocalSearchParams<{ tab?: string }>();
  const [activeTab, setActiveTab] = useState("home");
  const insets = useSafeAreaInsets();

  // Set initial tab based on URL parameter
  useEffect(() => {
    if (tab && ["home", "discover", "profile"].includes(tab)) {
      setActiveTab(tab);
    }
  }, [tab]);

  // Use display name from user metadata
  const businessName = user?.user_metadata?.name || "Business User";

  const renderActiveScreen = () => {
    switch (activeTab) {
      case "home":
        return <BusinessFeedScreen />;
      case "discover":
        return <DiscoverScreen />;
      case "profile":
        return <BusinessProfileScreen />;
      default:
        return <BusinessFeedScreen />;
    }
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: colorScheme === 'dark' ? '#000000' : '#FFFFFF' }]}>
      {/* Offline Banner */}
      <OfflineBanner visible={!networkStatus.isConnected} />

      <View style={styles.screenContainer}>{renderActiveScreen()}</View>

      <UnifiedBottomNavigation
        activeTab={activeTab}
        onTabChange={handleTabChange}
        showQRScanner={true}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  screenContainer: {
    flex: 1,
    marginBottom: 0, // Ensure no extra margin
  },
});
