import { OnboardingContainer } from '@/src/components/layout/OnboardingContainer';
import { useTheme } from '@/src/hooks/useTheme';
import { useAuth } from '@/src/contexts/AuthContext';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    View,
    Text,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { businessDetailsSchema, BusinessDetailsFormData } from '@/src/utils/validationSchemas';
import { FormField } from '@/src/components/forms/FormField';
import { Building2, Mail } from 'lucide-react-native';
import { getOnboardingData, saveOnboardingData } from '@/backend/supabase/services/common/onboardingService';

export default function BusinessDetailsScreen() {
  const theme = useTheme();
  const { user } = useAuth();
  const params = useLocalSearchParams<{
    redirect?: string;
    message?: string;
  }>();

  const [isLoading, setIsLoading] = useState(false);
  const [isFormReady, setIsFormReady] = useState(false);

  const [initialFormData, setInitialFormData] = useState<BusinessDetailsFormData>({
    businessName: '',
    email: '',
  });

  const {
    control,
    handleSubmit,
    formState: { isValid },
    reset,
  } = useForm<BusinessDetailsFormData>({
    resolver: yupResolver(businessDetailsSchema),
    defaultValues: initialFormData,
    mode: 'onChange',
  });

  // Load existing onboarding data on component mount
  useEffect(() => {
    const loadExistingData = async () => {
      try {
        // Pre-fill form with user data
        const baseFormData = {
          businessName: '',
          email: '',
        };

        const updatedFormData = {
          ...baseFormData,
          email: user?.email || '',
        };

        // Load any existing onboarding data
        const existingData = await getOnboardingData();
        if (existingData) {
          updatedFormData.businessName = existingData.businessName || updatedFormData.businessName;
          updatedFormData.email = existingData.email || updatedFormData.email;
        }

        setInitialFormData(updatedFormData);
        setIsFormReady(true);
      } catch (error) {
        console.error('Error loading existing onboarding data:', error);
        setIsFormReady(true);
      }
    };

    loadExistingData();
  }, [user]);

  useEffect(() => {
    reset(initialFormData);
  }, [initialFormData, reset]);

  const handleFormSubmit = async (values: BusinessDetailsFormData) => {
    setIsLoading(true);
    try {
      // Save step 1 data to onboarding storage
      await saveOnboardingData({
        businessName: values.businessName,
        email: values.email,
      });

      // Navigate to next step with data
      const searchParams = new URLSearchParams();
      Object.entries(values).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });

      // Pass redirect parameters if available
      if (params.redirect) {
        searchParams.append('redirect', params.redirect);
      }
      if (params.message) {
        searchParams.append('message', params.message);
      }

      const nextUrl = `/(onboarding)/card-information?${searchParams.toString()}`;
      router.push(nextUrl);
    } catch (error) {
      console.error('Navigation error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    router.push('/(auth)/choose-role');
  };

  if (!isFormReady) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: theme.colors.background }}>
        <Text style={{ color: theme.colors.textPrimary }}>Loading...</Text>
      </View>
    );
  }

  return (
    <>
      <StatusBar
        style={theme.isDark ? 'light' : 'dark'}
        backgroundColor={theme.colors.background}
      />
      <OnboardingContainer
        currentStep={1}
        totalSteps={4}
        onBack={handleBack}
        onNext={handleSubmit(handleFormSubmit)}
        nextButtonText={isLoading ? "Loading..." : "Continue"}
        isNextDisabled={isLoading || !isFormReady || !isValid}
        showProgress={false}
      >
        <View style={{ gap: theme.spacing.md, paddingHorizontal: theme.spacing.xs }}>
          <View style={{ alignItems: 'center', marginBottom: theme.spacing.lg }}>
            <Text style={{
              fontSize: theme.typography.fontSize.xxl,
              fontWeight: '700',
              color: theme.colors.textPrimary,
              textAlign: 'center',
              marginBottom: theme.spacing.xs,
            }}>
              Business Details
            </Text>
            <Text style={{
              fontSize: theme.typography.fontSize.base,
              color: theme.colors.textSecondary,
              textAlign: 'center',
              lineHeight: theme.typography.lineHeight.normal * theme.typography.fontSize.base,
            }}>
              Let&apos;s set up your business profile
            </Text>
          </View>

          <FormField
            control={control}
            name="businessName"
            label="Business Name"
            placeholder="e.g., My Awesome Business"
            leftIcon={<Building2 size={20} color={theme.colors.primary} />}
          />
          <FormField
            control={control}
            name="email"
            label="Contact Email"
            placeholder="e.g., <EMAIL>"
            leftIcon={<Mail size={20} color={theme.colors.primary} />}
            keyboardType="email-address"
          />


        </View>
      </OnboardingContainer>
    </>
  );
}
