import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import {
  Wifi,
  WifiOff,
  RefreshCw,
  AlertTriangle,
  Server,
  Clock,
  Shield,
  HelpCircle,
} from 'lucide-react-native';
import { useTheme } from '@/src/hooks/useTheme';
import { AppError } from '@/src/utils/errorHandling';

interface ErrorRecoveryProps {
  error: AppError;
  onRetry?: () => void;
  onDismiss?: () => void;
  isRetrying?: boolean;
  retryCount?: number;
  maxRetries?: number;
  showDismiss?: boolean;
  compact?: boolean;
  style?: any;
}

const getErrorIcon = (errorType: string) => {
  switch (errorType) {
    case 'network':
      return WifiOff;
    case 'server':
      return Server;
    case 'timeout':
      return Clock;
    case 'unauthorized':
      return Shield;
    default:
      return AlertTriangle;
  }
};

const getErrorGuidance = (errorType: string): string => {
  switch (errorType) {
    case 'network':
      return 'Check your internet connection and try again. Make sure you have a stable connection.';
    case 'server':
      return 'Our servers are experiencing issues. Please wait a moment and try again.';
    case 'timeout':
      return 'The request took too long. Check your connection and try again.';
    case 'unauthorized':
      return 'Authentication failed. You may need to log in again.';
    case 'validation':
      return 'Please check your input and try again.';
    default:
      return 'Something unexpected happened. Please try again or contact support if the issue persists.';
  }
};

export const ErrorRecovery: React.FC<ErrorRecoveryProps> = ({
  error,
  onRetry,
  onDismiss,
  isRetrying = false,
  retryCount = 0,
  maxRetries = 3,
  showDismiss = true,
  compact = false,
  style,
}) => {
  const theme = useTheme();
  const IconComponent = getErrorIcon(error.type);
  const guidance = getErrorGuidance(error.type);
  const canRetry = onRetry && retryCount < maxRetries;

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.background,
      borderRadius: theme.borderRadius.lg,
      padding: compact ? theme.spacing.md : theme.spacing.lg,
      margin: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    iconContainer: {
      alignItems: 'center',
      marginBottom: compact ? theme.spacing.sm : theme.spacing.md,
    },
    title: {
      fontSize: compact ? theme.typography.fontSize.lg : theme.typography.fontSize.xl,
      fontWeight: theme.typography.fontWeight.bold,
      color: theme.colors.textPrimary,
      textAlign: 'center',
      marginBottom: theme.spacing.xs,
    },
    message: {
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.textSecondary,
      lineHeight: theme.typography.lineHeight.relaxed * theme.typography.fontSize.base,
      marginBottom: compact ? theme.spacing.sm : theme.spacing.md,
    },
    guidance: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textMuted, // Changed from textTertiary
      textAlign: 'center',
      lineHeight: theme.typography.lineHeight.relaxed * theme.typography.fontSize.sm,
      marginBottom: compact ? theme.spacing.md : theme.spacing.lg,
      fontStyle: 'italic',
    },
    actionsContainer: {
      flexDirection: compact ? 'column' : 'row',
      justifyContent: 'center',
      alignItems: 'center',
      gap: theme.spacing.sm,
    },
    retryButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.xs,
      minWidth: 120,
      justifyContent: 'center',
    },
    retryButtonDisabled: {
      backgroundColor: theme.colors.secondary, // Changed from backgroundSecondary
      opacity: 0.6,
    },
    retryButtonText: {
      color: '#FFFFFF', // Changed from theme.colors.white
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.medium,
    },
    retryButtonTextDisabled: {
      color: theme.colors.textSecondary,
    },
    dismissButton: {
      backgroundColor: 'transparent',
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.xs,
      minWidth: 120,
      justifyContent: 'center',
    },
    dismissButtonText: {
      color: theme.colors.textSecondary,
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.medium,
    },
    retryInfo: {
      fontSize: theme.typography.fontSize.xs,
      color: theme.colors.textMuted, // Changed from textTertiary
      textAlign: 'center',
      marginTop: theme.spacing.xs,
    },
    networkStatus: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: theme.spacing.xs,
      marginBottom: theme.spacing.sm,
      padding: theme.spacing.xs,
      backgroundColor: error.type === 'network' ? theme.colors.error + '20' : theme.colors.secondary, // Changed from backgroundSecondary
      borderRadius: theme.borderRadius.sm,
    },
    networkStatusText: {
      fontSize: theme.typography.fontSize.sm,
      color: error.type === 'network' ? theme.colors.error : theme.colors.textSecondary,
      fontWeight: theme.typography.fontWeight.medium,
    },
  });

  return (
    <View style={[styles.container, style]}>
      {/* Network Status Indicator */}
      {error.type === 'network' && (
        <View style={styles.networkStatus}>
          <WifiOff size={16} color={theme.colors.error} />
          <Text style={styles.networkStatusText}>No Internet Connection</Text>
        </View>
      )}

      {/* Error Icon */}
      <View style={styles.iconContainer}>
        <IconComponent 
          size={compact ? 48 : 64} 
          color={theme.colors.error} 
        />
      </View>

      {/* Error Title */}
      <Text style={styles.title}>{error.title}</Text>

      {/* Error Message */}
      <Text style={styles.message}>{error.message}</Text>

      {/* Guidance Text */}
      {!compact && (
        <Text style={styles.guidance}>{guidance}</Text>
      )}

      {/* Action Buttons */}
      <View style={styles.actionsContainer}>
        {/* Retry Button */}
        {canRetry && (
          <TouchableOpacity
            style={[
              styles.retryButton,
              (!canRetry || isRetrying) && styles.retryButtonDisabled,
            ]}
            onPress={onRetry}
            disabled={!canRetry || isRetrying}
            activeOpacity={0.8}
          >
            {isRetrying ? (
              <ActivityIndicator size="small" color={'#FFFFFF'} />
            ) : (
              <RefreshCw size={16} color={'#FFFFFF'} />
            )}
            <Text style={[
              styles.retryButtonText,
              (!canRetry || isRetrying) && styles.retryButtonTextDisabled,
            ]}>
              {isRetrying ? 'Retrying...' : 'Try Again'}
            </Text>
          </TouchableOpacity>
        )}

        {/* Dismiss Button */}
        {showDismiss && onDismiss && (
          <TouchableOpacity
            style={styles.dismissButton}
            onPress={onDismiss}
            activeOpacity={0.8}
          >
            <Text style={styles.dismissButtonText}>Dismiss</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Retry Information */}
      {canRetry && retryCount > 0 && (
        <Text style={styles.retryInfo}>
          Attempt {retryCount} of {maxRetries}
        </Text>
      )}

      {/* Max Retries Reached */}
      {retryCount >= maxRetries && (
        <Text style={styles.retryInfo}>
          Maximum retry attempts reached. Please check your connection or contact support.
        </Text>
      )}
    </View>
  );
};

export default ErrorRecovery;
