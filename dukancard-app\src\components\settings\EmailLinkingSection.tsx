import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Input } from '@/src/components/ui/Input';
import { Button } from '@/src/components/ui/Button';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { Mail, AlertCircle, CheckCircle } from 'lucide-react-native';
import { linkCustomerEmail, verifyEmailOTP } from '@/backend/supabase/services/common/settingsService';

interface EmailLinkingSectionProps {
  currentEmail?: string | null;
  currentPhone?: string | null;
  registrationType: 'google' | 'email' | 'phone';
  onEmailUpdated?: () => void;
}

interface FormData {
  email: string;
  otp: string;
}

interface FormErrors {
  email?: string;
  otp?: string;
}

type Step = 'email' | 'otp';

export function EmailLinkingSection({
  currentEmail,
  registrationType,
  onEmailUpdated,
}: EmailLinkingSectionProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [step, setStep] = useState<Step>('email');
  const [formData, setFormData] = useState<FormData>({
    email: '',
    otp: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<string>('');
  const [emailForOTP, setEmailForOTP] = useState<string>('');

  const isGoogleUser = registrationType === 'google';
  const isEmailUser = registrationType === 'email';

  const validateEmail = (email: string): string | undefined => {
    if (!email.trim()) {
      return 'Email is required';
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      return 'Please enter a valid email address';
    }
    return undefined;
  };

  const validateOTP = (otp: string): string | undefined => {
    if (!otp.trim()) {
      return 'Verification code is required';
    }
    if (otp.trim().length !== 6) {
      return 'Verification code must be 6 digits';
    }
    if (!/^\d{6}$/.test(otp.trim())) {
      return 'Verification code must contain only numbers';
    }
    return undefined;
  };

  const handleEmailSubmit = async () => {
    const emailError = validateEmail(formData.email);
    if (emailError) {
      setErrors({ email: emailError });
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      const result = await linkCustomerEmail(formData.email.trim());

      if (result.success) {
        if (result.requiresOTP) {
          setEmailForOTP(formData.email.trim());
          setStep('otp');
          setMessage("We've sent a 6-digit verification code to your email address.");
          Alert.alert('Success', 'Verification code sent to your email!');
        } else {
          setMessage('Email address has been linked to your account.');
          Alert.alert('Success', 'Email linked successfully!');
          onEmailUpdated?.();
        }
      } else {
        setErrors({ email: result.error || 'Failed to link email' });
        Alert.alert('Error', result.error || 'Failed to link email');
      }
    } catch (error) {
      console.error('Email linking error:', error);
      setErrors({ email: 'An unexpected error occurred' });
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleOTPSubmit = async () => {
    const otpError = validateOTP(formData.otp);
    if (otpError) {
      setErrors({ otp: otpError });
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      const result = await verifyEmailOTP(emailForOTP, formData.otp.trim());

      if (result.success) {
        setMessage('Email address has been linked to your account.');
        setStep('email');
        setEmailForOTP('');
        setFormData({ email: '', otp: '' });
        Alert.alert('Success', 'Email linked successfully!');
        onEmailUpdated?.();
      } else {
        setErrors({ otp: result.error || 'Failed to verify code' });
        Alert.alert('Error', result.error || 'Failed to verify code');
      }
    } catch (error) {
      console.error('OTP verification error:', error);
      setErrors({ otp: 'An unexpected error occurred' });
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFieldChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const getTitle = () => {
    if (isGoogleUser) return "Email Address (Google)";
    if (isEmailUser) return "Update Email Address";
    return "Link Email Address";
  };

  const getDescription = () => {
    if (isGoogleUser) {
      return "Your email is linked to your Google account and cannot be changed here.";
    }
    if (isEmailUser) {
      return "Update your email address. We'll send OTP verification to both old and new email addresses.";
    }
    return "Add an email address to your account for additional login options and notifications.";
  };

  return (
    <View style={[styles.container, { backgroundColor: isDark ? '#1a1a1a' : '#fff' }]}>
      <View style={styles.header}>
        <View style={[styles.iconContainer, { backgroundColor: isDark ? '#1E3A8A20' : '#DBEAFE' }]}>
          <Mail size={20} color="#3B82F6" />
        </View>
        <View style={styles.headerContent}>
          <Text style={[styles.title, { color: isDark ? '#fff' : '#000' }]}>
            {getTitle()}
          </Text>
          <Text style={[styles.description, { color: isDark ? '#999' : '#666' }]}>
            {getDescription()}
          </Text>
        </View>
      </View>

      {message ? (
        <View style={[styles.messageContainer, { backgroundColor: isDark ? '#1E3A8A20' : '#DBEAFE' }]}>
          <View style={styles.messageContent}>
            <CheckCircle size={16} color="#3B82F6" />
            <Text style={[styles.messageText, { color: isDark ? '#60A5FA' : '#1D4ED8' }]}>
              {message}
            </Text>
          </View>
        </View>
      ) : null}

      <View style={styles.content}>
        {isGoogleUser ? (
          // Google users - show email as read-only
          <View style={styles.readOnlyContainer}>
            <Text style={[styles.label, { color: isDark ? '#fff' : '#000' }]}>
              Email Address
            </Text>
            <View style={[styles.readOnlyField, { backgroundColor: isDark ? '#2a2a2a' : '#f5f5f5' }]}>
              <Text style={[styles.readOnlyText, { color: isDark ? '#999' : '#666' }]}>
                {currentEmail}
              </Text>
            </View>
            <Text style={[styles.helperText, { color: isDark ? '#999' : '#666' }]}>
              This email is managed by your Google account.
            </Text>
          </View>
        ) : step === 'email' ? (
          // Email step
          <View style={styles.formContainer}>
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: isDark ? '#fff' : '#000' }]}>
                Email Address
              </Text>
              <Input
                value={formData.email}
                onChangeText={(text) => handleFieldChange('email', text)}
                placeholder="<EMAIL>"
                error={errors.email}
                leftIcon={<Mail size={20} color={isDark ? '#999' : '#666'} />}
                keyboardType="email-address"
                autoCapitalize="none"
                editable={!isLoading}
              />
              <Text style={[styles.helperText, { color: isDark ? '#999' : '#666' }]}>
                {isEmailUser
                  ? "We'll send verification codes to both your current and new email addresses."
                  : "We'll send a verification code to this email address."
                }
              </Text>
            </View>

            <Button
              title={isLoading ? 'Sending...' : isEmailUser ? 'Update Email' : 'Link Email'}
              onPress={handleEmailSubmit}
              disabled={isLoading || !formData.email.trim()}
              variant="primary"
              icon={
                isLoading ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Mail size={20} color="#fff" />
                )
              }
            />
          </View>
        ) : (
          // OTP step
          <View style={styles.formContainer}>
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: isDark ? '#fff' : '#000' }]}>
                Verification Code
              </Text>
              <Input
                value={formData.otp}
                onChangeText={(text) => handleFieldChange('otp', text.replace(/[^0-9]/g, '').slice(0, 6))}
                placeholder="Enter 6-digit code"
                error={errors.otp}
                leftIcon={<AlertCircle size={20} color={isDark ? '#999' : '#666'} />}
                keyboardType="numeric"
                maxLength={6}
                editable={!isLoading}
              />
              <Text style={[styles.helperText, { color: isDark ? '#999' : '#666' }]}>
                Enter the 6-digit code sent to {emailForOTP}
              </Text>
            </View>

            <View style={styles.buttonGroup}>
              <Button
                title="Back"
                onPress={() => {
                  setStep('email');
                  setFormData(prev => ({ ...prev, otp: '' }));
                  setErrors({});
                }}
                disabled={isLoading}
                variant="secondary"
                style={styles.backButton}
              />
              <Button
                title={isLoading ? 'Verifying...' : 'Verify Code'}
                onPress={handleOTPSubmit}
                disabled={isLoading || formData.otp.length !== 6}
                variant="primary"
                style={styles.verifyButton}
                icon={
                  isLoading ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <CheckCircle size={20} color="#fff" />
                  )
                }
              />
            </View>
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
  },
  messageContainer: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#3B82F6',
  },
  messageContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  messageText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  content: {
    gap: 16,
  },
  readOnlyContainer: {
    gap: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
  },
  readOnlyField: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e5e5',
  },
  readOnlyText: {
    fontSize: 14,
  },
  helperText: {
    fontSize: 12,
    lineHeight: 16,
  },
  formContainer: {
    gap: 20,
  },
  inputGroup: {
    gap: 8,
  },
  buttonGroup: {
    flexDirection: 'row',
    gap: 12,
  },
  backButton: {
    flex: 1,
  },
  verifyButton: {
    flex: 2,
  },
});
