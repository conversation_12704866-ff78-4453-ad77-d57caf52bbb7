import { supabase } from "../../../config/supabase";

// --- Pincode Lookup Action ---
export async function getPincodeDetails(pincode: string): Promise<{
  data?: {
    city: string;
    state: string;
    localities: string[];
  };
  city?: string;
  state?: string;
  localities?: string[];
  error?: string;
}> {
  if (!pincode || !/^\d{6}$/.test(pincode)) {
    return { error: "Invalid Pincode format." };
  }

  try {
    // First get city and state from pincodes table
    const { data: pincodeData, error: pincodeError } = await supabase
      .from("pincodes")
      .select("OfficeName, DivisionName, StateName")
      .eq("Pincode", pincode) // Updated column name to match database
      .order("OfficeName");

    if (pincodeError) {
      console.error("Pincode Fetch Error:", pincodeError);
      return { error: "Database error fetching pincode details." };
    }

    if (!pincodeData || pincodeData.length === 0) {
      return { error: "<PERSON>nco<PERSON> not found." };
    }

    // State names are already in title case format in the database
    const state = pincodeData[0].StateName;

    // Use DivisionName as the city (already cleaned)
    const city = pincodeData[0].DivisionName;

    // Get unique localities from post office names
    const localities = [
      ...new Set(pincodeData.map((item) => item.OfficeName)),
    ] as string[];

    return {
      data: { city, state, localities },
      city,
      state,
      localities,
    };
  } catch (e) {
    console.error("Pincode Lookup Exception:", e);
    return { error: "An unexpected error occurred during pincode lookup." };
  }
}
