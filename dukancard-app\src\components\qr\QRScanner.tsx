import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Alert, Dimensions, TouchableOpacity, Linking } from 'react-native';
import { Camera, CameraView, BarcodeScanningResult } from 'expo-camera';
import { useRouter } from 'expo-router';
import { validateQRCodeForUser } from '@/src/utils/qrCodeUtils';
import { useToast } from '@/src/components/ui/Toast';

interface QRScannerProps {
  onScanSuccess?: (businessSlug: string) => void;
  onScanError?: (error: string) => void;
  onClose?: () => void;
  enableTorch?: boolean; // Add torch control prop
}

const { width, height } = Dimensions.get('window');

export default function QRScanner({ onScanSuccess, onScanError, onClose, enableTorch = false }: QRScannerProps) {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [scanned, setScanned] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const router = useRouter();
  const toast = useToast();

  useEffect(() => {
    const getCameraPermissions = async () => {
      try {
        // Check current permission status using Expo Camera
        const { status, canAskAgain } = await Camera.getCameraPermissionsAsync();

        if (status === 'granted') {
          setHasPermission(true);
          return;
        }

        // Request permission if we can ask
        if (canAskAgain) {
          const { status: requestStatus } = await Camera.requestCameraPermissionsAsync();
          setHasPermission(requestStatus === 'granted');

          if (requestStatus !== 'granted') {
            console.warn('Camera permission denied');
            toast.warning('Camera permission is required to scan QR codes');
          }
        } else {
          // Permission permanently denied
          setHasPermission(false);
          toast.error('Camera permission is required to scan QR codes');
        }
      } catch (error) {
        console.error('Error requesting camera permissions:', error);
        setHasPermission(false);
        toast.error('Failed to request camera permissions');
      }
    };

    getCameraPermissions();
  }, [toast]);

  const handleBarCodeScanned = async ({ type, data }: BarcodeScanningResult) => {
    if (scanned || isProcessing) return;

    setScanned(true);
    setIsProcessing(true);

    try {
      // Validate the QR code URL
      const validation = validateQRCodeForUser(data);

      if (!validation.isValid) {
        const errorMessage = validation.error || 'Invalid QR code';
        toast.error(errorMessage);
        if (onScanError) {
          onScanError(errorMessage);
        }
        // Reset scanning after a delay
        setTimeout(() => {
          setScanned(false);
          setIsProcessing(false);
        }, 2000);
        return;
      }

      // Extract business slug from valid URL
      const businessSlug = validation.businessSlug!;

      toast.success('QR code scanned successfully!');

      if (onScanSuccess) {
        onScanSuccess(businessSlug);
      } else {
        // Default navigation to business card view
        router.push(`/business/${businessSlug}`);
      }
    } catch (error) {
      console.error('Error processing QR code:', error);
      const errorMessage = error instanceof Error
        ? `Failed to process QR code: ${error.message}`
        : 'Failed to process QR code';
      toast.error(errorMessage);
      if (onScanError) {
        onScanError(errorMessage);
      }
      // Reset scanning after a delay
      setTimeout(() => {
        setScanned(false);
        setIsProcessing(false);
      }, 2000);
    }
  };

  if (hasPermission === null) {
    return (
      <View style={styles.container}>
        <Text style={styles.message}>Requesting camera permission...</Text>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View style={styles.container}>
        <View style={styles.permissionContainer}>
          <Text style={styles.message}>Camera Access Required</Text>
          <Text style={styles.subMessage}>
            To scan QR codes, please enable camera permissions in your device settings.
          </Text>
          <Text style={styles.instructionText}>
            Go to Settings → Apps → Dukancard → Permissions → Camera
          </Text>
          <View style={styles.permissionButtons}>
            <TouchableOpacity
              style={styles.settingsButton}
              onPress={() => Linking.openSettings()}
            >
              <Text style={styles.settingsButtonText}>Open Settings</Text>
            </TouchableOpacity>
            {onClose && (
              <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                <Text style={styles.closeButtonText}>Close</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <CameraView
        style={styles.camera}
        facing="back"
        enableTorch={enableTorch}
        onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
        barcodeScannerSettings={{
          barcodeTypes: ['qr'],
        }}
      />

      {/* Overlay positioned absolutely on top of camera */}
      <View style={styles.overlay}>
        {/* Top overlay */}
        <View style={styles.overlayTop}>
          <Text style={styles.instructionText}>
            Point your camera at a Dukancard QR code
          </Text>
        </View>

        {/* Middle section with scanning frame */}
        <View style={styles.overlayMiddle}>
          <View style={styles.overlayLeft} />
          <View style={styles.scanFrame}>
            <View style={styles.scanFrameCorner} />
            <View style={[styles.scanFrameCorner, styles.topRight]} />
            <View style={[styles.scanFrameCorner, styles.bottomLeft]} />
            <View style={[styles.scanFrameCorner, styles.bottomRight]} />

            {isProcessing && (
              <View style={styles.processingOverlay}>
                <Text style={styles.processingText}>Processing...</Text>
              </View>
            )}
          </View>
          <View style={styles.overlayRight} />
        </View>

        {/* Bottom overlay */}
        <View style={styles.overlayBottom}>
          <Text style={styles.helpText}>
            Make sure the QR code is clearly visible and well-lit
          </Text>
        </View>
      </View>
    </View>
  );
}

const scanFrameSize = Math.min(width * 0.7, 250);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  camera: {
    flex: 1,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
  },
  overlayTop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingBottom: 20,
  },
  overlayMiddle: {
    flexDirection: 'row',
    height: scanFrameSize,
  },
  overlayLeft: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  overlayRight: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  overlayBottom: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingTop: 20,
  },
  scanFrame: {
    width: scanFrameSize,
    height: scanFrameSize,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanFrameCorner: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderColor: '#D4AF37',
    borderWidth: 3,
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  topRight: {
    top: 0,
    right: 0,
    left: 'auto',
    borderLeftWidth: 0,
    borderRightWidth: 3,
    borderBottomWidth: 0,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    top: 'auto',
    borderRightWidth: 0,
    borderTopWidth: 0,
    borderBottomWidth: 3,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    top: 'auto',
    left: 'auto',
    borderLeftWidth: 0,
    borderTopWidth: 0,
    borderRightWidth: 3,
    borderBottomWidth: 3,
  },
  processingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  processingText: {
    color: '#D4AF37',
    fontSize: 16,
    fontWeight: '600',
  },
  instructionText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  helpText: {
    color: '#ccc',
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  message: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 10,
  },
  subMessage: {
    color: '#ccc',
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  permissionButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 15,
    marginTop: 20,
  },
  settingsButton: {
    backgroundColor: '#3498db',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  settingsButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  closeButton: {
    backgroundColor: '#D4AF37',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  closeButtonText: {
    color: '#000',
    fontSize: 16,
    fontWeight: '600',
  },
});
