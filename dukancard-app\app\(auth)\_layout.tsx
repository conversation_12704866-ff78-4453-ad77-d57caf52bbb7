import { useColorScheme } from "@/src/hooks/useColorScheme";
import { supabase } from "@/lib/supabase";
import { router, Stack } from "expo-router";
import { LogOut } from "lucide-react-native";
import { Text, TouchableOpacity } from "react-native";
import {
  AlertProvider,
  useAlertDialog,
} from "@/src/components/providers/AlertProvider";

function AuthLayoutContent() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === "dark";
  const goldColor = "#D4AF37";
  const textColor = isDark ? "#FFFFFF" : "#000000";
  const backgroundColor = isDark ? "#000000" : "#FFFFFF";
  const { logout, error } = useAlertDialog();

  const handleLogout = async () => {
    logout(async () => {
      try {
        await supabase.auth.signOut();
        router.replace("/(auth)/login");
      } catch (err) {
        error("Logout Failed", "Failed to logout. Please try again.");
      }
    });
  };

  return (
    <Stack>
      <Stack.Screen
        name="login"
        options={{
          headerShown: false,
          title: "Login",
        }}
      />
      <Stack.Screen
        name="choose-role"
        options={{
          headerShown: true,
          title: "",
          headerStyle: {
            backgroundColor: backgroundColor,
          },
          headerTintColor: textColor,
          headerLeft: () => (
            <Text
              style={{
                fontSize: 20,
                fontWeight: "bold",
                color: goldColor,
                marginLeft: 16,
                paddingVertical: 0, // Reduce vertical padding
              }}
            >
              Dukan<Text style={{ color: textColor }}>card</Text>
            </Text>
          ),
          headerRight: () => (
            <TouchableOpacity
              style={{
                padding: 4, // Reduced padding
                borderRadius: 8,
                borderWidth: 1,
                borderColor: goldColor + "40",
                marginRight: 16,
              }}
              onPress={handleLogout}
              activeOpacity={0.7}
            >
              <LogOut size={20} color={goldColor} />
            </TouchableOpacity>
          ),
        }}
      />

      <Stack.Screen
        name="complete-profile"
        options={{
          headerShown: true,
          title: "",
          headerStyle: {
            backgroundColor: backgroundColor,
          },
          headerTintColor: textColor,
          headerLeft: () => (
            <Text
              style={{
                fontSize: 20,
                fontWeight: "bold",
                color: goldColor,
                marginLeft: 16,
                paddingVertical: 0, // Reduce vertical padding
              }}
            >
              Dukan<Text style={{ color: textColor }}>card</Text>
            </Text>
          ),
          headerRight: () => (
            <TouchableOpacity
              style={{
                padding: 4, // Reduced padding
                borderRadius: 8,
                borderWidth: 1,
                borderColor: goldColor + "40",
                marginRight: 16,
              }}
              onPress={handleLogout}
              activeOpacity={0.7}
            >
              <LogOut size={20} color={goldColor} />
            </TouchableOpacity>
          ),
        }}
      />
    </Stack>
  );
}

export default function AuthLayout() {
  return (
    <AlertProvider>
      <AuthLayoutContent />
    </AlertProvider>
  );
}
