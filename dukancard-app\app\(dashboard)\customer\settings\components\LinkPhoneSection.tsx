import React from 'react';
import { View, Text } from 'react-native';
import { Phone } from 'lucide-react-native';
import { useTheme } from '@/src/hooks/useTheme';
import { createLinkPhoneSectionStyles } from '@/styles/dashboard/customer/settings/link-phone';

interface LinkPhoneSectionProps {
  currentEmail?: string | null;
  currentPhone?: string | null;
  registrationType: 'google' | 'email' | 'phone';
}

export default function LinkPhoneSection({
  currentPhone,
}: LinkPhoneSectionProps) {
  const theme = useTheme();
  const styles = createLinkPhoneSectionStyles(theme);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <View style={styles.iconContainer}>
            <Phone size={16} color="#10B981" />
          </View>
          <Text style={styles.title}>Phone Number</Text>
        </View>
        <Text style={styles.description}>
          {currentPhone
            ? "Your current phone number linked to this account."
            : "No phone number is currently linked to your account."
          }
        </Text>
      </View>
      <View style={styles.content}>
        {currentPhone ? (
          // Show current phone number (read-only)
          <View style={styles.section}>
            <Text style={styles.label}>Current Phone Number</Text>
            <View style={styles.readOnlyContainer}>
              <Text style={styles.readOnlyText}>{currentPhone}</Text>
            </View>
            <Text style={styles.helperText}>
              Phone number changes are not currently supported. Contact support if you need to update your number.
            </Text>
          </View>
        ) : (
          // No phone number linked
          <View style={styles.emptyState}>
            <View style={styles.emptyIconContainer}>
              <Phone size={24} color={theme.colors.textSecondary} />
            </View>
            <Text style={styles.emptyTitle}>No Phone Number</Text>
            <Text style={styles.emptyDescription}>
              No phone number is currently linked to your account. Phone number linking is not available at this time.
            </Text>
          </View>
        )}
      </View>
    </View>
  );
}
