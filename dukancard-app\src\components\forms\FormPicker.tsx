import React from 'react';
import { Control, Controller, FieldError, FieldPath, FieldValues } from 'react-hook-form';
import { Text, View, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '@/src/hooks/useTheme';
import { ChevronDown } from 'lucide-react-native';

interface PickerOption {
  label: string;
  value: string;
}

interface FormPickerProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> {
  control: Control<TFieldValues>;
  name: TName;
  label: string;
  placeholder?: string;
  error?: FieldError;
  options: PickerOption[];
  onPress: () => void;
  leftIcon?: React.ReactNode;
  testID?: string;
}

export function FormPicker<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  control,
  name,
  label,
  placeholder = 'Select an option',
  error,
  options,
  onPress,
  leftIcon,
  testID,
}: FormPickerProps<TFieldValues, TName>) {
  const theme = useTheme();

  const getDisplayValue = (value: string) => {
    const option = options.find(opt => opt.value === value);
    return option ? option.label : placeholder;
  };

  return (
    <View style={styles.container}>
      <Controller
        control={control}
        name={name}
        render={({ field: { value }, fieldState: { error: fieldError } }) => (
          <View>
            {/* Label */}
            <Text style={[styles.label, { color: theme.colors.textPrimary }]}>
              {label}
            </Text>

            {/* Picker Button */}
            <TouchableOpacity
              style={[
                styles.pickerButton,
                {
                  backgroundColor: theme.colors.background,
                  borderColor: fieldError?.message || error?.message 
                    ? theme.colors.error 
                    : theme.colors.border,
                },
                theme.isDark && styles.pickerButtonDark,
              ]}
              onPress={onPress}
              testID={testID}
            >
              {leftIcon && (
                <View style={styles.leftIconContainer}>
                  {leftIcon}
                </View>
              )}
              
              <Text
                style={[
                  styles.pickerText,
                  {
                    color: value 
                      ? theme.colors.textPrimary 
                      : theme.colors.textSecondary,
                  },
                ]}
              >
                {getDisplayValue(value)}
              </Text>

              <ChevronDown 
                size={20} 
                color={theme.colors.textSecondary} 
                style={styles.chevron}
              />
            </TouchableOpacity>

            {/* Error Message */}
            {(fieldError?.message || error?.message) && (
              <Text style={[styles.errorText, { color: theme.colors.error }]}>
                {fieldError?.message || error?.message}
              </Text>
            )}
          </View>
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  pickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderRadius: 8,
    minHeight: 48,
  },
  pickerButtonDark: {
    borderColor: '#374151',
  },
  leftIconContainer: {
    marginRight: 12,
  },
  pickerText: {
    flex: 1,
    fontSize: 16,
  },
  chevron: {
    marginLeft: 8,
  },
  errorText: {
    fontSize: 14,
    marginTop: 4,
    marginLeft: 4,
  },
});
