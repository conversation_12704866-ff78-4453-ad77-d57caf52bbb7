/**
 * Production Error Logging Service
 * Handles error logging in production builds without external paid services
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import Constants from 'expo-constants';
import { errorTracker } from './errorTracking';

interface ProductionError {
  id: string;
  timestamp: string;
  error: {
    name: string;
    message: string;
    stack?: string;
  };
  context: {
    appVersion: string;
    platform: string;
    buildType: string;
    userId?: string;
    screen?: string;
    action?: string;
  };
  deviceInfo: {
    model?: string;
    osVersion?: string;
    appState?: string;
  };
  sent: boolean;
}

class ProductionErrorLogging {
  private isProduction: boolean;
  private storageKey = '@dukancard_production_errors';
  private maxStoredErrors = 100;
  private batchSize = 10;

  constructor() {
    this.isProduction = Constants.expoConfig?.extra?.appEnv === 'production';
  }

  /**
   * Log an error in production
   */
  async logProductionError(
    error: Error, 
    context?: {
      screen?: string;
      action?: string;
      userId?: string;
      additionalData?: any;
    }
  ) {
    if (!this.isProduction) return;

    try {
      const productionError: ProductionError = {
        id: this.generateErrorId(),
        timestamp: new Date().toISOString(),
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack,
        },
        context: {
          appVersion: Constants.expoConfig?.version || 'unknown',
          platform: Constants.platform?.ios ? 'ios' : 'android',
          buildType: 'production',
          userId: context?.userId,
          screen: context?.screen,
          action: context?.action,
        },
        deviceInfo: await this.getDeviceInfo(),
        sent: false,
      };

      await this.storeError(productionError);
      
      // Try to send errors immediately (non-blocking)
      this.sendStoredErrors().catch(() => {
        // Silently fail - errors will be sent later
      });

    } catch (storageError) {
      // If we can't store the error, at least log it to console
      console.error('Failed to store production error:', storageError);
    }
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get device information
   */
  private async getDeviceInfo() {
    try {
      return {
        model: Constants.deviceName || 'unknown',
        osVersion: Constants.platform?.ios ? 
          Constants.platform.ios.systemVersion : 
          Constants.platform?.android?.versionCode?.toString(),
        appState: 'active', // Could be enhanced with AppState
      };
    } catch {
      return {
        model: 'unknown',
        osVersion: 'unknown',
        appState: 'unknown',
      };
    }
  }

  /**
   * Store error locally
   */
  private async storeError(error: ProductionError) {
    try {
      const existingErrors = await this.getStoredErrors();
      const updatedErrors = [error, ...existingErrors].slice(0, this.maxStoredErrors);
      
      await AsyncStorage.setItem(this.storageKey, JSON.stringify(updatedErrors));
    } catch (storageError) {
      console.error('Failed to store error:', storageError);
    }
  }

  /**
   * Get stored errors
   */
  private async getStoredErrors(): Promise<ProductionError[]> {
    try {
      const stored = await AsyncStorage.getItem(this.storageKey);
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }

  /**
   * Send stored errors to custom endpoint
   */
  private async sendStoredErrors() {
    if (!this.isProduction) return;

    try {
      const errors = await this.getStoredErrors();
      const unsent = errors.filter(error => !error.sent).slice(0, this.batchSize);
      
      if (unsent.length === 0) return;

      // In a real implementation, you would send to your own logging endpoint
      // For now, we'll just mark them as sent
      const success = await this.sendToCustomEndpoint(unsent);
      
      if (success) {
        // Mark errors as sent
        const updatedErrors = errors.map(error => 
          unsent.find(u => u.id === error.id) ? { ...error, sent: true } : error
        );
        
        await AsyncStorage.setItem(this.storageKey, JSON.stringify(updatedErrors));
      }
    } catch (sendError) {
      console.error('Failed to send errors:', sendError);
    }
  }

  /**
   * Send errors to custom endpoint (placeholder implementation)
   */
  private async sendToCustomEndpoint(errors: ProductionError[]): Promise<boolean> {
    try {
      // TODO: Replace with your actual logging endpoint
      // Example implementation:
      /*
      const response = await fetch('https://your-domain.com/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer your-api-key',
        },
        body: JSON.stringify({ errors }),
      });
      
      return response.ok;
      */
      
      // For now, just simulate success after a delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log(`Would send ${errors.length} errors to logging endpoint`);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get error statistics
   */
  async getErrorStats(): Promise<{
    total: number;
    sent: number;
    pending: number;
    recent: number; // last 24 hours
  }> {
    try {
      const errors = await this.getStoredErrors();
      const now = new Date();
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      
      return {
        total: errors.length,
        sent: errors.filter(e => e.sent).length,
        pending: errors.filter(e => !e.sent).length,
        recent: errors.filter(e => new Date(e.timestamp) > yesterday).length,
      };
    } catch {
      return { total: 0, sent: 0, pending: 0, recent: 0 };
    }
  }

  /**
   * Clear old errors (keep only recent ones)
   */
  async clearOldErrors(daysToKeep: number = 7) {
    try {
      const errors = await this.getStoredErrors();
      const cutoff = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000);
      
      const recentErrors = errors.filter(error => 
        new Date(error.timestamp) > cutoff
      );
      
      await AsyncStorage.setItem(this.storageKey, JSON.stringify(recentErrors));
    } catch (error) {
      console.error('Failed to clear old errors:', error);
    }
  }

  /**
   * Force send all pending errors
   */
  async forceSendErrors(): Promise<boolean> {
    try {
      await this.sendStoredErrors();
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get recent errors for debugging (development only)
   */
  async getRecentErrors(limit: number = 10): Promise<ProductionError[]> {
    if (this.isProduction) return []; // Don't expose in production
    
    try {
      const errors = await this.getStoredErrors();
      return errors.slice(0, limit);
    } catch {
      return [];
    }
  }

  /**
   * Initialize production error logging
   */
  async initialize() {
    if (!this.isProduction) return;

    // Clean up old errors on startup
    await this.clearOldErrors();
    
    // Try to send any pending errors
    this.sendStoredErrors().catch(() => {
      // Silently fail
    });
  }
}

// Create singleton instance
export const productionErrorLogger = new ProductionErrorLogging();

// Export types
export type { ProductionError };
