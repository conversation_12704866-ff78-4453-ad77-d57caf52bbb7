/**
 * Skeleton Loaders for Social Features
 * Loading placeholders for subscriptions, likes, and reviews
 */

import React from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';

// Skeleton animation hook
const useSkeletonAnimation = () => {
  const animatedValue = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          easing: Easing.ease,
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          easing: Easing.ease,
          useNativeDriver: true,
        }),
      ])
    );
    animation.start();

    return () => animation.stop();
  }, [animatedValue]);

  const opacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  return opacity;
};

// Base skeleton component
const SkeletonBox: React.FC<{ width: number | string; height: number; style?: any }> = ({
  width,
  height,
  style,
}) => {
  const opacity = useSkeletonAnimation();

  return (
    <Animated.View
      style={[
        {
          width,
          height,
          backgroundColor: '#E0E0E0',
          borderRadius: 4,
          opacity,
        },
        style,
      ]}
    />
  );
};

// Subscription Card Skeleton
export const SubscriptionCardSkeleton: React.FC = () => {
  return (
    <View style={styles.card}>
      <View style={styles.cardContent}>
        {/* Logo skeleton */}
        <SkeletonBox width={50} height={50} style={styles.logoSkeleton} />

        {/* Business info skeleton */}
        <View style={styles.businessInfoSkeleton}>
          <SkeletonBox width="80%" height={16} style={{ marginBottom: 8 }} />
          <SkeletonBox width="60%" height={14} style={{ marginBottom: 6 }} />
          <SkeletonBox width="40%" height={12} />
        </View>

        {/* Visit button skeleton */}
        <SkeletonBox width={32} height={32} style={styles.visitButtonSkeleton} />
      </View>

      {/* Unsubscribe button skeleton */}
      <SkeletonBox width="100%" height={44} />
    </View>
  );
};

// Like Card Skeleton
export const LikeCardSkeleton: React.FC = () => {
  return (
    <View style={styles.card}>
      <View style={styles.cardContent}>
        {/* Logo skeleton */}
        <SkeletonBox width={50} height={50} style={styles.logoSkeleton} />

        {/* Business info skeleton */}
        <View style={styles.businessInfoSkeleton}>
          <SkeletonBox width="75%" height={16} style={{ marginBottom: 8 }} />
          <SkeletonBox width="55%" height={14} style={{ marginBottom: 6 }} />
          <SkeletonBox width="35%" height={12} />
        </View>

        {/* Visit button skeleton */}
        <SkeletonBox width={32} height={32} style={styles.visitButtonSkeleton} />
      </View>

      {/* Unlike button skeleton */}
      <SkeletonBox width="100%" height={44} />
    </View>
  );
};

// Review Card Skeleton
export const ReviewCardSkeleton: React.FC = () => {
  return (
    <View style={styles.card}>
      {/* Business header skeleton */}
      <View style={styles.businessHeader}>
        <SkeletonBox width={40} height={40} style={styles.logoSkeleton} />
        <View style={styles.businessInfoSkeleton}>
          <SkeletonBox width="70%" height={16} style={{ marginBottom: 4 }} />
          <SkeletonBox width="50%" height={12} />
        </View>
        <SkeletonBox width={28} height={28} style={styles.visitButtonSkeleton} />
      </View>

      {/* Review content skeleton */}
      <View style={styles.reviewContent}>
        {/* Stars skeleton */}
        <View style={styles.starsContainer}>
          {[1, 2, 3, 4, 5].map((star) => (
            <SkeletonBox key={star} width={20} height={20} style={styles.starSkeleton} />
          ))}
        </View>

        {/* Review text skeleton */}
        <SkeletonBox width="100%" height={14} style={{ marginBottom: 6 }} />
        <SkeletonBox width="85%" height={14} style={{ marginBottom: 6 }} />
        <SkeletonBox width="70%" height={14} style={{ marginBottom: 16 }} />

        {/* Action buttons skeleton */}
        <View style={styles.actionsSkeleton}>
          <SkeletonBox width="45%" height={32} style={{ marginRight: 8 }} />
          <SkeletonBox width="45%" height={32} style={{ marginLeft: 8 }} />
        </View>
      </View>
    </View>
  );
};

// List skeleton loaders
export const SubscriptionListSkeleton: React.FC<{ count?: number }> = ({ count = 5 }) => {
  return (
    <View>
      {Array.from({ length: count }).map((_, index) => (
        <SubscriptionCardSkeleton key={index} />
      ))}
    </View>
  );
};

export const LikeListSkeleton: React.FC<{ count?: number }> = ({ count = 5 }) => {
  return (
    <View>
      {Array.from({ length: count }).map((_, index) => (
        <LikeCardSkeleton key={index} />
      ))}
    </View>
  );
};

export const ReviewListSkeleton: React.FC<{ count?: number }> = ({ count = 3 }) => {
  return (
    <View>
      {Array.from({ length: count }).map((_, index) => (
        <ReviewCardSkeleton key={index} />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    overflow: 'hidden',
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  logoSkeleton: {
    borderRadius: 25,
    marginRight: 12,
  },
  businessInfoSkeleton: {
    flex: 1,
    marginRight: 12,
  },
  visitButtonSkeleton: {
    borderRadius: 16,
  },
  businessHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  reviewContent: {
    padding: 16,
  },
  starsContainer: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  starSkeleton: {
    borderRadius: 10,
    marginRight: 4,
  },
  actionsSkeleton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});
