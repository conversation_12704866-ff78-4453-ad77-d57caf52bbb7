import { StyleSheet, Dimensions } from 'react-native';

const { width } = Dimensions.get('window');

export const createDiscoverScreenStyles = (isDark: boolean) => {
  const backgroundColor = isDark ? '#000000' : '#FFFFFF';
  const textColor = isDark ? '#FFFFFF' : '#000000';
  const secondaryTextColor = isDark ? '#9CA3AF' : '#6B7280';
  const borderColor = isDark ? '#374151' : '#E5E7EB';
  const cardBackgroundColor = isDark ? '#1F2937' : '#F9FAFB';
  const inputBackgroundColor = isDark ? '#374151' : '#FFFFFF';

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor,
    },
    header: {
      padding: 20,
      paddingBottom: 16,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      marginBottom: 4,
      color: textColor,
    },
    subtitle: {
      fontSize: 16,
      lineHeight: 24,
      color: secondaryTextColor,
    },
    searchSection: {
      margin: 16,
      padding: 20,
      borderRadius: 12,
      backgroundColor: cardBackgroundColor,
    },
    inputGroup: {
      marginBottom: 20,
    },
    label: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 8,
      color: textColor,
    },
    locationHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    locationButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 6,
      gap: 4,
    },
    locationButtonText: {
      color: '#000000',
      fontSize: 14,
      fontWeight: '600',
    },
    inputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 12,
      borderRadius: 8,
      marginBottom: 12,
      gap: 8,
      backgroundColor: inputBackgroundColor,
      borderWidth: 1,
      borderColor: borderColor,
    },
    textInput: {
      flex: 1,
      fontSize: 16,
      paddingVertical: 0,
      color: textColor,
    },
    viewToggleSection: {
      margin: 16,
      marginTop: 0,
    },
    viewToggle: {
      flexDirection: 'row',
      borderRadius: 8,
      padding: 4,
      backgroundColor: cardBackgroundColor,
    },
    viewToggleButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 6,
      gap: 8,
    },
    viewToggleText: {
      fontSize: 14,
      fontWeight: '600',
    },
    searchButtonSection: {
      margin: 16,
      marginTop: 0,
    },
    searchButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 16,
      paddingHorizontal: 24,
      borderRadius: 8,
      gap: 8,
    },
    searchButtonDisabled: {
      opacity: 0.5,
    },
    searchButtonText: {
      color: '#000000',
      fontSize: 16,
      fontWeight: '600',
    },
    categoriesSection: {
      margin: 16,
      marginTop: 0,
    },
    categoriesHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    categoriesTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: textColor,
    },
    categoriesGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
    },
    categoryCard: {
      width: (width - 64) / 3, // 3 columns with margins
      aspectRatio: 1,
      backgroundColor: inputBackgroundColor,
      borderRadius: 12,
      padding: 12,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 1,
      borderColor: borderColor,
    },
    categoryIcon: {
      fontSize: 24,
      marginBottom: 8,
    },
    categoryName: {
      fontSize: 12,
      fontWeight: '600',
      textAlign: 'center',
      color: textColor,
    },
    resultsSection: {
      margin: 16,
      marginTop: 0,
    },
    resultsHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    resultsTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: textColor,
    },
    sortButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 6,
      backgroundColor: cardBackgroundColor,
      borderWidth: 1,
      borderColor: borderColor,
      gap: 4,
    },
    sortButtonText: {
      fontSize: 14,
      color: textColor,
    },
    placeholder: {
      padding: 40,
      borderRadius: 12,
      alignItems: 'center',
      backgroundColor: cardBackgroundColor,
    },
    placeholderIcon: {
      fontSize: 48,
      marginBottom: 16,
    },
    placeholderTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      marginBottom: 8,
      color: textColor,
    },
    placeholderText: {
      fontSize: 16,
      textAlign: 'center',
      lineHeight: 24,
      color: secondaryTextColor,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor,
    },
    loadingText: {
      marginTop: 16,
      fontSize: 16,
      color: secondaryTextColor,
    },
    businessCard: {
      backgroundColor: inputBackgroundColor,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      borderWidth: 1,
      borderColor: borderColor,
    },
    businessHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    businessLogo: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: cardBackgroundColor,
      marginRight: 12,
    },
    businessInfo: {
      flex: 1,
    },
    businessName: {
      fontSize: 16,
      fontWeight: '600',
      color: textColor,
      marginBottom: 4,
    },
    businessLocation: {
      fontSize: 14,
      color: secondaryTextColor,
    },
    businessDescription: {
      fontSize: 14,
      color: secondaryTextColor,
      lineHeight: 20,
      marginBottom: 8,
    },
    businessFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    ratingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    ratingText: {
      fontSize: 14,
      fontWeight: '600',
      color: textColor,
    },
    reviewsText: {
      fontSize: 12,
      color: secondaryTextColor,
    },
    productCard: {
      backgroundColor: inputBackgroundColor,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      borderWidth: 1,
      borderColor: borderColor,
    },
    productImage: {
      width: '100%',
      height: 120,
      borderRadius: 8,
      backgroundColor: cardBackgroundColor,
      marginBottom: 12,
    },
    productName: {
      fontSize: 16,
      fontWeight: '600',
      color: textColor,
      marginBottom: 4,
    },
    productPrice: {
      fontSize: 18,
      fontWeight: 'bold',
      color: '#D4AF37',
      marginBottom: 4,
    },
    productBusiness: {
      fontSize: 14,
      color: secondaryTextColor,
    },
  });
};
