/**
 * Activity Tracking Service for React Native
 * Handles tracking user activities and business interactions
 */

import { supabase } from '@/lib/supabase';
import { fetchBatchProfiles } from '@/backend/supabase/services/customer/batchProfileService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import 'react-native-get-random-values'; // Required for uuid
import { v4 as uuidv4 } from 'uuid';

export interface ActivityData {
  id: string;
  business_profile_id: string;
  user_id: string;
  activity_type: 'like' | 'subscribe' | 'rating' | 'visit';
  rating_value?: number | null;
  created_at: string;
  is_read: boolean;
  user_profile?: {
    name?: string | null;
    avatar_url?: string | null;
    email?: string | null;
    is_business?: boolean;
    business_name?: string | null;
    business_slug?: string | null;
    logo_url?: string | null;
  };
  customer_profiles?: {
    name?: string | null;
    avatar_url?: string | null;
    email?: string | null;
  };
  business_profiles?: {
    business_name?: string | null;
    business_slug?: string | null;
    logo_url?: string | null;
  };
}

export interface ActivityResponse {
  success: boolean;
  data?: ActivityData[];
  count?: number;
  error?: string;
}

export interface VisitTrackingParams {
  businessProfileId: string;
  visitorIdentifier?: string;
}

class ActivityService {
  private visitorId: string | null = null;

  /**
   * Initialize the activity service
   */
  async initialize(): Promise<void> {
    try {
      // Get or create visitor identifier
      this.visitorId = await this.getOrCreateVisitorId();
    } catch (error) {
      console.error('Failed to initialize activity service:', error);
    }
  }

  /**
   * Get or create a unique visitor identifier
   */
  private async getOrCreateVisitorId(): Promise<string> {
    try {
      const visitorId = await AsyncStorage.getItem('dukancard_visitor_id');

      if (!visitorId) {
        const newVisitorId = uuidv4();
        await AsyncStorage.setItem('dukancard_visitor_id', newVisitorId);
        return newVisitorId;
      }

      return visitorId;
    } catch (error) {
      console.error('Error managing visitor ID:', error);
      // Fallback to generating a new ID
      return uuidv4();
    }
  }

  /**
   * Track a business card visit
   */
  async trackVisit(params: VisitTrackingParams): Promise<{ success: boolean; error?: string }> {
    const { businessProfileId, visitorIdentifier } = params;
    
    if (!businessProfileId) {
      return { success: false, error: 'Business profile ID is required' };
    }

    try {
      const identifier = visitorIdentifier || this.visitorId || await this.getOrCreateVisitorId();
      
      // Check if the business profile is online
      const { data: profile, error: profileError } = await supabase
        .from('business_profiles')
        .select('status')
        .eq('id', businessProfileId)
        .single();

      if (profileError) {
        console.error('Error checking business profile status:', profileError);
        return { success: false, error: 'Failed to verify business status' };
      }

      // Only track visits for online businesses
      if (profile?.status !== 'online') {
        return { success: true }; // Don't track offline businesses
      }

      // Record the visit
      const { error: visitError } = await supabase
        .from('card_visits')
        .insert({
          business_profile_id: businessProfileId,
          visitor_identifier: identifier,
          visited_at: new Date().toISOString(),
        });

      if (visitError) {
        console.error('Error recording visit:', visitError);
        return { success: false, error: 'Failed to record visit' };
      }

      return { success: true };
    } catch (error) {
      console.error('Error tracking visit:', error);
      return { success: false, error: 'Failed to track visit' };
    }
  }

  /**
   * Get customer activities (for the current user)
   */
  async getCustomerActivities(
    page: number = 1,
    limit: number = 20
  ): Promise<ActivityResponse> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        return { success: false, error: 'Authentication required' };
      }

      const from = (page - 1) * limit;
      const to = from + limit - 1;

      // Get activities where the user is the actor (likes, subscriptions, reviews they made)
      const { data: activities, error, count } = await supabase
        .from('business_activities')
        .select('*', { count: 'exact' })
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .range(from, to);

      if (error) {
        console.error('Error fetching customer activities:', error);
        return { success: false, error: 'Failed to fetch activities' };
      }

      // If no activities, return early
      if (!activities || activities.length === 0) {
        return {
          success: true,
          data: [],
          count: count || 0,
        };
      }

      // Get business profiles for the activities
      const businessProfileIds = activities.map((activity: any) => activity.business_profile_id);

      const { data: businessProfiles } = await supabase
        .from('business_profiles')
        .select('id, business_name, business_slug, logo_url')
        .in('id', businessProfileIds);

      // Create a map for easy lookup
      const businessProfilesMap = new Map();
      businessProfiles?.forEach((profile: any) => {
        businessProfilesMap.set(profile.id, profile);
      });

      // Enrich activities with business profile data
      const enrichedActivities = activities.map((activity: any) => ({
        ...activity,
        business_profiles: businessProfilesMap.get(activity.business_profile_id) || null
      }));

      return {
        success: true,
        data: enrichedActivities,
        count: count || 0,
      };
    } catch (error) {
      console.error('Error in getCustomerActivities:', error);
      return { success: false, error: 'Failed to fetch activities' };
    }
  }

  /**
   * Get business activities (for business owners)
   */
  async getBusinessActivities(
    businessProfileId: string,
    page: number = 1,
    limit: number = 20,
    filterBy: 'all' | 'like' | 'subscribe' | 'rating' | 'unread' = 'all'
  ): Promise<ActivityResponse> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        return { success: false, error: 'Authentication required' };
      }

      // Verify the user owns this business
      if (user.id !== businessProfileId) {
        return { success: false, error: 'Unauthorized' };
      }

      const from = (page - 1) * limit;
      const to = from + limit - 1;

      // Build the query - similar to Next.js implementation
      // We'll fetch the basic activity data first, then enrich with profile data if needed
      let query = supabase
        .from('business_activities')
        .select('*', { count: 'exact' })
        .eq('business_profile_id', businessProfileId);

      // Apply filters
      if (filterBy === 'like') {
        query = query.eq('activity_type', 'like');
      } else if (filterBy === 'subscribe') {
        query = query.eq('activity_type', 'subscribe');
      } else if (filterBy === 'rating') {
        query = query.eq('activity_type', 'rating');
      } else if (filterBy === 'unread') {
        query = query.eq('is_read', false);
      }

      const { data: activities, error, count } = await query
        .order('created_at', { ascending: false })
        .range(from, to);

      if (error) {
        // Handle specific case where offset is beyond available data
        if (error.code === 'PGRST103') {
          // No more data available
          return {
            success: true,
            data: [],
            count: count || 0,
          };
        }
        console.error('Error fetching business activities:', error);
        return { success: false, error: 'Failed to fetch activities' };
      }

      // If no activities, return early
      if (!activities || activities.length === 0) {
        return {
          success: true,
          data: [],
          count: count || 0,
        };
      }

      // Get user profiles for the activities (similar to Next.js implementation)
      const userIds = activities.map((activity: any) => activity.user_id);

      // Fetch both customer and business profiles using direct service
      const profilesResult = await fetchBatchProfiles(userIds, ['customer', 'business']);

      if (!profilesResult.success) {
        console.error('Failed to fetch profiles for activity enrichment:', profilesResult.error);
        // Continue without profile enrichment rather than failing completely
        return {
          success: true,
          data: activities.map((activity: any) => ({
            ...activity,
            customer_profile: null,
            business_profile: null,
          })),
          count: count || 0,
        };
      }

      const { customerProfiles, businessProfiles } = profilesResult.data!;

      // Combine the profiles into a map for easy lookup
      const userProfiles = new Map();

      // Add customer profiles to the map
      customerProfiles?.forEach((profile: any) => {
        userProfiles.set(profile.id, {
          name: profile.name,
          avatar_url: profile.avatar_url,
          email: profile.email,
          is_business: false
        });
      });

      // Add business profiles to the map
      businessProfiles?.forEach((profile: any) => {
        userProfiles.set(profile.id, {
          name: profile.business_name,
          avatar_url: profile.logo_url,
          business_slug: profile.business_slug,
          is_business: true
        });
      });

      // Enrich activities with user profile data
      const enrichedActivities = activities.map((activity: any) => {
        const userProfile = userProfiles.get(activity.user_id);

        // Maintain backward compatibility with legacy structure
        const legacyCustomerProfile = userProfile && !userProfile.is_business ? {
          name: userProfile.name,
          avatar_url: userProfile.avatar_url,
          email: userProfile.email
        } : null;

        const legacyBusinessProfile = userProfile && userProfile.is_business ? {
          business_name: userProfile.name,
          business_slug: userProfile.business_slug,
          logo_url: userProfile.avatar_url
        } : null;

        return {
          ...activity,
          user_profile: userProfile || null,
          // Legacy fields for backward compatibility
          customer_profiles: legacyCustomerProfile,
          business_profiles: legacyBusinessProfile
        };
      });

      return {
        success: true,
        data: enrichedActivities,
        count: count || 0,
      };
    } catch (error) {
      console.error('Error in getBusinessActivities:', error);
      return { success: false, error: 'Failed to fetch activities' };
    }
  }

  /**
   * Mark activities as read
   */
  async markActivitiesAsRead(
    businessProfileId: string,
    activityIds: string[] | 'all'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        return { success: false, error: 'Authentication required' };
      }

      // Verify the user owns this business
      if (user.id !== businessProfileId) {
        return { success: false, error: 'Unauthorized' };
      }

      let query = supabase
        .from('business_activities')
        .update({ is_read: true })
        .eq('business_profile_id', businessProfileId);

      if (activityIds !== 'all') {
        query = query.in('id', activityIds);
      }

      const { error } = await query;

      if (error) {
        console.error('Error marking activities as read:', error);
        return { success: false, error: 'Failed to mark activities as read' };
      }

      return { success: true };
    } catch (error) {
      console.error('Error in markActivitiesAsRead:', error);
      return { success: false, error: 'Failed to mark activities as read' };
    }
  }

  /**
   * Get unread activities count
   */
  async getUnreadActivitiesCount(businessProfileId: string): Promise<{ count: number; error?: string }> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        return { count: 0, error: 'Authentication required' };
      }

      // Verify the user owns this business
      if (user.id !== businessProfileId) {
        return { count: 0, error: 'Unauthorized' };
      }

      const { count, error } = await supabase
        .from('business_activities')
        .select('*', { count: 'exact', head: true })
        .eq('business_profile_id', businessProfileId)
        .eq('is_read', false);

      if (error) {
        console.error('Error fetching unread activities count:', error);
        return { count: 0, error: 'Failed to fetch unread count' };
      }

      return { count: count || 0 };
    } catch (error) {
      console.error('Error in getUnreadActivitiesCount:', error);
      return { count: 0, error: 'Failed to fetch unread count' };
    }
  }
}

// Export singleton instance
export const activityService = new ActivityService();
