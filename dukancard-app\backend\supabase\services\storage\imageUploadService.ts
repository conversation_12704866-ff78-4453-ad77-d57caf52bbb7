/**
 * Reusable Image Upload Service for React Native
 * Handles image selection, compression, and upload to Supabase storage
 */

import * as ImagePicker from 'expo-image-picker';
import { Alert } from 'react-native';
import { supabase } from '@/lib/supabase';
import { compressImageModerateClient, compressImageUltraAggressiveClient } from '@/src/utils/client-image-compression';

export interface ImageUploadOptions {
  bucket: string;
  path: string;
  compressionLevel?: 'moderate' | 'aggressive';
  targetSizeKB?: number;
  maxDimension?: number;
  quality?: number;
  allowsEditing?: boolean;
  aspect?: [number, number];
}

export interface ImageUploadResult {
  success: boolean;
  url?: string;
  error?: string;
  finalSizeKB?: number;
}

export interface ImageSelectionResult {
  success: boolean;
  uri?: string;
  size?: number;
  error?: string;
}

/**
 * Check camera permission status
 */
export async function checkCameraPermission(): Promise<{
  granted: boolean;
  canAskAgain: boolean;
  status: string;
}> {
  try {
    const { status, canAskAgain } = await ImagePicker.getCameraPermissionsAsync();
    
    return {
      granted: status === ImagePicker.PermissionStatus.GRANTED,
      canAskAgain,
      status: status,
    };
  } catch (error) {
    console.error('Error checking camera permission:', error);
    return {
      granted: false,
      canAskAgain: false,
      status: 'denied',
    };
  }
}

/**
 * Request camera permission
 */
export async function requestCameraPermission(): Promise<{
  granted: boolean;
  canAskAgain: boolean;
  status: string;
}> {
  try {
    const { status, canAskAgain } = await ImagePicker.requestCameraPermissionsAsync();
    
    return {
      granted: status === ImagePicker.PermissionStatus.GRANTED,
      canAskAgain,
      status: status,
    };
  } catch (error) {
    console.error('Error requesting camera permission:', error);
    return {
      granted: false,
      canAskAgain: false,
      status: 'denied',
    };
  }
}

/**
 * Show image picker options with permission-aware choices
 */
export async function showImagePickerOptions(): Promise<'camera' | 'gallery' | null> {
  const cameraPermission = await checkCameraPermission();
  
  return new Promise((resolve) => {
    const options = [];
    
    // Only show camera option if permission is granted or can be requested
    if (cameraPermission.granted || cameraPermission.canAskAgain) {
      options.push({
        text: 'Camera',
        onPress: () => resolve('camera'),
      });
    }
    
    // Always show gallery option
    options.push({
      text: 'Gallery',
      onPress: () => resolve('gallery'),
    });
    
    options.push({
      text: 'Cancel',
      style: 'cancel' as const,
      onPress: () => resolve(null),
    });

    Alert.alert(
      'Select Image',
      cameraPermission.granted || cameraPermission.canAskAgain 
        ? 'Choose how you want to select your image'
        : 'Camera permission denied. You can select from gallery.',
      options,
      { cancelable: true, onDismiss: () => resolve(null) }
    );
  });
}

/**
 * Open camera with proper permission handling
 */
export async function openCamera(options: Partial<ImageUploadOptions> = {}): Promise<ImagePicker.ImagePickerResult> {
  const cameraPermission = await checkCameraPermission();
  
  if (!cameraPermission.granted) {
    if (cameraPermission.canAskAgain) {
      const requestResult = await requestCameraPermission();
      if (!requestResult.granted) {
        throw new Error('Camera permission denied');
      }
    } else {
      throw new Error('Camera permission permanently denied');
    }
  }

  return await ImagePicker.launchCameraAsync({
    mediaTypes: ['images'],
    allowsEditing: options.allowsEditing ?? true,
    aspect: options.aspect ?? [1, 1],
    quality: options.quality ?? 0.8,
    base64: false,
  });
}

/**
 * Open image gallery
 */
export async function openImageGallery(options: Partial<ImageUploadOptions> = {}): Promise<ImagePicker.ImagePickerResult> {
  return await ImagePicker.launchImageLibraryAsync({
    mediaTypes: ['images'],
    allowsEditing: options.allowsEditing ?? true,
    aspect: options.aspect ?? [1, 1],
    quality: options.quality ?? 0.8,
    base64: false,
  });
}

/**
 * Select image from camera or gallery
 */
export async function selectImage(
  source: 'camera' | 'gallery',
  options: Partial<ImageUploadOptions> = {}
): Promise<ImageSelectionResult> {
  try {
    let result: ImagePicker.ImagePickerResult;
    
    if (source === 'camera') {
      result = await openCamera(options);
    } else {
      result = await openImageGallery(options);
    }

    if (result.canceled || !result.assets || result.assets.length === 0) {
      return { success: false, error: 'Image selection cancelled' };
    }

    const asset = result.assets[0];
    return {
      success: true,
      uri: asset.uri,
      size: asset.fileSize || 0,
    };
  } catch (error) {
    console.error('Error selecting image:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to select image',
    };
  }
}

/**
 * Compress image based on options
 */
async function compressImage(
  uri: string,
  originalSize: number,
  options: Partial<ImageUploadOptions>
): Promise<{ uri: string; size: number }> {
  try {
    const compressionLevel = options.compressionLevel || 'moderate';
    
    if (compressionLevel === 'aggressive') {
      const result = await compressImageUltraAggressiveClient(uri, originalSize, {
        targetSizeKB: options.targetSizeKB,
        maxDimension: options.maxDimension,
        quality: options.quality,
      });
      return {
        uri: result.uri || uri, // Fallback to original URI if compression result doesn't have uri
        size: result.finalSizeKB * 1024,
      };
    } else {
      const result = await compressImageModerateClient(uri, originalSize, {
        targetSizeKB: options.targetSizeKB,
        maxDimension: options.maxDimension,
        quality: options.quality,
      });
      return {
        uri: result.uri || uri, // Fallback to original URI if compression result doesn't have uri
        size: result.finalSizeKB * 1024,
      };
    }
  } catch (error) {
    console.error('Error compressing image:', error);
    // Fallback to original image
    const response = await fetch(uri);
    const blob = await response.blob();
    return {
      uri,
      size: blob.size,
    };
  }
}

/**
 * Upload image to Supabase storage
 */
export async function uploadImage(
  imageUri: string,
  options: ImageUploadOptions
): Promise<ImageUploadResult> {
  try {
    // Get original file size
    const response = await fetch(imageUri);
    const blob = await response.blob();
    const originalSize = blob.size;

    // Compress image
    const compressedResult = await compressImage(imageUri, originalSize, options);

    // Create file from compressed URI
    const compressedResponse = await fetch(compressedResult.uri);
    const compressedBlob = await compressedResponse.blob();

    // Generate unique filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileName = `${timestamp}_${randomString}.webp`;
    const filePath = `${options.path}/${fileName}`;

    // Upload to Supabase
    const { data, error } = await supabase.storage
      .from(options.bucket)
      .upload(filePath, compressedBlob, {
        contentType: 'image/webp',
        upsert: false,
      });

    if (error) {
      console.error('Supabase upload error:', error);
      return { success: false, error: 'Failed to upload image to storage' };
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from(options.bucket)
      .getPublicUrl(data.path);

    return {
      success: true,
      url: urlData.publicUrl,
      finalSizeKB: Math.round((compressedResult.size / 1024) * 100) / 100,
    };
  } catch (error) {
    console.error('Image upload error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to upload image',
    };
  }
}

/**
 * Complete image selection and upload flow
 */
export async function selectAndUploadImage(
  options: ImageUploadOptions
): Promise<ImageUploadResult> {
  try {
    // Show picker options
    const selectedOption = await showImagePickerOptions();
    if (!selectedOption) {
      return { success: false, error: 'Image selection cancelled' };
    }

    // Select image
    const selectionResult = await selectImage(selectedOption, options);
    if (!selectionResult.success || !selectionResult.uri) {
      return { success: false, error: selectionResult.error || 'Failed to select image' };
    }

    // Upload image
    return await uploadImage(selectionResult.uri, options);
  } catch (error) {
    console.error('Complete image flow error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to complete image upload',
    };
  }
}
