export interface BusinessPostsRow {
  id: string;
  business_id: string;
  content: string;
  image_url: string | null;
  created_at: string;
  updated_at: string;
  city_slug: string | null;
  state_slug: string | null;
  locality_slug: string | null;
  pincode: string | null;
  product_ids: string[] | null;
  mentioned_business_ids: string[] | null;
}

export interface BusinessPostsInsert {
  id?: string;
  business_id: string;
  content: string;
  image_url?: string | null;
  created_at?: string;
  updated_at?: string;
  city_slug?: string | null;
  state_slug?: string | null;
  locality_slug?: string | null;
  pincode?: string | null;
  product_ids?: string[] | null;
  mentioned_business_ids?: string[] | null;
}

export interface BusinessPostsUpdate {
  id?: string;
  business_id?: string;
  content?: string;
  image_url?: string | null;
  created_at?: string;
  updated_at?: string;
  city_slug?: string | null;
  state_slug?: string | null;
  locality_slug?: string | null;
  pincode?: string | null;
  product_ids?: string[] | null;
  mentioned_business_ids?: string[] | null;
}

export type BusinessPosts = BusinessPostsRow;
