/**
 * React Error Boundary Component
 * Catches JavaScript errors anywhere in the child component tree
 */

import React, { Component, ReactNode } from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { errorTracker } from "@/backend/supabase/services/monitoring/errorTracking";
import { devErrorMonitor } from "@/backend/supabase/services/monitoring/developmentErrorMonitoring";
import { useColorScheme } from "@/src/hooks/useColorScheme";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: any) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    // Log the error to our tracking service
    errorTracker.logError(error, {
      context: "React Error Boundary",
      componentStack: errorInfo.componentStack,
      errorBoundary: true,
    });

    // Log to development error monitor
    devErrorMonitor.logComponentError(error, errorInfo, "ErrorBoundary");

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default fallback UI with theme support
      return (
        <ThemedErrorFallback
          error={this.state.error}
          onRetry={this.handleRetry}
        />
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
    padding: 20,
  },
  errorContainer: {
    backgroundColor: "white",
    padding: 24,
    borderRadius: 12,
    alignItems: "center",
    maxWidth: 300,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 12,
    textAlign: "center",
  },
  message: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    lineHeight: 22,
    marginBottom: 24,
  },
  debugContainer: {
    backgroundColor: "#f8f8f8",
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
    width: "100%",
  },
  debugTitle: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
  },
  debugText: {
    fontSize: 12,
    color: "#666",
    fontFamily: "monospace",
  },
  retryButton: {
    backgroundColor: "#D4AF37",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
});

/**
 * Higher-order component to wrap components with error boundary
 */
// Themed error fallback component
interface ThemedErrorFallbackProps {
  error?: Error;
  onRetry: () => void;
}

function ThemedErrorFallback({ error, onRetry }: ThemedErrorFallbackProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === "dark";

  const themedStyles = StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: isDark ? "#000000" : "#ffffff",
      padding: 20,
    },
    errorContainer: {
      alignItems: "center",
      maxWidth: 400,
      width: "100%",
    },
    title: {
      fontSize: 24,
      fontWeight: "bold",
      color: isDark ? "#ffffff" : "#1f2937",
      marginBottom: 16,
      textAlign: "center",
    },
    message: {
      fontSize: 16,
      color: isDark ? "#d1d5db" : "#6b7280",
      textAlign: "center",
      lineHeight: 24,
      marginBottom: 24,
    },
    debugContainer: {
      backgroundColor: isDark ? "#1f2937" : "#f3f4f6",
      padding: 16,
      borderRadius: 8,
      marginBottom: 24,
      width: "100%",
    },
    debugTitle: {
      fontSize: 14,
      fontWeight: "bold",
      color: isDark ? "#ffffff" : "#1f2937",
      marginBottom: 8,
    },
    debugText: {
      fontSize: 12,
      color: isDark ? "#d1d5db" : "#6b7280",
      fontFamily: "monospace",
    },
    retryButton: {
      backgroundColor: "#D4AF37",
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 8,
    },
    retryButtonText: {
      color: "white",
      fontSize: 16,
      fontWeight: "600",
    },
  });

  return (
    <View style={themedStyles.container}>
      <View style={themedStyles.errorContainer}>
        <Text style={themedStyles.title}>Oops! Something went wrong</Text>
        <Text style={themedStyles.message}>
          We&apos;re sorry, but something unexpected happened. Please try again.
        </Text>

        {__DEV__ && error && (
          <View style={themedStyles.debugContainer}>
            <Text style={themedStyles.debugTitle}>Debug Info:</Text>
            <Text style={themedStyles.debugText}>{error.message}</Text>
          </View>
        )}

        <TouchableOpacity style={themedStyles.retryButton} onPress={onRetry}>
          <Text style={themedStyles.retryButtonText}>Try Again</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) {
  return function WrappedComponent(props: P) {
    return (
      <ErrorBoundary fallback={fallback}>
        <Component {...props} />
      </ErrorBoundary>
    );
  };
}
