/**
 * Slug Utilities
 * Utilities for generating and validating slugs
 */

export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

export function validateSlugFormat(slug: string): boolean {
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return slugRegex.test(slug) && slug.length >= 3 && slug.length <= 50;
}

export function isReservedSlug(slug: string): boolean {
  const reserved = ['admin', 'api', 'www', 'app', 'dashboard', 'settings'];
  return reserved.includes(slug.toLowerCase());
}
