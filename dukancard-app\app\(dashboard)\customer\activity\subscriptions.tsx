/**
 * Customer Subscriptions Activity Screen
 * Displays all businesses subscribed to by the customer with unsubscribe functionality
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { useAuth } from '@/src/contexts/AuthContext';
import { DashboardScreenContainer } from '@/src/components/layout/ScreenContainer';
import { SubscriptionCard } from '@/src/components/social/SubscriptionCard';
import { SearchComponent } from '@/src/components/social/SearchComponent';
import { EmptyState } from '@/src/components/shared/ui/EmptyState';
import { LoadingSpinner } from '@/src/components/shared/ui/LoadingSpinner';
import { SubscriptionListSkeleton } from '@/src/components/social/SkeletonLoaders';
import { subscriptionsService } from '@/backend/supabase/services/posts/socialService';
import { useTheme } from '@/src/hooks/useTheme';

import { Bell, ArrowLeft } from 'lucide-react-native';
import { createSubscriptionsActivityStyles } from '@/styles/dashboard/customer/activity/subscriptions';
import { router } from 'expo-router';

// Types
interface BusinessProfileData {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  address_line: string | null;
}

interface SubscriptionWithProfile {
  id: string;
  business_profiles: BusinessProfileData | null;
}

export default function CustomerSubscriptionsActivity() {
  const { user } = useAuth();
  const theme = useTheme();
  const styles = createSubscriptionsActivityStyles(theme);

  // State
  const [subscriptions, setSubscriptions] = useState<SubscriptionWithProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  // Fetch subscriptions
  const fetchSubscriptions = useCallback(async (
    page: number = 1, 
    search: string = '', 
    isRefresh: boolean = false
  ) => {
    if (!user) return;

    try {
      if (isRefresh) {
        setRefreshing(true);
      } else if (page === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const result = await subscriptionsService.fetchSubscriptions(user.id, page, 10, search);

      if (page === 1 || isRefresh) {
        setSubscriptions(result.items);
      } else {
        setSubscriptions(prev => [...prev, ...result.items]);
      }

      setTotalCount(result.totalCount);
      setHasMore(result.hasMore);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error fetching subscriptions:', error);
      Alert.alert('Error', 'Failed to load subscriptions. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
      setLoadingMore(false);
    }
  }, [user]);

  // Initial load
  useEffect(() => {
    if (user) {
      fetchSubscriptions(1, searchTerm);
    }
  }, [user, fetchSubscriptions, searchTerm]);

  // Handle search
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1);
    fetchSubscriptions(1, term);
  }, [fetchSubscriptions]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setCurrentPage(1);
    fetchSubscriptions(1, searchTerm, true);
  }, [fetchSubscriptions, searchTerm]);

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (!loadingMore && hasMore) {
      fetchSubscriptions(currentPage + 1, searchTerm);
    }
  }, [loadingMore, hasMore, currentPage, searchTerm, fetchSubscriptions]);

  // Handle unsubscribe
  const handleUnsubscribe = useCallback(async (subscriptionId: string) => {
    try {
      await subscriptionsService.unsubscribe(subscriptionId);
      setSubscriptions(prev => prev.filter(sub => sub.id !== subscriptionId));
      setTotalCount(prev => prev - 1);
      Alert.alert('Success', 'Successfully unsubscribed from business');
    } catch (error) {
      console.error('Error unsubscribing:', error);
      Alert.alert('Error', 'Failed to unsubscribe. Please try again.');
    }
  }, []);

  // Render header
  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <TouchableOpacity 
        style={styles.backButton} 
        onPress={() => router.back()}
      >
        <ArrowLeft size={24} color={theme.isDark ? '#ffffff' : '#1a1a1a'} />
      </TouchableOpacity>
      <Text style={styles.headerTitle}>My Subscriptions</Text>
      <View style={styles.headerSpacer} />
    </View>
  );

  // Render subscription item
  const renderSubscription = ({ item }: { item: SubscriptionWithProfile }) => (
    <SubscriptionCard
      subscription={item}
      onUnsubscribe={handleUnsubscribe}
    />
  );

  // Render footer
  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color="#D4AF37" />
      </View>
    );
  };

  // Render empty state
  const renderEmptyState = () => (
    <EmptyState
      icon={Bell}
      title="No Subscriptions"
      description="You haven't subscribed to any businesses yet. Discover businesses and follow them to see their updates."
      actionText="Discover Businesses"
      onAction={() => {
        // TODO: Navigate to discover screen
      }}
    />
  );

  if (loading && subscriptions.length === 0) {
    return (
      <DashboardScreenContainer scrollable={false}>
        {renderHeader()}
        <SubscriptionListSkeleton />
      </DashboardScreenContainer>
    );
  }

  return (
    <DashboardScreenContainer scrollable={false}>
      {renderHeader()}
      <View style={styles.container}>
        {/* Header with count */}
        <View style={styles.header}>
          <Text style={styles.countText}>
            {totalCount} {totalCount === 1 ? 'Business' : 'Businesses'} Following
          </Text>
        </View>

        {/* Search */}
        <View style={styles.searchContainer}>
          <SearchComponent
            value={searchTerm}
            onChangeText={handleSearch}
            placeholder="Search businesses..."
          />
        </View>

        {/* Subscriptions List */}
        <FlatList
          data={subscriptions}
          renderItem={renderSubscription}
          keyExtractor={(item) => item.id}
          contentContainerStyle={[
            styles.listContainer,
            subscriptions.length === 0 && styles.emptyListContainer
          ]}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={['#D4AF37']}
              tintColor="#D4AF37"
            />
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.1}
          ListFooterComponent={renderFooter}
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </DashboardScreenContainer>
  );
}
