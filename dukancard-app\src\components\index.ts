/**
 * Centralized exports for all components
 * Organized by category for better maintainability
 */

// UI Components
export { Button } from './ui/Button';
export { Input } from './ui/Input';
export { OTPInput } from './ui/OTPInput';
export { ToastProvider, useToast } from './ui/Toast';
export { DukancardLogo } from './ui/DukancardLogo';
export { GoogleIcon } from './ui/GoogleIcon';

// Feature Components
export * from './features';

// Layout Components
export {
    AuthScreenContainer, DashboardScreenContainer,
    FormScreenContainer, OnboardingScreenContainer, ScreenContainer
} from './layout/ScreenContainer';
export {
    AuthCard, AuthContainer, AuthFooter, AuthHeader
} from './layout/AuthContainer';
export {
    OnboardingCard, OnboardingContainer
} from './layout/OnboardingContainer';
export {
    DashboardCard, DashboardContainer, DashboardSection
} from './layout/DashboardContainer';

// Legacy exports for backward compatibility
// These will be gradually moved to appropriate feature directories
export { AuthGuard } from './AuthGuard';
export { ErrorBoundary } from './ErrorBoundary';
export { Collapsible } from './Collapsible';
export { ExternalLink } from './ExternalLink';
export { HapticTab } from './HapticTab';
export { HelloWave } from './HelloWave';
export { default as ParallaxScrollView } from './ParallaxScrollView';
export { ThemedText } from './ThemedText';
export { ThemedView } from './ThemedView';
