/**
 * Global Location Context for React Native
 * Manages current GPS location state and fetches location when app opens
 * and location permission is granted
 */

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";
import { useAuth } from "./AuthContext";
import {
  getCurrentLocation,
  requestLocationPermission,
} from "@/backend/supabase/services/location/locationService";

// Types
export interface CurrentLocationData {
  latitude: number;
  longitude: number;
  timestamp: number;
}

export interface LocationContextType {
  currentLocation: CurrentLocationData | null;
  isLocationLoading: boolean;
  locationPermissionGranted: boolean;
  locationError: string | null;
  refreshLocation: () => Promise<void>;
  requestLocationAccess: () => Promise<boolean>;
}

// Create context
const LocationContext = createContext<LocationContextType | undefined>(
  undefined
);

// Provider component
export function LocationProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const [currentLocation, setCurrentLocation] =
    useState<CurrentLocationData | null>(null);
  const [isLocationLoading, setIsLocationLoading] = useState(false);
  const [locationPermissionGranted, setLocationPermissionGranted] =
    useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);

  // Request location permission and access
  const requestLocationAccess = useCallback(async (): Promise<boolean> => {
    try {
      setLocationError(null);
      const permissionResult = await requestLocationPermission();
      setLocationPermissionGranted(permissionResult.granted);
      return permissionResult.granted;
    } catch (error) {
      console.error("Error requesting location permission:", error);
      setLocationError("Failed to request location permission");
      return false;
    }
  }, []);

  // Fetch current GPS location
  const fetchCurrentLocation = useCallback(async (): Promise<void> => {
    try {
      setIsLocationLoading(true);
      setLocationError(null);

      // Check permission first
      const permissionResult = await requestLocationPermission();
      setLocationPermissionGranted(permissionResult.granted);

      if (!permissionResult.granted) {
        setLocationError("Location permission not granted");
        return;
      }

      // Get current location
      const location = await getCurrentLocation();
      if (location) {
        const locationData: CurrentLocationData = {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          timestamp: Date.now(),
        };
        setCurrentLocation(locationData);
        console.log("LocationContext: Current location updated:", locationData);
      } else {
        console.log("LocationContext: Failed to get current location");
        setLocationError("Failed to get current location");
      }
    } catch (error) {
      console.error("Error fetching current location:", error);
      setLocationError(
        error instanceof Error ? error.message : "Failed to get location"
      );
    } finally {
      setIsLocationLoading(false);
    }
  }, []);

  // Refresh location manually
  const refreshLocation = useCallback(async (): Promise<void> => {
    await fetchCurrentLocation();
  }, [fetchCurrentLocation]);

  // Initialize location on app start when user is authenticated and not on login screen
  useEffect(() => {
    const initializeLocation = async () => {
      // Only fetch location if user is authenticated (not on login screen)
      if (user) {
        console.log(
          "LocationContext: Initializing location for authenticated user:",
          user.id
        );
        await fetchCurrentLocation();
      } else {
        console.log(
          "LocationContext: No user authenticated, skipping location fetch"
        );
      }
    };

    initializeLocation();
  }, [user, fetchCurrentLocation]);

  // Context value
  const contextValue: LocationContextType = {
    currentLocation,
    isLocationLoading,
    locationPermissionGranted,
    locationError,
    refreshLocation,
    requestLocationAccess,
  };

  return (
    <LocationContext.Provider value={contextValue}>
      {children}
    </LocationContext.Provider>
  );
}

// Hook to use location context
export function useLocation(): LocationContextType {
  const context = useContext(LocationContext);
  if (context === undefined) {
    throw new Error("useLocation must be used within a LocationProvider");
  }
  return context;
}

// Helper function to calculate distance between two coordinates
export function calculateDistanceFromCurrent(
  currentLocation: CurrentLocationData | null,
  targetLatitude: number,
  targetLongitude: number
): number | null {
  if (!currentLocation) {
    return null;
  }

  const R = 6371; // Radius of the Earth in kilometers
  const dLat = (targetLatitude - currentLocation.latitude) * (Math.PI / 180);
  const dLon = (targetLongitude - currentLocation.longitude) * (Math.PI / 180);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(currentLocation.latitude * (Math.PI / 180)) *
      Math.cos(targetLatitude * (Math.PI / 180)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c; // Distance in kilometers

  return distance;
}
