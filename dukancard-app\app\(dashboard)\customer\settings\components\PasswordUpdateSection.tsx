import React, { useState } from "react";
import { View, Text, Alert } from "react-native";
import { KeyRound, Info } from "lucide-react-native";
import { Input } from "@/src/components/ui/Input";
import { Button } from "@/src/components/ui/Button";
import { useTheme } from '@/src/hooks/useTheme';
import { createPasswordUpdateSectionStyles } from '@/styles/dashboard/customer/settings/password-update';

interface PasswordUpdateSectionProps {
  registrationType: 'google' | 'email' | 'phone';
}

export default function PasswordUpdateSection({
  registrationType,
}: PasswordUpdateSectionProps) {
  const theme = useTheme();
  const styles = createPasswordUpdateSectionStyles(theme);

  const isGoogleLogin = registrationType === 'google';
  const [isLoading, setIsLoading] = useState(false);
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  // Validate password complexity
  const validatePassword = (password: string): string | null => {
    if (password.length < 6) return "Password must be at least 6 characters";
    if (!/[A-Z]/.test(password)) return "Password must contain at least one uppercase letter";
    if (!/[a-z]/.test(password)) return "Password must contain at least one lowercase letter";
    if (!/[0-9]/.test(password)) return "Password must contain at least one number";
    if (!/[^A-Za-z0-9]/.test(password)) return "Password must contain at least one symbol";
    return null;
  };

  // Handle password update
  const onPasswordSubmit = async () => {
    setErrors({});

    // Validate inputs
    const newErrors: {[key: string]: string} = {};

    if (!currentPassword) newErrors.currentPassword = "Current password is required";
    if (!newPassword) newErrors.newPassword = "New password is required";
    if (!confirmPassword) newErrors.confirmPassword = "Please confirm your password";

    const passwordError = validatePassword(newPassword);
    if (passwordError) newErrors.newPassword = passwordError;

    if (newPassword !== confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setIsLoading(true);
    try {
      // TODO: Implement actual password update logic
      // For now, simulate success
      Alert.alert('Success', 'Password updated successfully!');
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <View style={styles.iconContainer}>
            <KeyRound size={16} color={theme.colors.primary} />
          </View>
          <View style={styles.titleContent}>
            <Text style={styles.title}>Password</Text>
            <Text style={styles.subtitle}>Update your password</Text>
          </View>
        </View>
      </View>

      <View style={styles.content}>
        {isGoogleLogin ? (
          <View style={styles.infoContainer}>
            <View style={styles.infoContent}>
              <Info size={16} color="#F59E0B" />
              <Text style={styles.infoText}>
                You signed up with Google. Password management is handled by your Google account.
              </Text>
            </View>
          </View>
        ) : (
          <View style={styles.form}>
            <Input
              label="Current Password"
              type="password"
              placeholder="••••••••"
              value={currentPassword}
              onChangeText={setCurrentPassword}
              error={errors.currentPassword}
              containerStyle={styles.inputContainer}
            />

            <Input
              label="New Password"
              type="password"
              placeholder="••••••••"
              value={newPassword}
              onChangeText={setNewPassword}
              error={errors.newPassword}
              containerStyle={styles.inputContainer}
            />
            <Text style={styles.helperText}>
              Must contain: 6+ chars, 1 uppercase letter, 1 lowercase letter, 1 number, 1 symbol
            </Text>

            <Input
              label="Confirm Password"
              type="password"
              placeholder="••••••••"
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              error={errors.confirmPassword}
              containerStyle={styles.inputContainer}
            />

            <View style={styles.buttonContainer}>
              <Button
                title={isLoading ? "Updating..." : "Change Password"}
                onPress={onPasswordSubmit}
                disabled={isLoading}
                loading={isLoading}
                variant="primary"
                style={styles.submitButton}
              />
            </View>
          </View>
        )}
      </View>
    </View>
  );
}
