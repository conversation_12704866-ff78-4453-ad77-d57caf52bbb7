import React, { createContext, useContext, ReactNode } from 'react';
import { AlertDialog } from '@/src/components/ui/AlertDialog';
import { useAlert } from '@/src/hooks/useAlert';

interface AlertContextType {
  showAlert: ReturnType<typeof useAlert>['showAlert'];
  hideAlert: ReturnType<typeof useAlert>['hideAlert'];
  showSuccess: ReturnType<typeof useAlert>['showSuccess'];
  showError: ReturnType<typeof useAlert>['showError'];
  showWarning: ReturnType<typeof useAlert>['showWarning'];
  showInfo: ReturnType<typeof useAlert>['showInfo'];
  showConfirm: ReturnType<typeof useAlert>['showConfirm'];
  showLogout: ReturnType<typeof useAlert>['showLogout'];
  showDelete: ReturnType<typeof useAlert>['showDelete'];
}

const AlertContext = createContext<AlertContextType | undefined>(undefined);

interface AlertProviderProps {
  children: ReactNode;
}

export function AlertProvider({ children }: AlertProviderProps) {
  const {
    alertState,
    showAlert,
    hideAlert,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showConfirm,
    showLogout,
    showDelete,
  } = useAlert();

  const contextValue: AlertContextType = {
    showAlert,
    hideAlert,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showConfirm,
    showLogout,
    showDelete,
  };

  return (
    <AlertContext.Provider value={contextValue}>
      {children}
      <AlertDialog
        visible={alertState.visible}
        type={alertState.type}
        title={alertState.title}
        message={alertState.message}
        buttons={alertState.buttons || []}
        onClose={alertState.showCloseButton !== false ? hideAlert : undefined}
        showCloseButton={alertState.showCloseButton}
        customIcon={alertState.customIcon}
      />
    </AlertContext.Provider>
  );
}

export function useAlertContext() {
  const context = useContext(AlertContext);
  if (context === undefined) {
    throw new Error('useAlertContext must be used within an AlertProvider');
  }
  return context;
}

// Export individual hook functions for convenience
export const useAlertDialog = () => {
  const context = useAlertContext();
  return {
    alert: context.showAlert,
    success: context.showSuccess,
    error: context.showError,
    warning: context.showWarning,
    info: context.showInfo,
    confirm: context.showConfirm,
    logout: context.showLogout,
    delete: context.showDelete,
  };
};
