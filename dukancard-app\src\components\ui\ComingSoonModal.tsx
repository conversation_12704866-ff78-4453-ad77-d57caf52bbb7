import React from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  Linking,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { X, ExternalLink, Globe, Smartphone } from 'lucide-react-native';

interface ComingSoonModalProps {
  visible: boolean;
  onClose: () => void;
  featureName: string;
  description?: string;
}

const { width: screenWidth } = Dimensions.get('window');

export default function ComingSoonModal({
  visible,
  onClose,
  featureName,
  description = 'This feature is available on our website with full functionality.',
}: ComingSoonModalProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Theme colors
  const backgroundColor = isDark ? '#000000' : '#FFFFFF';
  const cardBackgroundColor = isDark ? '#1A1A1A' : '#F8F9FA';
  const textColor = isDark ? '#FFFFFF' : '#000000';
  const subtitleColor = isDark ? '#B0B0B0' : '#666666';
  const borderColor = isDark ? '#333333' : '#E5E5E5';
  const goldColor = '#D4AF37';

  const handleOpenWebsite = async () => {
    try {
      const url = 'https://dukancard.in/login';
      const supported = await Linking.canOpenURL(url);
      
      if (supported) {
        await Linking.openURL(url);
      } else {
        console.error('Cannot open URL:', url);
      }
    } catch (error) {
      console.error('Error opening website:', error);
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={[styles.modalContainer, { backgroundColor: cardBackgroundColor }]}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerContent}>
              <View style={[styles.iconContainer, { backgroundColor: goldColor + '20' }]}>
                <Globe size={24} color={goldColor} />
              </View>
              <Text style={[styles.title, { color: textColor }]}>
                {featureName}
              </Text>
            </View>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <X size={24} color={subtitleColor} />
            </TouchableOpacity>
          </View>

          {/* Content */}
          <View style={styles.content}>
            <View style={[styles.mobileIconContainer, { backgroundColor: isDark ? '#2A2A2A' : '#F0F0F0' }]}>
              <Smartphone size={32} color={subtitleColor} />
            </View>
            
            <Text style={[styles.subtitle, { color: textColor }]}>
              Coming Soon to Mobile
            </Text>
            
            <Text style={[styles.description, { color: subtitleColor }]}>
              {description}
            </Text>

            <View style={[styles.featureBox, { backgroundColor: isDark ? '#2A2A2A' : '#F0F0F0', borderColor }]}>
              <Text style={[styles.featureText, { color: textColor }]}>
                ✨ Full {featureName.toLowerCase()} functionality
              </Text>
              <Text style={[styles.featureText, { color: textColor }]}>
                📊 Advanced management tools
              </Text>
              <Text style={[styles.featureText, { color: textColor }]}>
                💻 Desktop-optimized interface
              </Text>
            </View>
          </View>

          {/* Actions */}
          <View style={styles.actions}>
            <TouchableOpacity
              style={[styles.websiteButton, { backgroundColor: goldColor }]}
              onPress={handleOpenWebsite}
              activeOpacity={0.8}
            >
              <ExternalLink size={20} color="#FFFFFF" />
              <Text style={styles.websiteButtonText}>
                Open Website
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.cancelButton, { borderColor }]}
              onPress={onClose}
              activeOpacity={0.7}
            >
              <Text style={[styles.cancelButtonText, { color: subtitleColor }]}>
                Maybe Later
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modalContainer: {
    width: Math.min(screenWidth - 40, 400),
    borderRadius: 16,
    padding: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    paddingBottom: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
  },
  closeButton: {
    padding: 4,
  },
  content: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    alignItems: 'center',
  },
  mobileIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  subtitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
    marginBottom: 20,
  },
  featureBox: {
    width: '100%',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 20,
  },
  featureText: {
    fontSize: 14,
    marginBottom: 8,
    lineHeight: 20,
  },
  actions: {
    padding: 20,
    paddingTop: 0,
    gap: 12,
  },
  websiteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    gap: 8,
  },
  websiteButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    borderWidth: 1,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
});
