import { useColorScheme } from '@/src/hooks/useColorScheme';
import { useTheme } from '@/src/hooks/useTheme';
import { responsiveFontSize } from '@/lib/theme/colors';
import { Loader2 } from 'lucide-react-native';
import React from 'react';
import { StyleSheet, View } from 'react-native';

// This is the main entry point for the app after splash screen
// The splash screen is now handled in _layout.tsx before any providers load
// This component just shows a simple loader while AuthGuard handles navigation

export default function IndexScreen() {
  const theme = useTheme();
  const backgroundColor = theme.colors.background;

  // Simple loader - AuthGuard will handle all navigation logic
  return (
    <View style={[styles.container, { backgroundColor }]}>
      <Loader2 size={responsiveFontSize(32)} color={theme.colors.primary} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
