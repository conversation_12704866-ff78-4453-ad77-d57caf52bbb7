import { StyleSheet } from 'react-native';

export const createAnalyticsStyles = () => {
  return StyleSheet.create({
    container: {
      flex: 1,
    },
    content: {
      padding: 20,
      paddingTop: 60,
    },
    header: {
      alignItems: 'center',
      marginBottom: 30,
    },
    subtitle: {
      marginTop: 8,
      textAlign: 'center',
      opacity: 0.7,
    },
    metricsContainer: {
      marginBottom: 30,
    },
    metricCard: {
      padding: 20,
      marginBottom: 16,
      backgroundColor: 'rgba(10, 126, 164, 0.1)',
      borderRadius: 12,
    },
    metricTitle: {
      fontSize: 16,
      marginBottom: 8,
    },
    metricValue: {
      fontSize: 32,
      fontWeight: 'bold',
      marginBottom: 4,
    },
    metricChange: {
      fontSize: 14,
      opacity: 0.7,
      color: '#4CAF50',
    },
    chartPlaceholder: {
      padding: 40,
      alignItems: 'center',
      backgroundColor: 'rgba(10, 126, 164, 0.05)',
      borderRadius: 12,
      borderWidth: 2,
      borderColor: 'rgba(10, 126, 164, 0.2)',
      borderStyle: 'dashed',
    },
    chartText: {
      textAlign: 'center',
      opacity: 0.6,
    },
  });
};
