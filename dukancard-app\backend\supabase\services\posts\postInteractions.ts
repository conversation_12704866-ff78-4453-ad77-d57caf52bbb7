/**
 * Post Interactions Service for React Native
 * Handles like, comment, and share functionality for posts
 */

import { supabase } from '@/lib/supabase';
import { Alert, Share } from 'react-native';

export interface PostInteractionResponse {
  success: boolean;
  message: string;
  error?: string;
}

export interface PostLikeStatus {
  isLiked: boolean;
  likeCount: number;
}

/**
 * Like a post (business or customer post)
 */
export async function likePost(
  postId: string,
  postSource: 'business' | 'customer'
): Promise<PostInteractionResponse> {
  
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  
  if (authError || !user) {
    return { 
      success: false, 
      message: 'Authentication required',
      error: 'User not authenticated' 
    };
  }

  try {
    // Determine the correct table based on post source
    const tableName = postSource === 'business' ? 'business_post_likes' : 'customer_post_likes';
    const postIdColumn = postSource === 'business' ? 'business_post_id' : 'customer_post_id';

    // Insert like record
    const { error: insertError } = await supabase
      .from(tableName)
      .insert({
        user_id: user.id,
        [postIdColumn]: postId,
      });

    if (insertError) {
      // Handle duplicate like (user already liked this post)
      if (insertError.code === '23505') {
        return { 
          success: true, 
          message: 'Post already liked' 
        };
      }
      
      console.error('Error liking post:', insertError);
      return { 
        success: false, 
        message: 'Failed to like post',
        error: insertError.message 
      };
    }

    return { 
      success: true, 
      message: 'Post liked successfully' 
    };
  } catch (error) {
    console.error('Error in likePost:', error);
    return { 
      success: false, 
      message: 'Failed to like post',
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Unlike a post (business or customer post)
 */
export async function unlikePost(
  postId: string,
  postSource: 'business' | 'customer'
): Promise<PostInteractionResponse> {
  
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  
  if (authError || !user) {
    return { 
      success: false, 
      message: 'Authentication required',
      error: 'User not authenticated' 
    };
  }

  try {
    // Determine the correct table based on post source
    const tableName = postSource === 'business' ? 'business_post_likes' : 'customer_post_likes';
    const postIdColumn = postSource === 'business' ? 'business_post_id' : 'customer_post_id';

    // Delete like record
    const { error: deleteError } = await supabase
      .from(tableName)
      .delete()
      .match({
        user_id: user.id,
        [postIdColumn]: postId,
      });

    if (deleteError) {
      console.error('Error unliking post:', deleteError);
      return { 
        success: false, 
        message: 'Failed to unlike post',
        error: deleteError.message 
      };
    }

    return { 
      success: true, 
      message: 'Post unliked successfully' 
    };
  } catch (error) {
    console.error('Error in unlikePost:', error);
    return { 
      success: false, 
      message: 'Failed to unlike post',
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Get like status for a post
 */
export async function getPostLikeStatus(
  postId: string,
  postSource: 'business' | 'customer'
): Promise<PostLikeStatus> {
  
  const { data: { user } } = await supabase.auth.getUser();
  
  try {
    // Determine the correct table based on post source
    const tableName = postSource === 'business' ? 'business_post_likes' : 'customer_post_likes';
    const postIdColumn = postSource === 'business' ? 'business_post_id' : 'customer_post_id';

    // Get total like count
    const { count: likeCount } = await supabase
      .from(tableName)
      .select('*', { count: 'exact', head: true })
      .eq(postIdColumn, postId);

    // Check if current user has liked the post
    let isLiked = false;
    if (user) {
      const { data: userLike } = await supabase
        .from(tableName)
        .select('id')
        .match({
          user_id: user.id,
          [postIdColumn]: postId,
        })
        .maybeSingle();
      
      isLiked = !!userLike;
    }

    return {
      isLiked,
      likeCount: likeCount || 0,
    };
  } catch (error) {
    console.error('Error getting post like status:', error);
    return {
      isLiked: false,
      likeCount: 0,
    };
  }
}

/**
 * Share a post using React Native Share API
 */
export async function sharePost(
  postId: string,
  postContent: string,
  authorName: string,
  businessSlug?: string
): Promise<PostInteractionResponse> {
  try {
    // Always share the post URL, not the business slug
    const shareUrl = `https://dukancard.in/post/${postId}`;

    const shareMessage = `Check out this post by ${authorName}:\n\n"${postContent}"\n\n${shareUrl}`;

    const result = await Share.share({
      message: shareMessage,
      url: shareUrl,
      title: `Post by ${authorName}`,
    });

    if (result.action === Share.sharedAction) {
      return { 
        success: true, 
        message: 'Post shared successfully' 
      };
    } else if (result.action === Share.dismissedAction) {
      return { 
        success: false, 
        message: 'Share cancelled' 
      };
    }

    return { 
      success: true, 
      message: 'Share completed' 
    };
  } catch (error) {
    console.error('Error sharing post:', error);
    return { 
      success: false, 
      message: 'Failed to share post',
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Show comment modal/screen (placeholder for future implementation)
 */
export function showComments(postId: string, postSource: 'business' | 'customer'): void {
  // TODO: Implement comment modal/screen navigation
  Alert.alert(
    'Comments',
    'Comment functionality will be implemented in a future update.',
    [{ text: 'OK' }]
  );
}
