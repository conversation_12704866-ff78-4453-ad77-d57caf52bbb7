import { SupabaseClient } from "@supabase/supabase-js";
import { CustomerProfiles, BusinessProfiles } from "../types/database";

// Define a union type for the profile data
export type UserProfileData = CustomerProfiles | BusinessProfiles;

// Define the return type for the checkUserProfile function
export type UserProfileCheckResult =
  | { type: "business"; profile: BusinessProfiles }
  | { type: "customer"; profile: CustomerProfiles }
  | { type: "none"; profile: null }
  | { type: "error"; error: string };

/**
 * Checks for the existence of a user's profile in either
 * `business_profiles` or `customer_profiles` Supabase tables.
 *
 * @param userId The Supabase user ID.
 * @param supabaseClient The Supabase client instance.
 * @returns A UserProfileCheckResult indicating the profile type and data, or an error.
 */
export async function checkUserProfile(
  userId: string,
  supabaseClient: SupabaseClient
): Promise<UserProfileCheckResult> {
  try {
    // 1. Check for business profile
    const { data: businessProfile, error: businessError } = await supabaseClient
      .from("business_profiles")
      .select("*")
      .eq("id", userId)
      .maybeSingle();

    if (businessError) {
      console.error("Error checking business profile:", businessError);
      return { type: "error", error: businessError.message };
    }

    if (businessProfile) {
      return { type: "business", profile: businessProfile as BusinessProfiles };
    }

    // 2. If no business profile, check for customer profile
    const { data: customerProfile, error: customerError } = await supabaseClient
      .from("customer_profiles")
      .select("*")
      .eq("id", userId)
      .maybeSingle();

    if (customerError) {
      console.error("Error checking customer profile:", customerError);
      return { type: "error", error: customerError.message };
    }

    if (customerProfile) {
      return { type: "customer", profile: customerProfile as CustomerProfiles };
    }

    // 3. If no profile found in either table
    return { type: "none", profile: null };
  } catch (error) {
    console.error("Unexpected error in checkUserProfile:", error);
    return {
      type: "error",
      error:
        error instanceof Error ? error.message : "An unknown error occurred",
    };
  }
}
