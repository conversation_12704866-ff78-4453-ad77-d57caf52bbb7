import React, { useCallback, useRef, useState } from 'react';

export interface LoadingStateOptions {
  /**
   * Minimum duration (in ms) to keep loading state active
   * This prevents flickering for very fast operations
   * @default 300
   */
  minDuration?: number;
  
  /**
   * Delay (in ms) before resetting loading state
   * Useful for preventing flickering when operations are cancelled
   * @default 100
   */
  resetDelay?: number;
  
  /**
   * Whether to automatically reset loading state on unmount
   * @default true
   */
  resetOnUnmount?: boolean;
}

/**
 * Custom hook for managing loading states with anti-flickering mechanisms
 * 
 * Features:
 * - Minimum loading duration to prevent flickering
 * - Configurable reset delay
 * - Automatic cleanup on unmount
 * - Debounced state updates
 * 
 * @param options Configuration options for loading behavior
 * @returns Object with loading state and control functions
 */
export function useLoadingState(options: LoadingStateOptions = {}) {
  const {
    minDuration = 300,
    resetDelay = 100,
    resetOnUnmount = true,
  } = options;

  const [isLoading, setIsLoading] = useState(false);
  const loadingStartTime = useRef<number | null>(null);
  const resetTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const minDurationTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Clear any pending timeouts
  const clearTimeouts = useCallback(() => {
    if (resetTimeoutRef.current) {
      clearTimeout(resetTimeoutRef.current);
      resetTimeoutRef.current = null;
    }
    if (minDurationTimeoutRef.current) {
      clearTimeout(minDurationTimeoutRef.current);
      minDurationTimeoutRef.current = null;
    }
  }, []);

  // Start loading with minimum duration enforcement
  const startLoading = useCallback(() => {
    clearTimeouts();
    setIsLoading(true);
    loadingStartTime.current = Date.now();
  }, [clearTimeouts]);

  // Stop loading with anti-flickering mechanisms
  const stopLoading = useCallback((immediate = false) => {
    if (immediate) {
      clearTimeouts();
      setIsLoading(false);
      loadingStartTime.current = null;
      return;
    }

    const now = Date.now();
    const elapsed = loadingStartTime.current ? now - loadingStartTime.current : 0;
    const remainingMinDuration = Math.max(0, minDuration - elapsed);

    const resetLoading = () => {
      if (resetDelay > 0) {
        resetTimeoutRef.current = setTimeout(() => {
          setIsLoading(false);
          loadingStartTime.current = null;
        }, resetDelay);
      } else {
        setIsLoading(false);
        loadingStartTime.current = null;
      }
    };

    if (remainingMinDuration > 0) {
      // Wait for minimum duration before resetting
      minDurationTimeoutRef.current = setTimeout(resetLoading, remainingMinDuration);
    } else {
      // Reset immediately (with optional delay)
      resetLoading();
    }
  }, [minDuration, resetDelay, clearTimeouts]);

  // Stop loading with additional delay (useful for cancelled operations)
  const stopLoadingWithDelay = useCallback((delay: number = 300) => {
    const now = Date.now();
    const elapsed = loadingStartTime.current ? now - loadingStartTime.current : 0;
    const remainingMinDuration = Math.max(0, minDuration - elapsed);
    const totalDelay = Math.max(remainingMinDuration, delay);

    minDurationTimeoutRef.current = setTimeout(() => {
      if (resetDelay > 0) {
        resetTimeoutRef.current = setTimeout(() => {
          setIsLoading(false);
          loadingStartTime.current = null;
        }, resetDelay);
      } else {
        setIsLoading(false);
        loadingStartTime.current = null;
      }
    }, totalDelay);
  }, [minDuration, resetDelay]);

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      clearTimeouts();
      if (resetOnUnmount) {
        setIsLoading(false);
      }
    };
  }, [clearTimeouts, resetOnUnmount]);

  return {
    isLoading,
    startLoading,
    stopLoading,
    stopLoadingWithDelay,
    clearTimeouts,
  };
}

/**
 * Specialized hook for Google Sign-In loading state
 * Pre-configured with optimal settings for Google auth flows
 */
export function useGoogleSignInLoading() {
  return useLoadingState({
    minDuration: 400, // Slightly longer for auth operations
    resetDelay: 150,  // Longer delay for modal dismissal
    resetOnUnmount: true,
  });
}

/**
 * Specialized hook for form submission loading states
 * Pre-configured for form operations
 */
export function useFormSubmissionLoading() {
  return useLoadingState({
    minDuration: 200, // Shorter for form submissions
    resetDelay: 50,   // Quick reset for forms
    resetOnUnmount: true,
  });
}
