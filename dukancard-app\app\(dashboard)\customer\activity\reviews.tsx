/**
 * Customer Reviews Activity Screen
 * Displays all reviews written by the customer with edit/delete functionality
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { useAuth } from '@/src/contexts/AuthContext';
import { DashboardScreenContainer } from '@/src/components/layout/ScreenContainer';
import { ReviewCard } from '@/src/components/social/ReviewCard';
import { EmptyState } from '@/src/components/shared/ui/EmptyState';
import { LoadingSpinner } from '@/src/components/shared/ui/LoadingSpinner';
import { ReviewListSkeleton } from '@/src/components/social/SkeletonLoaders';
import { reviewsService, ReviewData } from '@/backend/supabase/services/posts/socialService';
import { SortSelector } from '@/src/components/social/SortSelector';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { Star, ArrowLeft } from 'lucide-react-native';
import { createReviewsActivityStyles } from '@/styles/dashboard/customer/activity/reviews';
import { router } from 'expo-router';

type ReviewSortOption = 'newest' | 'oldest' | 'rating_high' | 'rating_low';

export default function CustomerReviewsActivity() {
  const { user } = useAuth();
  const colorScheme = useColorScheme();
  const styles = createReviewsActivityStyles(colorScheme ?? 'light');

  // State
  const [reviews, setReviews] = useState<ReviewData[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [sortBy, setSortBy] = useState<ReviewSortOption>('newest');
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  // Fetch reviews
  const fetchReviews = useCallback(async (
    page: number = 1, 
    sort: ReviewSortOption = 'newest', 
    isRefresh: boolean = false
  ) => {
    if (!user) return;

    try {
      if (isRefresh) {
        setRefreshing(true);
      } else if (page === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const result = await reviewsService.fetchReviews(user.id, page, 10, sort);
      
      if (page === 1 || isRefresh) {
        setReviews(result.items);
      } else {
        setReviews(prev => [...prev, ...result.items]);
      }
      
      setTotalCount(result.totalCount);
      setHasMore(result.hasMore);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error fetching reviews:', error);
      Alert.alert('Error', 'Failed to load reviews. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
      setLoadingMore(false);
    }
  }, [user]);

  // Initial load
  useEffect(() => {
    if (user) {
      fetchReviews(1, sortBy);
    }
  }, [user, fetchReviews, sortBy]);

  // Handle sort change
  const handleSortChange = useCallback((newSort: ReviewSortOption) => {
    setSortBy(newSort);
    setCurrentPage(1);
    fetchReviews(1, newSort);
  }, [fetchReviews]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setCurrentPage(1);
    fetchReviews(1, sortBy, true);
  }, [fetchReviews, sortBy]);

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (!loadingMore && hasMore) {
      fetchReviews(currentPage + 1, sortBy);
    }
  }, [loadingMore, hasMore, currentPage, sortBy, fetchReviews]);

  // Handle review deletion
  const handleDeleteReview = useCallback(async (reviewId: string) => {
    try {
      await reviewsService.deleteReview(reviewId);
      setReviews(prev => prev.filter(review => review.id !== reviewId));
      setTotalCount(prev => prev - 1);
      Alert.alert('Success', 'Review deleted successfully');
    } catch (error) {
      console.error('Error deleting review:', error);
      Alert.alert('Error', 'Failed to delete review. Please try again.');
    }
  }, []);

  // Handle review update
  const handleUpdateReview = useCallback(async (reviewId: string, rating: number, reviewText: string) => {
    try {
      await reviewsService.updateReview(reviewId, rating, reviewText);
      // Update the review in the local state
      setReviews(prev => prev.map(review => 
        review.id === reviewId 
          ? { ...review, rating, review_text: reviewText, updated_at: new Date().toISOString() }
          : review
      ));
      Alert.alert('Success', 'Review updated successfully');
    } catch (error) {
      console.error('Error updating review:', error);
      Alert.alert('Error', 'Failed to update review. Please try again.');
    }
  }, []);

  // Render header
  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <TouchableOpacity 
        style={styles.backButton} 
        onPress={() => router.back()}
      >
        <ArrowLeft size={24} color={colorScheme === 'dark' ? '#ffffff' : '#1a1a1a'} />
      </TouchableOpacity>
      <Text style={styles.headerTitle}>My Reviews</Text>
      <View style={styles.headerSpacer} />
    </View>
  );

  // Render review item
  const renderReview = ({ item }: { item: ReviewData }) => (
    <ReviewCard
      review={item}
      onDelete={handleDeleteReview}
      onUpdate={handleUpdateReview}
    />
  );

  // Render footer
  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color="#D4AF37" />
      </View>
    );
  };

  // Render empty state
  const renderEmptyState = () => (
    <EmptyState
      icon={Star}
      title="No Reviews Written"
      description="You haven't written any reviews yet. Visit businesses and share your experience with others."
      actionText="Discover Businesses"
      onAction={() => {
        // TODO: Navigate to discover screen
      }}
    />
  );

  if (loading && reviews.length === 0) {
    return (
      <DashboardScreenContainer scrollable={false}>
        {renderHeader()}
        <ReviewListSkeleton />
      </DashboardScreenContainer>
    );
  }

  return (
    <DashboardScreenContainer scrollable={false}>
      {renderHeader()}
      <View style={styles.container}>
        {/* Header with count */}
        <View style={styles.header}>
          <Text style={styles.countText}>
            {totalCount} {totalCount === 1 ? 'Review' : 'Reviews'} Written
          </Text>
        </View>

        {/* Sort Selector */}
        <View style={styles.sortContainer}>
          <SortSelector
            value={sortBy}
            onValueChange={handleSortChange}
            options={[
              { label: 'Newest First', value: 'newest' },
              { label: 'Oldest First', value: 'oldest' },
              { label: 'Highest Rating', value: 'rating_high' },
              { label: 'Lowest Rating', value: 'rating_low' },
            ]}
          />
        </View>

        {/* Reviews List */}
        <FlatList
          data={reviews}
          renderItem={renderReview}
          keyExtractor={(item) => item.id}
          contentContainerStyle={[
            styles.listContainer,
            reviews.length === 0 && styles.emptyListContainer
          ]}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={['#D4AF37']}
              tintColor="#D4AF37"
            />
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.1}
          ListFooterComponent={renderFooter}
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </DashboardScreenContainer>
  );
}
