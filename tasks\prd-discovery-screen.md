# Product Requirements Document: Discovery Screen for React Native DukanCard App

## Introduction/Overview

The Discovery Screen is a comprehensive search and exploration feature that allows users to discover local businesses and products/services across India. This feature replaces the current "Coming Soon" discovery screen and provides a unified interface for both business and customer users to find relevant businesses and products based on location, category, and search criteria.

The feature combines location-based discovery with category filtering and search functionality, providing users with an intuitive way to explore the DukanCard ecosystem while maintaining consistent design patterns with the existing React Native app.

## Goals

1. **Enable Location-Based Discovery**: Allow users to discover businesses and products based on their current location or manually selected location (pincode/city/locality)
2. **Provide Category-Based Filtering**: Enable users to filter results by business categories using the existing category system
3. **Support Dual View Types**: Offer both business cards and products/services views with seamless switching
4. **Implement Persistent Location Preferences**: Save user location preferences across app sessions
5. **Show Distance Information**: Display distance from user's current location to businesses
6. **Enable Search Functionality**: Allow optional search by business name or product name
7. **Provide Infinite Scroll Pagination**: Implement efficient database-level pagination for large result sets

## User Stories

1. **As a user opening the discovery screen for the first time**, I want the app to automatically detect my location so that I can see nearby businesses and products without manual input.

2. **As a user**, I want to manually select my location (pincode, city, locality) so that I can discover businesses in areas other than my current location.

3. **As a user**, I want my location preferences to be remembered across app sessions so that I don't have to re-enter my location every time.

4. **As a user**, I want to filter businesses and products by category so that I can find specific types of services or products I'm looking for.

5. **As a user**, I want to switch between viewing business cards and products/services so that I can explore different types of content.

6. **As a user**, I want to see the distance from my current location to each business so that I can choose the most convenient options.

7. **As a user**, I want to search for specific business names or product names so that I can find exactly what I'm looking for.

8. **As a user**, I want to tap on a business card to view the full business profile so that I can learn more about the business.

9. **As a user**, I want to tap on a product to view detailed product information so that I can make informed decisions.

10. **As a user**, I want to scroll through results infinitely so that I can explore all available options without pagination interruptions.

## Functional Requirements

1. **Location Management**
   - 1.1 The system must automatically request location permission and fetch GPS coordinates on first app launch
   - 1.2 The system must allow manual location selection via pincode, city, and locality inputs
   - 1.3 The system must persist selected location preferences using AsyncStorage
   - 1.4 The system must provide a "Use Current Location" button to update location from GPS
   - 1.5 The system must handle location permission denial gracefully with fallback options

2. **Search and Filtering**
   - 2.1 The system must provide a search input for business names (optional)
   - 2.2 The system must provide a search input for product/service names (optional)
   - 2.3 The system must integrate with existing category picker for category-based filtering
   - 2.4 The system must support combined filtering (location + category + search terms)
   - 2.5 The system must implement search debouncing to optimize API calls

3. **View Management**
   - 3.1 The system must provide toggle between "Business Cards" and "Products" views
   - 3.2 The system must maintain separate pagination states for each view type
   - 3.3 The system must preserve search and filter states when switching between views

4. **Results Display**
   - 4.1 The system must display business cards with distance information in kilometers
   - 4.2 The system must use existing product card components for product display
   - 4.3 The system must create new business card components optimized for discovery
   - 4.4 The system must handle businesses without GPS coordinates gracefully
   - 4.5 The system must implement infinite scroll pagination using FlatList

5. **Navigation**
   - 5.1 The system must navigate to individual business profiles when business cards are tapped
   - 5.2 The system must navigate to individual product pages when products are tapped
   - 5.3 The system must maintain navigation history for back button functionality

6. **Data Management**
   - 6.1 The system must use direct Supabase queries with RLS for data fetching
   - 6.2 The system must implement efficient database-level pagination
   - 6.3 The system must cache search results appropriately to improve performance
   - 6.4 The system must handle loading states and error conditions

7. **Distance Calculation**
   - 7.1 The system must calculate and display distance from user's current GPS location to businesses
   - 7.2 The system must handle businesses without GPS coordinates by showing "Distance unavailable"
   - 7.3 The system must update distances when user location changes

## Non-Goals (Out of Scope)

1. **Advanced Filtering**: Complex filters like price range, ratings, or business hours are not included in this initial implementation
2. **Map View**: Geographic map display of results is not included
3. **Favorites/Bookmarks**: Saving favorite businesses or products is not included
4. **Reviews Integration**: Displaying or managing reviews is not part of this feature
5. **Social Features**: Sharing, liking, or commenting on discoveries is not included
6. **Offline Mode**: Offline discovery functionality is not included
7. **Push Notifications**: Location-based notifications are not included

## Design Considerations

1. **Existing Component Reuse**: Utilize existing ProductCard components and create new BusinessCard components following the same design patterns
2. **Category Picker Integration**: Use the existing CategoryBottomSheetPicker component for category selection
3. **Theme Consistency**: Follow the existing React Native app's color scheme and typography
4. **Loading States**: Implement skeleton loaders consistent with existing feed components
5. **Error Handling**: Use existing error handling patterns and toast notifications
6. **Safe Area Handling**: Ensure proper safe area handling for different device types

## Technical Considerations

1. **State Management**: Use React Context pattern similar to existing ThemeContext and AuthContext
2. **Location Services**: Integrate with existing location service utilities in `backend/supabase/services/location/locationService.ts`
3. **Database Queries**: Leverage existing business and product search functions, adapting them for discovery use cases
4. **Performance**: Implement proper memoization and optimization for smooth scrolling
5. **AsyncStorage**: Use existing AsyncStorage patterns for location preference persistence
6. **Navigation**: Integrate with existing Expo Router navigation patterns

## Success Metrics

1. **User Engagement**: Increase in discovery screen usage compared to previous "Coming Soon" screen
2. **Location Accuracy**: 95% of users successfully set their location (either automatically or manually)
3. **Search Success Rate**: 80% of searches return relevant results
4. **Navigation Success**: 70% of discovery interactions lead to business profile or product page visits
5. **Performance**: Discovery screen loads within 2 seconds on average
6. **Error Rate**: Less than 5% of discovery sessions encounter errors

## Open Questions

1. **Caching Strategy**: How long should search results be cached before requiring refresh?
2. **Distance Radius**: What should be the default search radius for location-based discovery?
3. **Result Limits**: What should be the maximum number of results to show per category/search?
4. **Analytics**: What specific user interaction events should be tracked for analytics?
5. **Accessibility**: Are there specific accessibility requirements for the discovery interface?
