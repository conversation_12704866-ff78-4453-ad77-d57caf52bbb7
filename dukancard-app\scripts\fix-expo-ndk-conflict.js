#!/usr/bin/env node

/**
 * Fix for Expo NDK build conflict
 * 
 * This script removes the conflicting externalNativeBuild configuration
 * from expo-modules-core that prevents custom NDK builds from working.
 * 
 * Run this script after npm install to fix the conflict.
 */

const fs = require('fs');
const path = require('path');

const EXPO_MODULES_CORE_BUILD_GRADLE = path.join(
  __dirname,
  '../node_modules/expo-modules-core/android/build.gradle'
);

function fixExpoNdkConflict() {
  console.log('🔧 Fixing Expo NDK build conflict...');
  
  if (!fs.existsSync(EXPO_MODULES_CORE_BUILD_GRADLE)) {
    console.log('❌ expo-modules-core build.gradle not found');
    return false;
  }

  let content = fs.readFileSync(EXPO_MODULES_CORE_BUILD_GRADLE, 'utf8');
  
  // Check if already fixed
  if (content.includes('// Removed externalNativeBuild to avoid conflicts')) {
    console.log('✅ Expo NDK conflict already fixed');
    return true;
  }

  // Remove the conflicting externalNativeBuild blocks
  const externalNativeBuildRegex = /\s*externalNativeBuild\s*\{[\s\S]*?\}\s*\}/g;
  
  // Find the defaultConfig section and replace externalNativeBuild
  const defaultConfigRegex = /(defaultConfig\s*\{[\s\S]*?)externalNativeBuild\s*\{[\s\S]*?\}\s*(\}[\s\S]*?)\s*externalNativeBuild\s*\{[\s\S]*?\}/;
  
  if (defaultConfigRegex.test(content)) {
    content = content.replace(defaultConfigRegex, (match, before, after) => {
      return before + '// Removed externalNativeBuild to avoid conflicts with custom NDK builds\n  ' + after;
    });
    
    fs.writeFileSync(EXPO_MODULES_CORE_BUILD_GRADLE, content);
    console.log('✅ Fixed Expo NDK build conflict');
    return true;
  } else {
    console.log('❌ Could not find externalNativeBuild blocks to remove');
    return false;
  }
}

if (require.main === module) {
  fixExpoNdkConflict();
}

module.exports = { fixExpoNdkConflict };
