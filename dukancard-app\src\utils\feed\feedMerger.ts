import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';
import { applyDiversityRules } from './diversityEngine';

/**
 * Feed Merger - Intelligently combines customer and business posts
 * Maintains chronological flow while respecting business prioritization
 * Inspired by Facebook's News Feed algorithm approach
 */

export interface MergeOptions {
  maintainChronologicalFlow?: boolean;
  diversityEnabled?: boolean;
  businessPostWeight?: number; // 0-1, higher means more business posts
}

/**
 * Merge customer and business posts intelligently
 * Customer posts maintain chronological order
 * Business posts get prioritized but still respect overall timeline
 */
export function mergeCustomerAndBusinessPosts(
  customerPosts: UnifiedPost[],
  prioritizedBusinessPosts: UnifiedPost[],
  options: MergeOptions = {}
): UnifiedPost[] {
  const {
    maintainChronologicalFlow = true,
    diversityEnabled = true,
    businessPostWeight = 0.6 // Slightly favor business posts
  } = options;

  if (customerPosts.length === 0) {
    return diversityEnabled ? applyDiversityRules(prioritizedBusinessPosts) : prioritizedBusinessPosts;
  }
  
  if (prioritizedBusinessPosts.length === 0) {
    return diversityEnabled ? applyDiversityRules(customerPosts) : customerPosts;
  }

  let mergedPosts: UnifiedPost[] = [];

  if (maintainChronologicalFlow) {
    // Interleave posts based on timestamp while respecting business prioritization
    mergedPosts = interleavePostsByTimestamp(customerPosts, prioritizedBusinessPosts, businessPostWeight);
  } else {
    // Simple concatenation with business posts first
    mergedPosts = [...prioritizedBusinessPosts, ...customerPosts];
  }

  // Apply diversity rules to prevent consecutive same-author posts
  return diversityEnabled ? applyDiversityRules(mergedPosts) : mergedPosts;
}

/**
 * Interleave posts by timestamp while giving weight to business posts
 * This creates a natural flow similar to Instagram's main feed
 */
function interleavePostsByTimestamp(
  customerPosts: UnifiedPost[],
  businessPosts: UnifiedPost[],
  businessWeight: number
): UnifiedPost[] {
  const result: UnifiedPost[] = [];
  let customerIndex = 0;
  let businessIndex = 0;
  
  // Sort both arrays by timestamp (latest first)
  const sortedCustomerPosts = [...customerPosts].sort((a, b) => 
    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  );
  const sortedBusinessPosts = [...businessPosts].sort((a, b) => 
    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  );

  while (customerIndex < sortedCustomerPosts.length || businessIndex < sortedBusinessPosts.length) {
    const customerPost = sortedCustomerPosts[customerIndex];
    const businessPost = sortedBusinessPosts[businessIndex];

    if (!customerPost && businessPost) {
      // Only business posts left
      result.push(businessPost);
      businessIndex++;
    } else if (customerPost && !businessPost) {
      // Only customer posts left
      result.push(customerPost);
      customerIndex++;
    } else if (customerPost && businessPost) {
      // Both available - decide based on timestamp and weight
      const customerTime = new Date(customerPost.created_at).getTime();
      const businessTime = new Date(businessPost.created_at).getTime();
      
      // Apply business weight to the decision
      const timeDifference = Math.abs(customerTime - businessTime);
      const shouldFavorBusiness = Math.random() < businessWeight;
      
      if (businessTime >= customerTime || (timeDifference < 3600000 && shouldFavorBusiness)) {
        // Business post is newer OR within 1 hour and we should favor business
        result.push(businessPost);
        businessIndex++;
      } else {
        // Customer post is significantly newer
        result.push(customerPost);
        customerIndex++;
      }
    }
  }

  return result;
}

/**
 * Create time-based buckets for more sophisticated merging
 * Groups posts by time periods (e.g., last hour, last day, etc.)
 */
export function createTimeBuckets(posts: UnifiedPost[], bucketSizeHours: number = 6): Map<number, UnifiedPost[]> {
  const buckets = new Map<number, UnifiedPost[]>();
  const bucketSizeMs = bucketSizeHours * 60 * 60 * 1000;
  
  posts.forEach(post => {
    const postTime = new Date(post.created_at).getTime();
    const bucketKey = Math.floor(postTime / bucketSizeMs);
    
    if (!buckets.has(bucketKey)) {
      buckets.set(bucketKey, []);
    }
    buckets.get(bucketKey)!.push(post);
  });
  
  return buckets;
}

/**
 * Merge posts using time buckets for more natural distribution
 * This approach is similar to how Twitter's timeline works
 */
export function mergeUsingTimeBuckets(
  customerPosts: UnifiedPost[],
  businessPosts: UnifiedPost[],
  bucketSizeHours: number = 6
): UnifiedPost[] {
  const customerBuckets = createTimeBuckets(customerPosts, bucketSizeHours);
  const businessBuckets = createTimeBuckets(businessPosts, bucketSizeHours);
  
  // Get all unique bucket keys and sort them (newest first)
  const allBucketKeys = new Set([
    ...Array.from(customerBuckets.keys()),
    ...Array.from(businessBuckets.keys())
  ]);
  
  const sortedBucketKeys = Array.from(allBucketKeys).sort((a, b) => b - a);
  
  const result: UnifiedPost[] = [];
  
  sortedBucketKeys.forEach(bucketKey => {
    const customerPostsInBucket = customerBuckets.get(bucketKey) || [];
    const businessPostsInBucket = businessBuckets.get(bucketKey) || [];
    
    // Merge posts within this time bucket
    const bucketMerged = mergeCustomerAndBusinessPosts(
      customerPostsInBucket,
      businessPostsInBucket,
      { maintainChronologicalFlow: true, diversityEnabled: true }
    );
    
    result.push(...bucketMerged);
  });
  
  return result;
}
