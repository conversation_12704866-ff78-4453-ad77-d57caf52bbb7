pluginManagement {
  def tempFile = new File(rootDir, "temp.js")
tempFile.write("console.log(require.resolve('@react-native/gradle-plugin/package.json', { paths: [require.resolve('react-native/package.json')] }));")
def reactNativeGradlePlugin = new File(
    providers.exec {
      workingDir(rootDir)
      commandLine("node", "temp.js")
    }.standardOutput.asText.get().trim()
  ).getParentFile().absolutePath
  includeBuild(reactNativeGradlePlugin)
  
  def tempFile2 = new File(rootDir, "temp2.js")
tempFile2.write("console.log(require.resolve('expo-modules-autolinking/package.json', { paths: [require.resolve('expo/package.json')] }));")
def expoPluginsPath = new File(
    providers.exec {
      workingDir(rootDir)
      commandLine("node", "temp2.js")
    }.standardOutput.asText.get().trim(),
    "../android/expo-gradle-plugin"
  ).absolutePath
  includeBuild(expoPluginsPath)
}

plugins {
  id("com.facebook.react.settings")
  id("expo-autolinking-settings")
}

extensions.configure(com.facebook.react.ReactSettingsExtension) { ex ->
  if (System.getenv('EXPO_USE_COMMUNITY_AUTOLINKING') == '1') {
    ex.autolinkLibrariesFromCommand()
  } else {
    ex.autolinkLibrariesFromCommand(expoAutolinking.rnConfigCommand)
  }
}
expoAutolinking.useExpoModules()

rootProject.name = 'Dukancard'

expoAutolinking.useExpoVersionCatalog()

include ':app'
includeBuild(expoAutolinking.reactNativeGradlePlugin)
