import { supabase } from "@/lib/supabase";
import { UnifiedPost } from "./unifiedFeed";
import { PostService } from '@/backend/supabase/services/posts/postService';

// Response type for single post fetch
export interface SinglePostResponse {
  success: boolean;
  message: string;
  data?: UnifiedPost;
  error?: string;
}

/**
 * Function to fetch a single post by ID from the unified_posts view
 * This function handles both business and customer posts for React Native with retry mechanism
 */
export async function fetchSinglePost(postId: string, retryCount = 0): Promise<SinglePostResponse> {
  const maxRetries = 3;
  try {
    // Validate input
    if (!postId || typeof postId !== 'string') {
      return {
        success: false,
        message: 'Invalid post ID provided',
        error: 'INVALID_POST_ID'
      };
    }

    // Fetch post from unified_posts view
    const { data, error } = await PostService.fetchSinglePost(postId);

    if (error) {
      console.error('Error fetching single post:', error);
      
      // Handle specific error cases
      if (error.code === 'PGRST116') {
        return {
          success: false,
          message: 'Post not found',
          error: 'POST_NOT_FOUND'
        };
      }

      return {
        success: false,
        message: 'Failed to fetch post',
        error: error.message
      };
    }

    if (!data) {
      return {
        success: false,
        message: 'Post not found',
        error: 'POST_NOT_FOUND'
      };
    }

    // Validate post data structure
    const post: UnifiedPost = {
      id: data.id,
      post_source: data.post_source,
      author_id: data.author_id,
      content: data.content || '',
      image_url: data.image_url,
      created_at: data.created_at,
      updated_at: data.updated_at,
      city_slug: data.city_slug,
      state_slug: data.state_slug,
      locality_slug: data.locality_slug,
      pincode: data.pincode,
      product_ids: data.product_ids || [],
      mentioned_business_ids: data.mentioned_business_ids || [],
      author_name: data.author_name,
      author_avatar: data.author_avatar,
      business_slug: data.business_slug,
      phone: data.phone,
      whatsapp_number: data.whatsapp_number,
      business_plan: data.business_plan,
    };

    return {
      success: true,
      message: 'Post fetched successfully',
      data: post
    };

  } catch (error) {
    console.error('Unexpected error in fetchSinglePost:', error);

    // Retry logic for network errors
    if (retryCount < maxRetries && error instanceof Error &&
        (error.message.includes('network') || error.message.includes('timeout') || error.message.includes('fetch'))) {
      await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // Exponential backoff
      return fetchSinglePost(postId, retryCount + 1);
    }

    return {
      success: false,
      message: 'An unexpected error occurred',
      error: error instanceof Error ? error.message : 'UNKNOWN_ERROR'
    };
  }
}
