import React, { useEffect, useRef } from 'react';
import { View, Animated } from 'react-native';
import { createSkeletonLoaderStyles } from '@/styles/ui/SkeletonLoader-styles';
import { useColorScheme } from '@/src/hooks/useColorScheme';

interface SkeletonLoaderProps {
  width?: number | string;
  height?: number;
  borderRadius?: number;
  style?: any;
  animated?: boolean;
}

export const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  width = '100%',
  height = 20,
  borderRadius = 4,
  style,
  animated = true,
}) => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const animatedValue = useRef(new Animated.Value(0)).current;
  const styles = createSkeletonLoaderStyles(isDark);

  useEffect(() => {
    if (!animated) return;

    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: false,
        }),
      ])
    );

    animation.start();

    return () => animation.stop();
  }, [animatedValue, animated]);

  const animatedStyle = animated ? {
    opacity: animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [0.3, 0.7],
    }),
  } : {};

  return (
    <Animated.View
      style={[
        styles.skeleton,
        {
          width,
          height,
          borderRadius,
        },
        animatedStyle,
        style,
      ]}
    />
  );
};

// Business Card Skeleton
export const BusinessCardSkeleton: React.FC = () => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createSkeletonLoaderStyles(isDark);

  return (
    <View style={styles.businessCard}>
      {/* Header */}
      <View style={styles.businessCardHeader}>
        <SkeletonLoader width={60} height={60} borderRadius={30} />
        <View style={styles.businessCardInfo}>
          <SkeletonLoader width="80%" height={16} />
          <SkeletonLoader width="60%" height={12} style={{ marginTop: 8 }} />
        </View>
      </View>

      {/* Stats */}
      <View style={styles.businessCardStats}>
        <View style={styles.statItem}>
          <SkeletonLoader width={20} height={20} borderRadius={10} />
          <SkeletonLoader width={30} height={14} style={{ marginTop: 4 }} />
        </View>
        <View style={styles.statItem}>
          <SkeletonLoader width={20} height={20} borderRadius={10} />
          <SkeletonLoader width={30} height={14} style={{ marginTop: 4 }} />
        </View>
        <View style={styles.statItem}>
          <SkeletonLoader width={20} height={20} borderRadius={10} />
          <SkeletonLoader width={30} height={14} style={{ marginTop: 4 }} />
        </View>
      </View>

      {/* Content */}
      <View style={styles.businessCardContent}>
        <SkeletonLoader width="100%" height={14} />
        <SkeletonLoader width="90%" height={14} style={{ marginTop: 8 }} />
        <SkeletonLoader width="70%" height={14} style={{ marginTop: 8 }} />
      </View>

      {/* Actions */}
      <View style={styles.businessCardActions}>
        <SkeletonLoader width="48%" height={36} borderRadius={8} />
        <SkeletonLoader width="48%" height={36} borderRadius={8} />
      </View>
    </View>
  );
};

// Profile Skeleton
export const ProfileSkeleton: React.FC = () => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createSkeletonLoaderStyles(isDark);

  return (
    <View style={styles.profile}>
      {/* Avatar and basic info */}
      <View style={styles.profileHeader}>
        <SkeletonLoader width={100} height={100} borderRadius={50} />
        <SkeletonLoader width="60%" height={20} style={{ marginTop: 16 }} />
        <SkeletonLoader width="40%" height={16} style={{ marginTop: 8 }} />
      </View>

      {/* Stats row */}
      <View style={styles.profileStats}>
        <View style={styles.profileStatItem}>
          <SkeletonLoader width={40} height={24} />
          <SkeletonLoader width={60} height={14} style={{ marginTop: 4 }} />
        </View>
        <View style={styles.profileStatItem}>
          <SkeletonLoader width={40} height={24} />
          <SkeletonLoader width={60} height={14} style={{ marginTop: 4 }} />
        </View>
        <View style={styles.profileStatItem}>
          <SkeletonLoader width={40} height={24} />
          <SkeletonLoader width={60} height={14} style={{ marginTop: 4 }} />
        </View>
      </View>

      {/* Menu items */}
      <View style={styles.profileMenu}>
        {[1, 2, 3, 4, 5].map((item) => (
          <View key={item} style={styles.profileMenuItem}>
            <SkeletonLoader width={24} height={24} borderRadius={4} />
            <SkeletonLoader width="70%" height={16} style={{ marginLeft: 16 }} />
            <SkeletonLoader width={16} height={16} borderRadius={8} />
          </View>
        ))}
      </View>
    </View>
  );
};

// Search Results Skeleton
export const SearchResultsSkeleton: React.FC = () => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createSkeletonLoaderStyles(isDark);

  return (
    <View style={styles.searchResults}>
      {[1, 2, 3, 4, 5].map((item) => (
        <View key={item} style={styles.searchResultItem}>
          <SkeletonLoader width={50} height={50} borderRadius={25} />
          <View style={styles.searchResultInfo}>
            <SkeletonLoader width="80%" height={16} />
            <SkeletonLoader width="60%" height={14} style={{ marginTop: 6 }} />
            <SkeletonLoader width="40%" height={12} style={{ marginTop: 4 }} />
          </View>
        </View>
      ))}
    </View>
  );
};

// Enhanced Public Card Header Skeleton
export const PublicCardHeaderSkeleton: React.FC = () => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createSkeletonLoaderStyles(isDark);

  return (
    <View style={styles.publicCardHeader}>
      {/* Top Row - Logo and QR Code in Two Columns */}
      <View style={styles.publicCardHeaderTop}>
        {/* Left Column - Business Logo */}
        <View style={styles.publicCardHeaderLogoContainer}>
          <SkeletonLoader width={104} height={104} borderRadius={52} />
        </View>

        {/* Right Column - QR Code */}
        <View style={styles.publicCardHeaderQRContainer}>
          <SkeletonLoader width={80} height={80} borderRadius={8} />
        </View>
      </View>

      {/* Business Info Section */}
      <View style={styles.publicCardHeaderBusinessInfo}>
        <SkeletonLoader width="60%" height={24} style={{ marginBottom: 8 }} />
        <SkeletonLoader width="40%" height={16} />
      </View>
    </View>
  );
};

// Enhanced Public Card Stats Skeleton
export const PublicCardStatsSkeleton: React.FC = () => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createSkeletonLoaderStyles(isDark);

  return (
    <View style={styles.publicCardStats}>
      {/* Metrics Row */}
      <View style={styles.publicCardStatsRow}>
        <View style={styles.publicCardStatItem}>
          <SkeletonLoader width={20} height={20} borderRadius={10} style={{ marginBottom: 4 }} />
          <SkeletonLoader width={30} height={16} style={{ marginBottom: 2 }} />
          <SkeletonLoader width={40} height={12} />
        </View>
        <View style={styles.publicCardStatItem}>
          <SkeletonLoader width={20} height={20} borderRadius={10} style={{ marginBottom: 4 }} />
          <SkeletonLoader width={30} height={16} style={{ marginBottom: 2 }} />
          <SkeletonLoader width={50} height={12} />
        </View>
        <View style={styles.publicCardStatItem}>
          <SkeletonLoader width={20} height={20} borderRadius={10} style={{ marginBottom: 4 }} />
          <SkeletonLoader width={25} height={16} style={{ marginBottom: 2 }} />
          <SkeletonLoader width={35} height={12} />
        </View>
      </View>

      {/* Interactive Buttons Row */}
      <View style={styles.publicCardActionsRow}>
        <SkeletonLoader width="30%" height={40} borderRadius={20} />
        <SkeletonLoader width="30%" height={40} borderRadius={20} />
        <SkeletonLoader width="30%" height={40} borderRadius={20} />
      </View>

      {/* Contact Buttons Row */}
      <View style={styles.publicCardContactRow}>
        <SkeletonLoader width="48%" height={44} borderRadius={8} />
        <SkeletonLoader width="48%" height={44} borderRadius={8} />
      </View>

      {/* Social Media Buttons Row */}
      <View style={styles.publicCardSocialRow}>
        <SkeletonLoader width="30%" height={40} borderRadius={20} />
        <SkeletonLoader width="30%" height={40} borderRadius={20} />
        <SkeletonLoader width="30%" height={40} borderRadius={20} />
      </View>
    </View>
  );
};

// Enhanced Public Card Contact Buttons Skeleton
export const PublicCardContactSkeleton: React.FC = () => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createSkeletonLoaderStyles(isDark);

  return (
    <View style={styles.publicCardContact}>
      <View style={styles.publicCardContactRow}>
        <SkeletonLoader width="48%" height={44} borderRadius={8} />
        <SkeletonLoader width="48%" height={44} borderRadius={8} />
      </View>
    </View>
  );
};

// Enhanced Public Card Social Buttons Skeleton
export const PublicCardSocialSkeleton: React.FC = () => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createSkeletonLoaderStyles(isDark);

  return (
    <View style={styles.publicCardSocial}>
      <View style={styles.publicCardSocialRow}>
        <SkeletonLoader width="30%" height={40} borderRadius={20} />
        <SkeletonLoader width="30%" height={40} borderRadius={20} />
        <SkeletonLoader width="30%" height={40} borderRadius={20} />
      </View>
    </View>
  );
};

// Complete Public Card Skeleton (for initial loading)
export const PublicCardSkeleton: React.FC = () => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createSkeletonLoaderStyles(isDark);

  return (
    <View style={styles.publicCardContainer}>
      <PublicCardHeaderSkeleton />
      <PublicCardStatsSkeleton />

      {/* Ad Section Skeleton */}
      <View style={styles.publicCardAdSection}>
        <SkeletonLoader width="100%" height={180} borderRadius={0} />
      </View>

      {/* Tab Navigation Skeleton */}
      <View style={styles.publicCardTabNav}>
        <SkeletonLoader width="20%" height={40} borderRadius={20} />
        <SkeletonLoader width="25%" height={40} borderRadius={20} />
        <SkeletonLoader width="20%" height={40} borderRadius={20} />
        <SkeletonLoader width="20%" height={40} borderRadius={20} />
      </View>

      {/* Default tab content skeleton */}
      <PublicCardTabSkeleton tab="products" />
    </View>
  );
};

// Full Screen Public Card Skeleton (for navigation loading)
export const PublicCardNavigationSkeleton: React.FC = () => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createSkeletonLoaderStyles(isDark);

  return (
    <View style={styles.publicCardNavigationContainer}>
      {/* Header with back button */}
      <View style={styles.publicCardNavigationHeader}>
        <SkeletonLoader width={24} height={24} borderRadius={12} />
      </View>

      {/* Full public card skeleton */}
      <PublicCardSkeleton />
    </View>
  );
};

// Public Card Tab Content Skeleton
export const PublicCardTabSkeleton: React.FC<{ tab: 'about' | 'products' | 'gallery' | 'reviews' }> = ({ tab }) => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createSkeletonLoaderStyles(isDark);

  if (tab === 'about') {
    return (
      <View style={styles.publicCardAbout}>
        {/* Business Info Table */}
        <View style={styles.publicCardAboutSection}>
          <SkeletonLoader width="30%" height={18} style={{ marginBottom: 12 }} />
          {[1, 2, 3, 4, 5].map((item) => (
            <View key={item} style={styles.publicCardAboutRow}>
              <SkeletonLoader width="35%" height={14} />
              <SkeletonLoader width="55%" height={14} />
            </View>
          ))}
        </View>

        {/* Contact Info */}
        <View style={styles.publicCardAboutSection}>
          <SkeletonLoader width="25%" height={18} style={{ marginBottom: 12 }} />
          <View style={styles.publicCardAboutRow}>
            <SkeletonLoader width={20} height={20} borderRadius={10} />
            <SkeletonLoader width="60%" height={16} style={{ marginLeft: 12 }} />
          </View>
          <View style={styles.publicCardAboutRow}>
            <SkeletonLoader width={20} height={20} borderRadius={10} />
            <SkeletonLoader width="50%" height={16} style={{ marginLeft: 12 }} />
          </View>
        </View>

        {/* Hours */}
        <View style={styles.publicCardAboutSection}>
          <SkeletonLoader width="35%" height={18} style={{ marginBottom: 12 }} />
          {[1, 2, 3].map((item) => (
            <View key={item} style={styles.publicCardAboutRow}>
              <SkeletonLoader width="25%" height={14} />
              <SkeletonLoader width="40%" height={14} />
            </View>
          ))}
        </View>
      </View>
    );
  }

  if (tab === 'products') {
    return (
      <View style={styles.publicCardProducts}>
        {/* Search and Sort */}
        <View style={styles.publicCardProductsHeader}>
          <SkeletonLoader width="100%" height={40} borderRadius={8} />
          <View style={styles.publicCardProductsFilters}>
            <SkeletonLoader width="30%" height={36} borderRadius={18} />
            <SkeletonLoader width="25%" height={36} borderRadius={18} />
          </View>
        </View>

        {/* Product Grid */}
        <View style={styles.publicCardProductsGrid}>
          {[1, 2, 3, 4].map((item) => (
            <View key={item} style={styles.publicCardProductItem}>
              <View style={styles.productCard}>
                <SkeletonLoader width="100%" height={120} borderRadius={8} />
                <View style={styles.productCardContent}>
                  <SkeletonLoader width="90%" height={14} style={{ marginTop: 8 }} />
                  <SkeletonLoader width="70%" height={12} style={{ marginTop: 4 }} />
                  <View style={styles.productCardPriceRow}>
                    <SkeletonLoader width="40%" height={16} style={{ marginTop: 8 }} />
                    <SkeletonLoader width="25%" height={12} style={{ marginTop: 8 }} />
                  </View>
                </View>
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  }

  if (tab === 'gallery') {
    return (
      <View style={styles.publicCardGallery}>
        {[1, 2, 3, 4, 5, 6].map((item) => (
          <SkeletonLoader
            key={item}
            width="48%"
            height={120}
            borderRadius={8}
            style={{ marginBottom: 8 }}
          />
        ))}
      </View>
    );
  }

  if (tab === 'reviews') {
    return (
      <View style={styles.publicCardReviews}>
        {/* Review Stats */}
        <View style={styles.publicCardReviewsStats}>
          <SkeletonLoader width="40%" height={24} />
          <SkeletonLoader width="60%" height={16} style={{ marginTop: 8 }} />
        </View>

        {/* Review List */}
        {[1, 2, 3].map((item) => (
          <View key={item} style={styles.reviewCard}>
            <View style={styles.reviewHeader}>
              <View style={styles.reviewerInfo}>
                <SkeletonLoader width={40} height={40} borderRadius={20} />
                <View style={styles.reviewerDetails}>
                  <SkeletonLoader width="70%" height={16} />
                  <View style={styles.reviewStars}>
                    {[1, 2, 3, 4, 5].map((star) => (
                      <SkeletonLoader key={star} width={16} height={16} borderRadius={8} style={{ marginRight: 2 }} />
                    ))}
                  </View>
                </View>
              </View>
              <SkeletonLoader width="20%" height={14} />
            </View>
            <View style={styles.reviewTextContainer}>
              <SkeletonLoader width="100%" height={16} />
              <SkeletonLoader width="80%" height={16} style={{ marginTop: 6 }} />
            </View>
          </View>
        ))}
      </View>
    );
  }

  return null;
};


