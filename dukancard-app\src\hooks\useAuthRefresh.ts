import { useAuth } from '@/src/contexts/AuthContext';
import React, { useState } from 'react';

/**
 * Hook for manually refreshing authentication state
 * Useful when you need to validate the current session or force a refresh
 */
export function useAuthRefresh() {
  const { forceRefreshAuth } = useAuth();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const refreshAuth = async (): Promise<{ success: boolean; error?: string }> => {
    if (isRefreshing) {
      return { success: false, error: 'Refresh already in progress' };
    }

    setIsRefreshing(true);
    
    try {
      await forceRefreshAuth();
      return { success: true };
    } catch (error) {
      console.error('Error refreshing auth:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to refresh authentication' 
      };
    } finally {
      setIsRefreshing(false);
    }
  };

  return {
    refreshAuth,
    isRefreshing,
  };
}

/**
 * Hook for periodic authentication validation
 * Automatically validates the session at specified intervals
 */
export function usePeriodicAuthValidation(intervalMs: number = 60000) {
  const { forceRefreshAuth } = useAuth();
  const [lastValidation, setLastValidation] = useState<Date | null>(null);
  const [validationError, setValidationError] = useState<string | null>(null);

  const validateAuth = React.useCallback(async (): Promise<void> => {
    try {
      await forceRefreshAuth();
      setLastValidation(new Date());
      setValidationError(null);
    } catch (error) {
      console.error('Periodic auth validation failed:', error);
      setValidationError(error instanceof Error ? error.message : 'Validation failed');
    }
  }, [forceRefreshAuth]);

  // Set up periodic validation
  React.useEffect(() => {
    const interval = setInterval(validateAuth, intervalMs);

    // Run initial validation
    validateAuth();

    return () => clearInterval(interval);
  }, [intervalMs, validateAuth]);

  return {
    lastValidation,
    validationError,
    validateAuth,
  };
}
