export interface CustomerPostsRow {
  id: string;
  customer_id: string;
  content: string;
  image_url: string | null;
  created_at: string;
  updated_at: string;
  city_slug: string | null;
  state_slug: string | null;
  locality_slug: string | null;
  pincode: string | null;
  mentioned_business_ids: string[] | null;
}

export interface CustomerPostsInsert {
  id?: string;
  customer_id: string;
  content: string;
  image_url?: string | null;
  created_at?: string;
  updated_at?: string;
  city_slug?: string | null;
  state_slug?: string | null;
  locality_slug?: string | null;
  pincode?: string | null;
  mentioned_business_ids?: string[] | null;
}

export interface CustomerPostsUpdate {
  id?: string;
  customer_id?: string;
  content?: string;
  image_url?: string | null;
  created_at?: string;
  updated_at?: string;
  city_slug?: string | null;
  state_slug?: string | null;
  locality_slug?: string | null;
  pincode?: string | null;
  mentioned_business_ids?: string[] | null;
}

export type CustomerPosts = CustomerPostsRow;
