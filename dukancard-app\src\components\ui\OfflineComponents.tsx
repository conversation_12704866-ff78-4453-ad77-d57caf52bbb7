import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { WifiOff } from 'lucide-react-native';

interface OfflineStateProps {
  message?: string;
  style?: any;
}

export const OfflineState: React.FC<OfflineStateProps> = ({
  message = 'You are currently offline. Please check your internet connection.',
  style,
}) => {
  return (
    <View style={[styles.container, style]}>
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <WifiOff color="#EF4444" size={48} />
        </View>
        <Text style={styles.title}>No Internet Connection</Text>
        <Text style={styles.message}>{message}</Text>
      </View>
    </View>
  );
};

/**
 * Offline banner component for showing at top of screen
 */
interface OfflineBannerProps {
  visible: boolean;
  style?: any;
}

export const OfflineBanner: React.FC<OfflineBannerProps> = ({
  visible,
  style,
}) => {
  if (!visible) return null;

  return (
    <View style={[styles.banner, style]}>
      <WifiOff color="#FFFFFF" size={16} />
      <Text style={styles.bannerText}>No internet connection</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 40,
    backgroundColor: '#F9FAFB',
  },
  content: {
    alignItems: 'center',
    maxWidth: 320,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#FEF2F2',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 12,
  },
  message: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  banner: {
    backgroundColor: '#EF4444',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    gap: 8,
  },
  bannerText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
});
