import { StyleSheet } from 'react-native';

export const createProductsStyles = () => {
  return StyleSheet.create({
    container: {
      flex: 1,
    },
    content: {
      padding: 20,
      paddingTop: 60,
    },
    header: {
      alignItems: 'center',
      marginBottom: 30,
    },
    subtitle: {
      marginTop: 8,
      textAlign: 'center',
      opacity: 0.7,
    },
    addButton: {
      marginBottom: 20,
    },
    productsContainer: {
      marginBottom: 30,
    },
    productCard: {
      padding: 16,
      marginBottom: 12,
      backgroundColor: 'rgba(10, 126, 164, 0.1)',
      borderRadius: 12,
    },
    productName: {
      fontSize: 18,
      marginBottom: 4,
    },
    productPrice: {
      fontSize: 20,
      fontWeight: 'bold',
      color: '#4CAF50',
      marginBottom: 8,
    },
    productDescription: {
      opacity: 0.7,
      lineHeight: 20,
    },
    emptyState: {
      padding: 40,
      alignItems: 'center',
      backgroundColor: 'rgba(10, 126, 164, 0.05)',
      borderRadius: 12,
      borderWidth: 2,
      borderColor: 'rgba(10, 126, 164, 0.2)',
      borderStyle: 'dashed',
    },
    emptyText: {
      textAlign: 'center',
      opacity: 0.6,
    },
  });
};
