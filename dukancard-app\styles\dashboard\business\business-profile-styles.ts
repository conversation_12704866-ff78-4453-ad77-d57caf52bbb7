import { StyleSheet } from "react-native";

export const createBusinessProfileStyles = (colorScheme: "light" | "dark") => {
  const isDark = colorScheme === "dark";

  return StyleSheet.create({
    container: {
      flex: 1,
    },
    errorContainer: {
      marginHorizontal: 16,
      marginVertical: 20,
    },
    // Profile content matching skeleton layout
    profileContent: {
      padding: 20,
      paddingBottom: 120, // Extra bottom padding to account for bottom navigation (80px) + safe area
    },
    // Profile header - matching skeleton profileHeader
    profileHeader: {
      alignItems: "center",
      marginBottom: 24,
    },
    largeAvatar: {
      width: 100,
      height: 100,
      borderRadius: 50,
      borderWidth: 2,
      borderColor: "#D4AF37", // Golden border to match brand
      justifyContent: "center",
      alignItems: "center",
      marginBottom: 16,
      overflow: "hidden",
      shadowColor: "#D4AF37",
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
      elevation: 4,
    },
    avatarImage: {
      width: 96, // Slightly smaller to account for border
      height: 96,
      borderRadius: 48,
    },
    profileName: {
      fontSize: 20,
      fontWeight: "600",
      marginBottom: 8,
    },
    profileEmail: {
      fontSize: 16,
    },
    // Profile stats - matching skeleton profileStats
    profileStats: {
      flexDirection: "row",
      justifyContent: "space-around",
      marginBottom: 32,
      paddingVertical: 16,
      borderRadius: 12,
    },
    profileStatItem: {
      alignItems: "center",
    },
    statValue: {
      fontSize: 24,
      fontWeight: "bold",
      marginBottom: 4,
    },
    statLabel: {
      fontSize: 14,
    },
    // Profile menu - matching skeleton profileMenu
    profileMenu: {
      gap: 16,
    },
    profileMenuItem: {
      flexDirection: "row",
      alignItems: "center",
      paddingVertical: 12,
    },
    menuItemText: {
      fontSize: 16,
      fontWeight: "500",
      flex: 1,
      marginLeft: 16,
    },
    // Legacy styles for backward compatibility
    content: {
      padding: 20,
      paddingTop: 60,
    },
    header: {
      alignItems: "center",
      marginBottom: 30,
    },
    avatar: {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: "#0a7ea4",
      alignItems: "center",
      justifyContent: "center",
      marginBottom: 16,
    },
    avatarText: {
      color: "#FFFFFF",
      fontWeight: "bold",
      fontSize: 24,
    },
    businessName: {
      marginBottom: 4,
    },
    businessType: {
      opacity: 0.7,
    },
    infoSection: {
      marginBottom: 30,
    },
    sectionTitle: {
      marginBottom: 16,
    },
    infoItem: {
      marginBottom: 16,
    },
    infoLabel: {
      fontSize: 14,
      opacity: 0.7,
      marginBottom: 4,
    },
    infoValue: {
      fontSize: 16,
    },
    actionsSection: {
      marginBottom: 30,
    },
    actionButton: {
      marginBottom: 12,
    },
    logoutSection: {
      marginTop: 20,
    },
    // Theme section styles
    themeSection: {
      marginTop: 24,
      padding: 16,
      borderRadius: 12,
    },
    themeSectionHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 12,
    },
    themeSectionTitle: {
      fontSize: 16,
      fontWeight: "600",
      marginLeft: 12,
    },
    // Logout button styles
    logoutButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 8,
      marginTop: 24,
      gap: 8,
      borderWidth: 1,
      borderColor: "#ef4444",
    },
    logoutButtonText: {
      fontSize: 16,
      fontWeight: "600",
    },
    // QR Code Section Styles
    qrSection: {
      marginVertical: 20,
      marginHorizontal: 16,
      padding: 20,
      borderRadius: 16,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 4,
    },
    qrHeader: {
      alignItems: "center",
      marginBottom: 20,
    },
    qrTitle: {
      fontSize: 20,
      fontWeight: "bold",
      marginBottom: 4,
    },
    qrSubtitle: {
      fontSize: 14,
      textAlign: "center",
    },
    qrContainer: {
      borderRadius: 12,
      padding: 16,
      marginBottom: 20,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    urlSection: {
      alignItems: "center",
    },
    urlLabel: {
      fontSize: 14,
      marginBottom: 8,
      fontWeight: "500",
    },
    urlContainer: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderRadius: 8,
      minWidth: 200,
      alignItems: "center",
    },
    urlText: {
      fontSize: 16,
      fontWeight: "600",
      fontFamily: "monospace",
    },
  });
};
