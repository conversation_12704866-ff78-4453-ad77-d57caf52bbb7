import { StyleSheet } from 'react-native';

export const createQRScannerModalStyles = () => {
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#000',
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      zIndex: 1,
    },
    closeButton: {
      padding: 8,
      borderRadius: 20,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
    },
    headerCenter: {
      flex: 1,
      alignItems: 'center',
    },
    headerTitle: {
      color: '#fff',
      fontSize: 18,
      fontWeight: '600',
    },
    offlineIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 4,
      paddingHorizontal: 8,
      paddingVertical: 2,
      backgroundColor: 'rgba(239, 68, 68, 0.2)',
      borderRadius: 12,
      gap: 4,
    },
    offlineText: {
      color: '#EF4444',
      fontSize: 12,
      fontWeight: '500',
    },
    headerActions: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
    },
    flashlightButton: {
      padding: 8,
      borderRadius: 20,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
    },
    scannerContainer: {
      flex: 1,
    },
    footer: {
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      paddingHorizontal: 20,
      paddingVertical: 16,
      zIndex: 1,
      alignItems: 'center',
    },
    galleryButton: {
      paddingHorizontal: 20,
      paddingVertical: 12,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      borderRadius: 8,
      marginBottom: 16,
    },
    galleryButtonText: {
      color: '#fff',
      fontSize: 16,
      fontWeight: '500',
      textAlign: 'center',
    },
    footerText: {
      color: '#ccc',
      fontSize: 14,
      textAlign: 'center',
      lineHeight: 20,
    },
    processingOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000,
    },
    processingContainer: {
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      borderRadius: 16,
      padding: 24,
      alignItems: 'center',
      minWidth: 200,
    },
    processingText: {
      color: '#FFFFFF',
      fontSize: 16,
      fontWeight: '500',
      marginTop: 16,
      textAlign: 'center',
    },
    progressContainer: {
      marginTop: 16,
      width: '100%',
      alignItems: 'center',
    },
    progressBar: {
      width: '100%',
      height: 4,
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      borderRadius: 2,
      overflow: 'hidden',
    },
    progressFill: {
      height: '100%',
      backgroundColor: '#D4AF37',
      borderRadius: 2,
    },
    progressText: {
      color: '#D4AF37',
      fontSize: 12,
      fontWeight: '600',
      marginTop: 8,
    },
  });
};
