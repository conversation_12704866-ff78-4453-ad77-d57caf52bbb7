import { Dimensions } from 'react-native';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

// Base width for scaling, typically a common phone width like iPhone 6/7/8
const BASE_WIDTH = 375;

// Scaling factor
const scale = SCREEN_WIDTH / BASE_WIDTH;

// Function to scale font sizes
export const responsiveFontSize = (size: number) => {
  const newSize = size * scale;
  return Math.round(newSize);
};

/**
 * Dukancard Theme Colors Configuration
 * Defines the complete color palette for the mobile app
 * Based on the brand gold color #C29D5B and supporting colors
 */

// Brand Colors - Primary Dukancard Gold Theme
export const brandColors = {
  // Primary gold color and variants
  gold: '#C29D5B',
  goldLight: '#D4B373',
  goldDark: '#B08A4A',
  goldForeground: '#2D2D2D', // Dark text on gold background
  
  // Gold with opacity variants for overlays and subtle effects
  goldOpacity: {
    10: 'rgba(194, 157, 91, 0.1)',
    20: 'rgba(194, 157, 91, 0.2)',
    30: 'rgba(194, 157, 91, 0.3)',
    50: 'rgba(194, 157, 91, 0.5)',
    80: 'rgba(194, 157, 91, 0.8)',
  },
} as const;

// Light Theme Colors
export const lightTheme = {
  // Base colors
  background: '#FFFFFF', // Pure white for consistency
  foreground: '#2D2D2D', // oklch(0.18 0.01 90) - Dark gray
  
  // Card and surface colors
  card: '#FFFFFF',
  cardForeground: '#2D2D2D',
  
  // Primary colors (using brand gold)
  primary: brandColors.gold,
  primaryForeground: brandColors.goldForeground,
  primaryLight: brandColors.goldLight,
  primaryDark: brandColors.goldDark,
  
  // Secondary colors
  secondary: '#F5F5F5', // oklch(0.96 0.005 90)
  secondaryForeground: '#404040', // oklch(0.25 0.01 90)
  
  // Accent colors
  accent: brandColors.goldLight,
  accentForeground: brandColors.goldForeground,
  
  // Muted colors for subtle elements
  muted: '#F5F5F5',
  mutedForeground: '#808080', // oklch(0.5 0.005 90)
  
  // Border and input colors
  border: '#E5E5E5', // oklch(0.9 0.005 90) - Softer border
  input: '#E5E5E5',
  ring: brandColors.goldOpacity[50], // Gold ring with opacity
  
  // Status colors
  success: '#22C55E',
  successForeground: '#FFFFFF',
  warning: '#F59E0B',
  warningForeground: '#FFFFFF',
  error: '#EF4444', // oklch(0.577 0.245 27.325)
  errorForeground: '#FFFFFF',
  destructive: '#EF4444',
  destructiveForeground: '#FFFFFF',
  
  // Text colors
  textPrimary: '#2D2D2D',
  textSecondary: '#808080',
  textMuted: '#A0A0A0',
  textOnPrimary: '#FFFFFF',
  
  // Icon colors
  icon: '#808080',
  iconMuted: '#A0A0A0',
  iconOnPrimary: '#FFFFFF',
  
  // Tab navigation colors
  tabIconDefault: '#808080',
  tabIconSelected: brandColors.gold,
  tabBackground: '#FFFFFF',
  tabBorder: '#E5E5E5',
  shadow: 'rgba(0, 0, 0, 0.1)', // Neutral shadow for light theme
} as const;

// Dark Theme Colors
export const darkTheme = {
  // Base colors
  background: '#000000', // Pure black for consistency
  foreground: '#F5F5F5', // oklch(0.95 0.005 90) - Light gray
  
  // Card and surface colors
  card: '#1A1A1A',
  cardForeground: '#F5F5F5',
  
  // Primary colors (using brand gold)
  primary: brandColors.gold,
  primaryForeground: brandColors.goldForeground,
  primaryLight: brandColors.goldLight,
  primaryDark: brandColors.goldDark,
  
  // Secondary colors
  secondary: '#2A2A2A', // oklch(0.25 0.01 90)
  secondaryForeground: '#F5F5F5',
  
  // Accent colors
  accent: brandColors.goldLight,
  accentForeground: brandColors.goldForeground,
  
  // Muted colors for subtle elements
  muted: '#2A2A2A',
  mutedForeground: '#A0A0A0', // oklch(0.65 0.005 90)
  
  // Border and input colors
  border: 'rgba(255, 255, 255, 0.15)', // More subtle border
  input: 'rgba(255, 255, 255, 0.18)',
  ring: brandColors.goldOpacity[50], // Gold ring with opacity
  
  // Status colors
  success: '#22C55E',
  successForeground: '#FFFFFF',
  warning: '#F59E0B',
  warningForeground: '#FFFFFF',
  error: '#EF4444',
  errorForeground: '#FFFFFF',
  destructive: '#EF4444',
  destructiveForeground: '#FFFFFF',
  
  // Text colors
  textPrimary: '#F5F5F5',
  textSecondary: '#A0A0A0',
  textMuted: '#808080',
  textOnPrimary: '#FFFFFF',
  
  // Icon colors
  icon: '#A0A0A0',
  iconMuted: '#808080',
  iconOnPrimary: '#FFFFFF',
  
  // Tab navigation colors
  tabIconDefault: '#A0A0A0',
  tabIconSelected: brandColors.gold,
  tabBackground: '#1A1A1A',
  tabBorder: 'rgba(255, 255, 255, 0.15)',
  shadow: 'rgba(255, 255, 255, 0.1)', // Neutral shadow for dark theme
} as const;

// Theme type definition
export type Theme = typeof lightTheme & { shadow: string };

// Export theme object with both light and dark variants
export const themes = {
  light: lightTheme,
  dark: darkTheme,
} as const;

// Spacing scale for consistent spacing throughout the app
export const spacing = {
  xs: responsiveFontSize(4),
  sm: responsiveFontSize(8),
  md: responsiveFontSize(16),
  lg: responsiveFontSize(24),
  xl: responsiveFontSize(32),
  xxl: responsiveFontSize(48),
  xxxl: responsiveFontSize(64),
} as const;

// Typography scale
export const typography = {
  fontSize: {
    xs: responsiveFontSize(12),
    sm: responsiveFontSize(14),
    base: responsiveFontSize(16),
    lg: responsiveFontSize(18),
    xl: responsiveFontSize(20),
    xxl: responsiveFontSize(24),
    xxxl: responsiveFontSize(32),
    xxxxl: responsiveFontSize(40),
  },
  fontWeight: {
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },
  lineHeight: {
    tight: 1.2,
    normal: 1.5,
    relaxed: 1.75,
  },
} as const;

// Border radius scale
export const borderRadius = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  xxl: 24,
  full: 9999,
} as const;

// Shadow presets
export const shadows = {
  sm: {
    shadowColor: brandColors.gold,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  md: {
    shadowColor: brandColors.gold,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  lg: {
    shadowColor: brandColors.gold,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  xl: {
    shadowColor: brandColors.gold,
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.25,
    shadowRadius: 24,
    elevation: 12,
  },
} as const;

// Animation durations
export const animations = {
  fast: 150,
  normal: 300,
  slow: 500,
} as const;

// Breakpoints for responsive design (though less relevant for mobile)
export const breakpoints = {
  sm: 480,
  md: 768,
  lg: 1024,
  xl: 1280,
} as const;

// Export everything as a complete theme configuration
export const themeConfig = {
  colors: themes,
  brandColors,
  spacing,
  typography,
  borderRadius,
  shadows,
  animations,
  breakpoints,
} as const;

export default themeConfig;
