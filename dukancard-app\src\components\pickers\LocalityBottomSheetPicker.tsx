import { useTheme } from "@/src/hooks/useTheme";
import BottomSheet, {
  BottomSheetView,
  BottomSheetFlatList,
  BottomSheetTextInput,
} from "@gorhom/bottom-sheet";
import { MapPin, Search, X } from "lucide-react-native";
import React, {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useState,
} from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";

interface LocalityBottomSheetPickerProps {
  localities: string[];
  selectedLocality?: string;
  onLocalitySelect: (locality: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

export interface LocalityBottomSheetPickerRef {
  present: () => void;
  dismiss: () => void;
}

const LocalityBottomSheetPicker = forwardRef<
  LocalityBottomSheetPickerRef,
  LocalityBottomSheetPickerProps
>(
  (
    {
      localities,
      selectedLocality,
      onLocalitySelect,
      placeholder = "Select locality",
      disabled = false,
    },
    ref
  ) => {
    const { colors, isDark } = useTheme();
    const [searchQuery, setSearchQuery] = useState("");
    const bottomSheetRef = React.useRef<BottomSheet>(null);

    // Snap points for the bottom sheet - using only the working snap point
    const snapPoints = useMemo(() => ["85%"], []);

    // Filter localities based on search query
    const filteredLocalities = useMemo(() => {
      if (!searchQuery.trim()) return localities;

      return localities.filter((locality) =>
        locality.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }, [localities, searchQuery]);

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      present: () => {
        if (!disabled && localities.length > 0) {
          bottomSheetRef.current?.expand();
        }
      },
      dismiss: () => {
        bottomSheetRef.current?.close();
      },
    }));

    const handleLocalitySelect = useCallback(
      (locality: string) => {
        onLocalitySelect(locality);
        bottomSheetRef.current?.close();
        setSearchQuery(""); // Clear search when closing
      },
      [onLocalitySelect]
    );

    const handleSheetChanges = useCallback((index: number) => {
      // Clear search when sheet is closed
      if (index === -1) {
        setSearchQuery("");
      }
    }, []);

    const renderLocalityItem = useCallback(
      ({ item }: { item: string }) => {
        const isSelected = selectedLocality === item;

        return (
          <TouchableOpacity
            style={[
              styles.localityItem,
              {
                backgroundColor: isSelected
                  ? colors.primary + "20"
                  : "transparent",
                borderBottomColor: colors.border,
              },
            ]}
            onPress={() => handleLocalitySelect(item)}
          >
            <View style={styles.localityContent}>
              <Text
                style={[
                  styles.localityName,
                  {
                    color: isSelected ? colors.primary : colors.textPrimary,
                    fontWeight: isSelected ? "600" : "400",
                  },
                ]}
              >
                {item}
              </Text>
            </View>
            {isSelected && (
              <View
                style={[
                  styles.selectedIndicator,
                  { backgroundColor: colors.primary },
                ]}
              />
            )}
          </TouchableOpacity>
        );
      },
      [selectedLocality, colors, handleLocalitySelect]
    );

    const renderEmptyState = useCallback(
      () => (
        <View style={styles.emptyState}>
          <MapPin size={48} color={colors.textSecondary} />
          <Text style={[styles.emptyStateTitle, { color: colors.textPrimary }]}>
            {localities.length === 0
              ? "No localities available"
              : "No localities found"}
          </Text>
          <Text
            style={[
              styles.emptyStateDescription,
              { color: colors.textSecondary },
            ]}
          >
            {localities.length === 0
              ? "Please enter a valid pincode first"
              : `No localities match "${searchQuery}"`}
          </Text>
        </View>
      ),
      [colors, searchQuery, localities.length]
    );

    return (
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        enablePanDownToClose
        enableDynamicSizing={false}
        enableContentPanningGesture={false}
        enableHandlePanningGesture={true}
        keyboardBehavior="fillParent"
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
        backgroundStyle={{
          backgroundColor: isDark ? "#000000" : "#ffffff",
        }}
        handleIndicatorStyle={{
          backgroundColor: colors.textSecondary,
        }}
      >
        <BottomSheetView style={styles.container}>
          {/* Fixed Header with Search - Direct in BottomSheetView */}
          <View
            style={[
              styles.header,
              {
                borderBottomColor: colors.border,
                backgroundColor: isDark ? "#000000" : "#ffffff",
              },
            ]}
          >
            <View style={styles.headerTop}>
              <Text style={[styles.headerTitle, { color: colors.textPrimary }]}>
                Select Locality
              </Text>
              <TouchableOpacity
                onPress={() => bottomSheetRef.current?.close()}
                style={styles.closeButton}
              >
                <X size={24} color={colors.textPrimary} />
              </TouchableOpacity>
            </View>

            {/* Search Input - Direct BottomSheetTextInput */}
            <View
              style={[
                styles.searchContainer,
                {
                  backgroundColor: isDark ? "#ffffff2e" : "#f5f5f5",
                  borderColor: isDark ? "#ffffff26" : "#e5e5e5",
                },
              ]}
            >
              <Search
                size={20}
                color={colors.textSecondary}
                style={styles.searchIcon}
              />
              <BottomSheetTextInput
                style={[styles.searchInput, { color: colors.textPrimary }]}
                placeholder="Search localities..."
                placeholderTextColor={colors.textSecondary}
                value={searchQuery}
                onChangeText={setSearchQuery}
                autoCapitalize="none"
                autoCorrect={false}
                returnKeyType="search"
                submitBehavior="blurAndSubmit"
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity
                  onPress={() => setSearchQuery("")}
                  style={styles.clearButton}
                >
                  <X size={16} color={colors.textSecondary} />
                </TouchableOpacity>
              )}
            </View>
          </View>

          {/* List Content - Simple FlatList without header */}
          <BottomSheetFlatList
            data={filteredLocalities}
            renderItem={renderLocalityItem}
            keyExtractor={(item) => item}
            showsVerticalScrollIndicator={true}
            ListEmptyComponent={renderEmptyState}
            contentContainerStyle={styles.listContent}
            keyboardShouldPersistTaps="handled"
            keyboardDismissMode="none"
            nestedScrollEnabled={true}
          />
        </BottomSheetView>
      </BottomSheet>
    );
  }
);

LocalityBottomSheetPicker.displayName = "LocalityBottomSheetPicker";

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  flatList: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
  },
  headerTop: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  closeButton: {
    padding: 4,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 12,
    borderWidth: 1,
    paddingHorizontal: 12,
    height: 48,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    height: "100%",
  },
  clearButton: {
    padding: 4,
    marginLeft: 8,
  },
  listContent: {
    paddingBottom: 80, // Increased padding to ensure bottom items are accessible within 85% height
  },
  localityItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  localityContent: {
    flex: 1,
  },
  localityName: {
    fontSize: 16,
  },
  selectedIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginLeft: 12,
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateDescription: {
    fontSize: 14,
    textAlign: "center",
    lineHeight: 20,
  },
});

export default LocalityBottomSheetPicker;
