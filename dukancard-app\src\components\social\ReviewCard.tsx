/**
 * ReviewCard Component for React Native
 * Displays individual review with edit and delete functionality
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ReviewBusinessProfile } from '@/backend/supabase/services/posts/socialService';

interface ReviewData {
  id: string;
  rating: number;
  review_text: string | null;
  created_at: string;
  updated_at: string;
  business_profile_id: string;
  user_id: string;
  business_profiles: ReviewBusinessProfile | null;
}

interface ReviewCardProps {
  review: ReviewData;
  onDelete: (reviewId: string) => Promise<void>;
  onUpdate: (reviewId: string, rating: number, reviewText: string) => Promise<void>;
}

export const ReviewCard: React.FC<ReviewCardProps> = ({
  review,
  onDelete,
  onUpdate,
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [editedRating, setEditedRating] = useState(review.rating);
  const [editedReviewText, setEditedReviewText] = useState(review.review_text || '');
  
  const business = review.business_profiles;

  if (!business) {
    return null;
  }

  const handleDelete = () => {
    Alert.alert(
      'Delete Review',
      'Are you sure you want to delete this review? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            setIsDeleting(true);
            try {
              await onDelete(review.id);
            } catch (error) {
              console.error('Error deleting review:', error);
            } finally {
              setIsDeleting(false);
            }
          },
        },
      ]
    );
  };

  const handleEdit = () => {
    setIsEditing(true);
    setEditedRating(review.rating);
    setEditedReviewText(review.review_text || '');
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditedRating(review.rating);
    setEditedReviewText(review.review_text || '');
  };

  const handleSaveEdit = async () => {
    if (editedRating < 1 || editedRating > 5) {
      Alert.alert('Invalid Rating', 'Please select a rating between 1 and 5 stars.');
      return;
    }

    setIsUpdating(true);
    try {
      await onUpdate(review.id, editedRating, editedReviewText);
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating review:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleVisitBusiness = () => {
    // TODO: Navigate to business profile
  };

  const getBusinessInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const renderStars = (rating: number, onPress?: (rating: number) => void) => {
    return (
      <View style={styles.starsContainer}>
        {[1, 2, 3, 4, 5].map((star) => (
          <TouchableOpacity
            key={star}
            onPress={() => onPress && onPress(star)}
            disabled={!onPress}
          >
            <Ionicons
              name={star <= rating ? 'star' : 'star-outline'}
              size={20}
              color={star <= rating ? '#FFD700' : '#ccc'}
              style={styles.star}
            />
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  return (
    <View style={styles.card}>
      {/* Business Header */}
      <TouchableOpacity
        style={styles.businessHeader}
        onPress={handleVisitBusiness}
        activeOpacity={0.7}
      >
        <View style={styles.logoContainer}>
          {business.logo_url ? (
            <Image source={{ uri: business.logo_url }} style={styles.logo} />
          ) : (
            <View style={styles.logoPlaceholder}>
              <Text style={styles.logoText}>
                {getBusinessInitials(business.business_name || 'B')}
              </Text>
            </View>
          )}
        </View>

        <View style={styles.businessInfo}>
          <Text style={styles.businessName} numberOfLines={1}>
            {business.business_name}
          </Text>
          <Text style={styles.reviewDate}>
            Reviewed on {formatDate(review.created_at)}
            {review.updated_at !== review.created_at && ' (edited)'}
          </Text>
        </View>

        <TouchableOpacity style={styles.visitButton} onPress={handleVisitBusiness}>
          <Ionicons name="arrow-forward" size={16} color="#D4AF37" />
        </TouchableOpacity>
      </TouchableOpacity>

      {/* Review Content */}
      <View style={styles.reviewContent}>
        {isEditing ? (
          <>
            {/* Edit Rating */}
            <View style={styles.editSection}>
              <Text style={styles.editLabel}>Rating:</Text>
              {renderStars(editedRating, setEditedRating)}
            </View>

            {/* Edit Review Text */}
            <View style={styles.editSection}>
              <Text style={styles.editLabel}>Review:</Text>
              <TextInput
                style={styles.editTextInput}
                value={editedReviewText}
                onChangeText={setEditedReviewText}
                placeholder="Write your review..."
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            </View>

            {/* Edit Actions */}
            <View style={styles.editActions}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={handleCancelEdit}
                disabled={isUpdating}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.saveButton, isUpdating && styles.saveButtonDisabled]}
                onPress={handleSaveEdit}
                disabled={isUpdating}
              >
                {isUpdating ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Text style={styles.saveButtonText}>Save</Text>
                )}
              </TouchableOpacity>
            </View>
          </>
        ) : (
          <>
            {/* Display Rating */}
            {renderStars(review.rating)}

            {/* Display Review Text */}
            {review.review_text && (
              <Text style={styles.reviewText}>{review.review_text}</Text>
            )}

            {/* Action Buttons */}
            <View style={styles.actions}>
              <TouchableOpacity
                style={styles.editActionButton}
                onPress={handleEdit}
                disabled={isDeleting}
              >
                <Ionicons name="create-outline" size={16} color="#D4AF37" />
                <Text style={styles.editActionText}>Edit</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.deleteActionButton, isDeleting && styles.deleteActionButtonDisabled]}
                onPress={handleDelete}
                disabled={isDeleting}
              >
                {isDeleting ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <>
                    <Ionicons name="trash-outline" size={16} color="#fff" />
                    <Text style={styles.deleteActionText}>Delete</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    overflow: 'hidden',
  },
  businessHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  logoContainer: {
    marginRight: 12,
  },
  logo: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  logoPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#D4AF37',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  businessInfo: {
    flex: 1,
  },
  businessName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 2,
  },
  reviewDate: {
    fontSize: 12,
    color: '#666',
  },
  visitButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(212, 175, 55, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  reviewContent: {
    padding: 16,
  },
  starsContainer: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  star: {
    marginRight: 4,
  },
  reviewText: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
    marginBottom: 16,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  editActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(212, 175, 55, 0.1)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    flex: 1,
    marginRight: 8,
    justifyContent: 'center',
  },
  editActionText: {
    color: '#D4AF37',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  deleteActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ff4444',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    flex: 1,
    marginLeft: 8,
    justifyContent: 'center',
  },
  deleteActionButtonDisabled: {
    backgroundColor: '#ccc',
  },
  deleteActionText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  editSection: {
    marginBottom: 16,
  },
  editLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  editTextInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    color: '#333',
    minHeight: 80,
  },
  editActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    flex: 1,
    marginRight: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 14,
    fontWeight: '500',
  },
  saveButton: {
    backgroundColor: '#D4AF37',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    flex: 1,
    marginLeft: 8,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: '#ccc',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
});
