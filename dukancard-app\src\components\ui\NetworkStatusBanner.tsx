import React, { useEffect, useState, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  TouchableOpacity,
} from 'react-native';
import {
  Wifi,
  WifiOff,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
} from 'lucide-react-native';
import { useTheme } from '@/src/hooks/useTheme';
import { useNetworkStatus } from '@/src/utils/networkStatus';

interface NetworkStatusBannerProps {
  onRetry?: () => void;
  showWhenOnline?: boolean;
  autoHideDelay?: number;
  style?: any;
}

export const NetworkStatusBanner: React.FC<NetworkStatusBannerProps> = ({
  onRetry,
  showWhenOnline = false,
  autoHideDelay = 3000,
  style,
}) => {
  const theme = useTheme();
  const networkStatus = useNetworkStatus();
  const [isVisible, setIsVisible] = useState(false);
  const [showOnlineMessage, setShowOnlineMessage] = useState(false);
  const [wasOffline, setWasOffline] = useState(false);
  const slideAnim = useRef(new Animated.Value(-100)).current;

  const slideDown = useCallback(() => {
    Animated.spring(slideAnim, {
      toValue: 0,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
  }, [slideAnim]);

  const slideUp = useCallback(() => {
    Animated.spring(slideAnim, {
      toValue: -100,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start(() => {
      setIsVisible(false);
      setShowOnlineMessage(false);
    });
  }, [slideAnim]);

  useEffect(() => {
    const { isConnected, isInternetReachable } = networkStatus;
    
    // Track if we were offline to show "back online" message
    if (!isConnected) {
      setWasOffline(true);
    }

    // Show banner when offline
    if (!isConnected) {
      setIsVisible(true);
      setShowOnlineMessage(false);
      slideDown();
    }
    // Show "back online" message if we were offline and now online
    else if (isConnected && wasOffline && showWhenOnline) {
      setIsVisible(true);
      setShowOnlineMessage(true);
      slideDown();
      
      // Auto-hide after delay
      setTimeout(() => {
        slideUp();
        setWasOffline(false);
      }, autoHideDelay);
    }
    // Hide banner when online and not showing online message
    else if (isConnected && !showOnlineMessage) {
      slideUp();
    }
  }, [networkStatus, showOnlineMessage, slideDown, slideUp, wasOffline, showWhenOnline, autoHideDelay]);

  const getBannerConfig = () => {
    if (showOnlineMessage) {
      return {
        backgroundColor: theme.colors.success + '20',
        borderColor: theme.colors.success,
        textColor: theme.colors.success,
        icon: CheckCircle,
        title: 'Back Online',
        message: 'Your internet connection has been restored.',
      };
    } else if (!networkStatus.isConnected) {
      return {
        backgroundColor: theme.colors.error + '20',
        borderColor: theme.colors.error,
        textColor: theme.colors.error,
        icon: WifiOff,
        title: 'No Internet Connection',
        message: 'Please check your connection and try again.',
      };
    } else if (networkStatus.isInternetReachable === false) {
      return {
        backgroundColor: theme.colors.warning + '20',
        borderColor: theme.colors.warning,
        textColor: theme.colors.warning,
        icon: AlertTriangle,
        title: 'Limited Connection',
        message: 'Connected to WiFi but no internet access.',
      };
    }
    
    return null;
  };

  const config = getBannerConfig();
  
  if (!isVisible || !config) {
    return null;
  }

  const { backgroundColor, borderColor, textColor, icon: IconComponent, title, message } = config;

  const styles = StyleSheet.create({
    container: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      zIndex: 1000,
      backgroundColor,
      borderBottomWidth: 1,
      borderBottomColor: borderColor,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 5,
    },
    content: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.sm,
    },
    iconContainer: {
      padding: theme.spacing.xs,
    },
    textContainer: {
      flex: 1,
    },
    title: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: theme.typography.fontWeight.semibold,
      color: textColor,
      marginBottom: theme.spacing.xs,
    },
    message: {
      fontSize: theme.typography.fontSize.xs,
      color: theme.colors.textSecondary,
      lineHeight: theme.typography.lineHeight.normal * theme.typography.fontSize.xs,
    },
    retryButton: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.xs,
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      backgroundColor: textColor,
      borderRadius: theme.borderRadius.sm,
    },
    retryButtonText: {
      fontSize: theme.typography.fontSize.xs,
      color: '#FFFFFF',
      fontWeight: theme.typography.fontWeight.medium,
    },
  });

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ translateY: slideAnim }],
        },
        style,
      ]}
    >
      <View style={styles.content}>
        {/* Status Icon */}
        <View style={styles.iconContainer}>
          <IconComponent size={20} color={textColor} />
        </View>

        {/* Text Content */}
        <View style={styles.textContainer}>
          <Text style={styles.title}>{title}</Text>
          <Text style={styles.message}>{message}</Text>
        </View>

        {/* Retry Button (only for offline state) */}
        {!showOnlineMessage && onRetry && (
          <TouchableOpacity
            style={styles.retryButton}
            onPress={onRetry}
            activeOpacity={0.8}
          >
            <RefreshCw size={12} color="#FFFFFF" />
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        )}
      </View>
    </Animated.View>
  );
};

export default NetworkStatusBanner;
