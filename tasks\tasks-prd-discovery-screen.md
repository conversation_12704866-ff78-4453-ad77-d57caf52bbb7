Wait a # Task List: Discovery Screen Implementation

## Relevant Files

- `src/contexts/DiscoveryContext.tsx` - Main context for discovery state management, search functionality, and data fetching
- `src/contexts/DiscoveryContext.test.tsx` - Unit tests for DiscoveryContext
- `src/services/discoveryService.ts` - Service layer for discovery-related API calls and data processing
- `src/services/discoveryService.test.ts` - Unit tests for discoveryService
- `src/services/locationStorageService.ts` - Service for persisting and retrieving location preferences using AsyncStorage
- `src/services/locationStorageService.test.ts` - Unit tests for locationStorageService
- `src/components/discovery/DiscoveryScreen.tsx` - Main discovery screen component
- `src/components/discovery/DiscoveryScreen.test.tsx` - Unit tests for DiscoveryScreen
- `src/components/discovery/LocationSelector.tsx` - Component for location selection (pincode/city/locality)
- `src/components/discovery/LocationSelector.test.tsx` - Unit tests for LocationSelector
- `src/components/discovery/SearchSection.tsx` - Component for business/product search inputs
- `src/components/discovery/SearchSection.test.tsx` - Unit tests for SearchSection
- `src/components/discovery/ViewToggle.tsx` - Component for switching between business cards and products views
- `src/components/discovery/ViewToggle.test.tsx` - Unit tests for ViewToggle
- `src/components/discovery/BusinessCard.tsx` - New business card component for discovery results
- `src/components/discovery/BusinessCard.test.tsx` - Unit tests for BusinessCard
- `src/components/discovery/ResultsList.tsx` - Component for displaying paginated results with infinite scroll
- `src/components/discovery/ResultsList.test.tsx` - Unit tests for ResultsList
- `src/components/discovery/CategorySelector.tsx` - Component integrating with existing category picker
- `src/components/discovery/CategorySelector.test.tsx` - Unit tests for CategorySelector
- `src/components/shared/screens/DiscoverScreenNew.tsx` - Update existing discovery screen to use new implementation
- `src/types/discovery.ts` - TypeScript types and interfaces for discovery functionality
- `src/utils/distanceCalculation.ts` - Utility functions for calculating distances between coordinates
- `src/utils/distanceCalculation.test.ts` - Unit tests for distance calculation utilities

### Notes

- Unit tests should be placed alongside the code files they are testing
- Use `npx jest [optional/path/to/test/file]` to run tests
- Integration tests should verify the complete discovery flow from location selection to result display

## Tasks

- [ ] 1.0 Set up Discovery Context and State Management

  - [x] 1.1 Create TypeScript interfaces and types for discovery state in `src/types/discovery.ts` (Reference: `dukancard/app/(main)/discover/context/types.ts` for ViewType, ProductFilterOption, ProductSortOption, DiscoverSearchResult, CombinedSearchFormData)
  - [x] 1.2 Define search parameters, filter options, and result data structures (Reference: `dukancard/app/(main)/discover/constants/urlParamConstants.ts` for parameter naming conventions)
  - [x] 1.3 Create DiscoveryContext with React.createContext and initial state (Reference: `dukancard/app/(main)/discover/context/DiscoverContext.tsx` structure and state management patterns)
  - [x] 1.4 Implement context provider with state management for search terms, filters, results, and loading states (Reference: `dukancard/app/(main)/discover/context/DiscoverContext.tsx` provider implementation)
  - [x] 1.5 Add context methods for updating search parameters, view type, and pagination (Reference: `dukancard/app/(main)/discover/context/commonContextFunctions.ts` for method patterns)
  - [x] 1.6 Implement useDiscovery custom hook for consuming context (Reference: `dukancard/app/(main)/discover/context/DiscoverContext.tsx` useDiscoverContext hook)
  - [x] 1.7 Write unit tests for context state management and hook functionality

- [x] 1.0 Set up Discovery Context and State Management

- [ ] 2.0 Implement Location Management System

  - [x] 2.1 Create locationStorageService with AsyncStorage integration for persisting location preferences (Reference: `dukancard-app/src/contexts/ThemeContext.tsx` for AsyncStorage patterns)
  - [x] 2.2 Implement functions to save/retrieve pincode, city, locality, and GPS coordinates (Reference: `dukancard/app/(main)/discover/components/ImprovedSearchSection.tsx` for location data structure)
  - [x] 2.3 Add automatic location detection on first app launch using existing location services (Reference: `dukancard-app/backend/supabase/services/location/locationService.ts` for GPS functions)
  - [x] 2.4 Create LocationSelector component with manual pincode/city/locality input fields (Reference: `dukancard/app/(main)/discover/components/ImprovedSearchSection.tsx` for input patterns and validation)
  - [x] 2.5 Implement "Use Current Location" functionality with GPS permission handling (Reference: `dukancard-app/src/components/ui/LocationPicker.tsx` for permission handling patterns)
  - [x] 2.6 Add location validation and error handling for invalid pincodes/cities (Reference: `dukancard/app/(main)/discover/components/ImprovedSearchSection.tsx` for validation logic)
  - [x] 2.7 Integrate location state with DiscoveryContext (Reference: `dukancard/app/(main)/discover/context/DiscoverContext.tsx` for location state management)
  - [x] 2.8 Write unit tests for location storage service and LocationSelector component

- [x] 2.0 Implement Location Management System

- [ ] 3.0 Create Discovery Service Layer

  - [x] 3.1 Create discoveryService with functions for fetching businesses and products from Supabase (Reference: `dukancard/app/(main)/discover/actions/combinedActions.ts` for searchDiscoverCombined function structure)
  - [x] 3.2 Implement location-based business search with distance calculation support (Reference: `dukancard/app/(main)/discover/actions/businessActions.ts` for business search patterns)
  - [x] 3.3 Add product/service search functionality with category and location filtering (Reference: `dukancard/app/(main)/discover/actions/productActions.ts` for product search implementation)
  - [x] 3.4 Implement pagination logic for infinite scroll with database-level limits (Reference: `dukancard/app/(main)/discover/constants/paginationConstants.ts` for pagination constants)
  - [x] 3.5 Add search debouncing and request cancellation for performance optimization (Reference: `dukancard-app/src/components/social/SearchComponent.tsx` for debouncing patterns)
  - [x] 3.6 Create distance calculation utility functions using GPS coordinates (Reference: `dukancard-app/backend/supabase/services/location/locationService.ts` findClosestPincodeFromGPS function)
  - [x] 3.7 Handle businesses without GPS coordinates gracefully (Reference: `dukancard/app/(main)/discover/actions/businessActions.ts` for fallback handling)
  - [x] 3.8 Write comprehensive unit tests for all service functions and edge cases

- [ ] 4.0 Build Core Discovery UI Components

  - [x] 4.1 Create SearchSection component with business name and product name input fields (Reference: `dukancard/app/(main)/discover/components/ImprovedSearchSection.tsx` for search input structure and form handling)
  - [x] 4.2 Implement search input debouncing and clear functionality (Reference: `dukancard-app/src/components/social/SearchComponent.tsx` for debouncing and clear button patterns)
  - [x] 4.3 Create CategorySelector component integrating with existing CategoryBottomSheetPicker (Reference: `dukancard-app/src/components/pickers/CategoryBottomSheetPicker.tsx` and `dukancard/app/(main)/discover/components/CategoryCarousel.tsx`)
  - [x] 4.4 Implement ViewToggle component for switching between business cards and products views (Reference: `dukancard/app/(main)/discover/components/ViewToggle.tsx` for toggle design and functionality)
  - [x] 4.5 Add loading states and skeleton loaders for all search components (Reference: `dukancard-app/src/components/ui/ProductSkeleton.tsx` for skeleton patterns)
  - [x] 4.6 Implement error handling and user feedback for search failures (Reference: `dukancard/app/(main)/discover/components/ErrorSection.tsx` for error display patterns)
  - [x] 4.7 Ensure components follow existing React Native app design patterns (Reference: existing components in `dukancard-app/src/components/ui/` for styling consistency)
  - [x] 4.8 Write unit tests for all UI components and user interactions

- [ ] 5.0 Implement Results Display and Navigation

  - [x] 5.1 Create new BusinessCard component optimized for discovery results display (Reference: `dukancard/app/(main)/discover/components/ModernBusinessResults.tsx` for business card layout and data display)
  - [x] 5.2 Add distance display in kilometers with fallback for businesses without GPS (Reference: `dukancard/app/(main)/discover/components/LocationIndicator.tsx` for location display patterns)
  - [x] 5.3 Implement ResultsList component with FlatList and infinite scroll pagination (Reference: `dukancard-app/src/components/feed/UnifiedFeedList.tsx` for FlatList pagination patterns)
  - [x] 5.4 Integrate existing ProductCard components for product results display (Reference: `dukancard-app/src/components/shared/ui/ProductCard.tsx` and `dukancard/app/(main)/discover/components/ProductResults.tsx`)
  - [x] 5.5 Add navigation handlers for business profile and product page routing (Reference: `dukancard-app/app/business/[businessSlug].tsx` and `dukancard-app/app/product/[productId].tsx` for navigation patterns)
  - [x] 5.6 Implement empty states for no results found scenarios (Reference: `dukancard-app/src/components/ui/ComingSoon.tsx` for empty state design patterns)
  - [x] 5.7 Add pull-to-refresh functionality for results lists (Reference: `dukancard-app/src/components/feed/UnifiedFeedList.tsx` RefreshControl implementation)
  - [x] 5.8 Write unit tests for results display components and navigation logic

- [x] 6.0 Integrate and Test Complete Discovery Flow
  - [x] 6.1 Update existing DiscoverScreenNew component to use new discovery implementation (Reference: `dukancard/app/(main)/discover/ModernDiscoverClient.tsx` for main component structure and layout)
  - [x] 6.2 Integrate DiscoveryContext provider at appropriate level in component hierarchy (Reference: `dukancard/app/(main)/discover/ModernDiscoverClient.tsx` DiscoverProvider usage)
  - [x] 6.3 Connect all components together in main DiscoveryScreen component (Reference: `dukancard/app/(main)/discover/ModernDiscoverClient.tsx` for component composition and layout)
  - [x] 6.4 Test complete user flow from location selection to result navigation (Reference: `dukancard/app/(main)/discover/components/ModernResultsSection.tsx` for complete flow integration)
  - [x] 6.5 Implement proper error boundaries and fallback UI components (Reference: `dukancard-app/src/components/ErrorBoundary.tsx` for error boundary patterns)
  - [ ] 6.6 Add analytics tracking for user interactions and search patterns (Reference: existing analytics patterns in the app)
  - [ ] 6.7 Perform performance testing and optimization for smooth scrolling (Reference: `dukancard-app/src/components/feed/UnifiedFeedList.tsx` for performance optimization patterns)
  - [ ] 6.8 Write integration tests covering complete discovery workflows
  - [ ] 6.9 Test on different device sizes and ensure responsive design (Reference: existing responsive patterns in `dukancard-app/src/components/`)
  - [ ] 6.10 Validate accessibility features and screen reader compatibility
