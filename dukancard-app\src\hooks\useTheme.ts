import { useColorScheme } from '@/src/hooks/useColorScheme';
import { themes, themeConfig, type Theme } from '@/lib/theme/colors';

/**
 * Hook to get the current theme based on the color scheme
 * Provides access to colors, spacing, typography, and other theme values
 */
export function useTheme() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  // Get the current theme colors
  const colors = themes[colorScheme ?? 'light'];
  
  // Return the complete theme object with utilities
  return {
    // Current theme colors
    colors,
    
    // Theme utilities
    isDark,
    isLight: !isDark,
    colorScheme: colorScheme ?? 'light',
    
    // Design tokens
    spacing: themeConfig.spacing,
    typography: themeConfig.typography,
    borderRadius: themeConfig.borderRadius,
    shadows: themeConfig.shadows,
    animations: themeConfig.animations,
    breakpoints: themeConfig.breakpoints,
    brandColors: themeConfig.brandColors,
    
    // Helper functions
    getColor: (colorKey: keyof Theme) => colors[colorKey],
    getSpacing: (key: keyof typeof themeConfig.spacing) => themeConfig.spacing[key],
    getShadow: (key: keyof typeof themeConfig.shadows) => themeConfig.shadows[key],
    getBorderRadius: (key: keyof typeof themeConfig.borderRadius) => themeConfig.borderRadius[key],
    
    // Typography helpers
    getFontSize: (key: keyof typeof themeConfig.typography.fontSize) => themeConfig.typography.fontSize[key],
    getFontWeight: (key: keyof typeof themeConfig.typography.fontWeight) => themeConfig.typography.fontWeight[key],
    getLineHeight: (key: keyof typeof themeConfig.typography.lineHeight) => themeConfig.typography.lineHeight[key],
  };
}

/**
 * Hook to get theme-aware colors with fallback support
 * Similar to the existing useThemeColor but using the new theme system
 */
export function useThemeColors() {
  const { colors } = useTheme();
  return colors;
}

/**
 * Hook to create theme-aware styles
 * Provides a function to create styles that automatically adapt to the current theme
 */
export function useThemedStyles<T extends Record<string, any>>(
  styleCreator: (theme: ReturnType<typeof useTheme>) => T
): T {
  const theme = useTheme();
  return styleCreator(theme);
}

export default useTheme;
