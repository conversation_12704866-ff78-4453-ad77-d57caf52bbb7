import { useColorScheme } from '@/src/hooks/useColorScheme';
import { ValidationFunction, ValidationResult } from '@/backend/supabase/utils/validation';
import { Ionicons } from '@expo/vector-icons';
import React, { useCallback, useEffect, useState } from 'react';
import {
    KeyboardTypeOptions,
    NativeSyntheticEvent,
    Text,
    TextInput,
    TextInputFocusEventData,
    TextInputProps,
    TouchableOpacity,
    View,
    ViewStyle
} from 'react-native';

export interface FormFieldProps extends Omit<TextInputProps, 'onChangeText'> {
  label?: string;
  error?: string;
  containerStyle?: ViewStyle;
  type?: 'text' | 'email' | 'password' | 'phone' | 'number' | 'url';
  validationFunction?: ValidationFunction;
  onChangeText?: (text: string) => void;
  onValidationChange?: (result: ValidationResult) => void;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  showValidationIcon?: boolean;
  required?: boolean;
  helperText?: string;
  maxLength?: number;
  debounceMs?: number;
}

export function FormField({
  label,
  error,
  containerStyle,
  type = 'text',
  validationFunction,
  onChangeText,
  onValidationChange,
  validateOnChange = true,
  validateOnBlur = true,
  showValidationIcon = true,
  required = false,
  helperText,
  maxLength,
  debounceMs = 300,
  ...textInputProps
}: FormFieldProps) {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [internalError, setInternalError] = useState<string | undefined>();
  const [isValid, setIsValid] = useState<boolean | null>(null);
  const [isFocused, setIsFocused] = useState(false);
  const [debounceTimeout, setDebounceTimeout] = useState<ReturnType<typeof setTimeout> | null>(null);
  const colorScheme = useColorScheme();

  // Use external error if provided, otherwise use internal validation error
  const displayError = error || internalError;
  const hasError = Boolean(displayError);

  const getKeyboardType = (): KeyboardTypeOptions => {
    switch (type) {
      case 'email':
        return 'email-address';
      case 'phone':
        return 'phone-pad';
      case 'number':
        return 'numeric';
      case 'url':
        return 'url';
      default:
        return 'default';
    }
  };

  const getSecureTextEntry = () => {
    if (type === 'password') {
      return !isPasswordVisible;
    }
    return false;
  };

  const getAutoCapitalize = () => {
    switch (type) {
      case 'email':
      case 'url':
        return 'none';
      case 'password':
        return 'none';
      default:
        return 'sentences';
    }
  };

  const getAutoCorrect = () => {
    return type !== 'email' && type !== 'password' && type !== 'url';
  };

  const validateValue = useCallback((value: string) => {
    if (!validationFunction) return;

    const result = validationFunction(value);
    setIsValid(result.isValid);
    setInternalError(result.error);
    
    if (onValidationChange) {
      onValidationChange(result);
    }
  }, [validationFunction, onValidationChange]);

  const handleChangeText = (text: string) => {
    // Clear previous debounce timeout
    if (debounceTimeout) {
      clearTimeout(debounceTimeout);
    }

    // Call the parent's onChangeText immediately
    if (onChangeText) {
      onChangeText(text);
    }

    // Validate on change if enabled
    if (validateOnChange && validationFunction) {
      if (debounceMs > 0) {
        // Debounced validation
        const timeout = setTimeout(() => {
          validateValue(text);
        }, debounceMs);
        setDebounceTimeout(timeout);
      } else {
        // Immediate validation
        validateValue(text);
      }
    }
  };

  const handleBlur = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
    setIsFocused(false);

    // Validate on blur if enabled
    if (validateOnBlur && validationFunction && textInputProps.value) {
      validateValue(textInputProps.value);
    }

    if (textInputProps.onBlur) {
      textInputProps.onBlur(e);
    }
  };

  const handleFocus = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
    setIsFocused(true);

    if (textInputProps.onFocus) {
      textInputProps.onFocus(e);
    }
  };

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeout) {
        clearTimeout(debounceTimeout);
      }
    };
  }, [debounceTimeout]);

  const iconColor = colorScheme === 'dark' ? '#A6A6A6' : '#808080';
  const validColor = '#10b981'; // green-500
  const invalidColor = '#ef4444'; // red-500

  const getBorderColor = () => {
    if (hasError) return invalidColor;
    if (isFocused) return '#C29D5B'; // Gold theme color
    if (isValid === true && showValidationIcon) return validColor;
    return colorScheme === 'dark' ? '#ffffff26' : '#e5e5e5';
  };

  const getValidationIcon = () => {
    if (!showValidationIcon || isValid === null) return null;
    
    return (
      <View
        style={{
          position: 'absolute',
          right: type === 'password' ? 50 : 16,
          top: 16,
          padding: 4
        }}
      >
        <Ionicons
          name={isValid ? 'checkmark-circle' : 'close-circle'}
          size={20}
          color={isValid ? validColor : invalidColor}
        />
      </View>
    );
  };

  return (
    <View style={[{ marginBottom: 16 }, containerStyle]}>
      {label && (
        <Text
          style={{
            color: colorScheme === 'dark' ? '#f2f2f2' : '#2d2d2d',
            fontSize: 14,
            fontWeight: '600',
            marginBottom: 8,
            letterSpacing: 0.5
          }}
        >
          {label}
          {required && (
            <Text style={{ color: invalidColor }}> *</Text>
          )}
        </Text>
      )}
      
      <View style={{ position: 'relative' }}>
        <TextInput
          style={{
            borderRadius: 12,
            paddingHorizontal: 16,
            paddingVertical: 12,
            paddingRight: type === 'password' ? 60 : showValidationIcon && isValid !== null ? 50 : 16,
            fontSize: 14,
            fontWeight: '500',
            letterSpacing: 0.5,
            minHeight: 48,
            backgroundColor: colorScheme === 'dark' ? '#ffffff2e' : '#f5f5f5',
            color: colorScheme === 'dark' ? '#f2f2f2' : '#2d2d2d',
            borderWidth: hasError || isFocused || isValid === true ? 2 : 1,
            borderColor: getBorderColor()
          }}
          placeholderTextColor={iconColor}
          keyboardType={getKeyboardType()}
          secureTextEntry={getSecureTextEntry()}
          autoCapitalize={getAutoCapitalize()}
          autoCorrect={getAutoCorrect()}
          maxLength={maxLength}
          onChangeText={handleChangeText}
          onBlur={handleBlur}
          onFocus={handleFocus}
          {...textInputProps}
        />
        
        {/* Validation icon */}
        {getValidationIcon()}
        
        {/* Password visibility toggle */}
        {type === 'password' && (
          <TouchableOpacity
            style={{
              position: 'absolute',
              right: 16,
              top: 16,
              padding: 4
            }}
            onPress={togglePasswordVisibility}
          >
            <Ionicons
              name={isPasswordVisible ? 'eye-off' : 'eye'}
              size={20}
              color={iconColor}
            />
          </TouchableOpacity>
        )}
      </View>
      
      {/* Error message */}
      {displayError && (
        <Text
          style={{
            color: invalidColor,
            fontSize: 14,
            fontWeight: '500',
            marginTop: 6
          }}
        >
          {displayError}
        </Text>
      )}
      
      {/* Helper text */}
      {helperText && !displayError && (
        <Text
          style={{
            color: iconColor,
            fontSize: 12,
            marginTop: 4
          }}
        >
          {helperText}
        </Text>
      )}
      
      {/* Character count */}
      {maxLength && textInputProps.value && (
        <Text
          style={{
            color: iconColor,
            fontSize: 12,
            textAlign: 'right',
            marginTop: 4
          }}
        >
          {textInputProps.value.length}/{maxLength}
        </Text>
      )}
    </View>
  );
}
