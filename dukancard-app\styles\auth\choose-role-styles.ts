import { useTheme } from '@/src/hooks/useTheme';
import { StyleSheet } from 'react-native';
import { EdgeInsets } from 'react-native-safe-area-context';

export const createChooseRoleStyles = (theme: ReturnType<typeof useTheme>, insets?: EdgeInsets) => {

  return StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    container: {
      flex: 1,
      paddingHorizontal: theme.spacing.lg,
    },
    // Top section with title
    topSection: {
      flex: 0.3,
      justifyContent: 'center',
      alignItems: 'center',
      paddingTop: theme.spacing.lg,
    },
    welcomeContainer: {
      alignItems: 'center',
      paddingHorizontal: theme.spacing.md,
    },
    welcomeTitle: {
      fontSize: theme.typography.fontSize.xxl,
      fontWeight: theme.typography.fontWeight.bold,
      textAlign: 'center',
      marginBottom: theme.spacing.md,
      color: theme.colors.textPrimary,
      letterSpacing: -0.5,
    },
    welcomeSubtitle: {
      fontSize: theme.typography.fontSize.base,
      textAlign: 'center',
      color: theme.colors.textSecondary,
      lineHeight: theme.typography.lineHeight.relaxed * theme.typography.fontSize.base,
      maxWidth: 320,
      fontWeight: theme.typography.fontWeight.normal,
    },
    messageContainer: {
      marginTop: theme.spacing.lg,
      padding: theme.spacing.lg,
      backgroundColor: theme.colors.muted,
      borderRadius: theme.borderRadius.lg,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    messageText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: theme.typography.lineHeight.normal * theme.typography.fontSize.sm,
      fontWeight: theme.typography.fontWeight.medium,
    },
    // Middle section with role selection
    middleSection: {
      flex: 0.45,
      justifyContent: 'center',
      paddingVertical: theme.spacing.xl,
    },
    roleContainer: {
      gap: theme.spacing.lg,
    },
    loadingContainer: {
      alignItems: 'center',
      gap: theme.spacing.lg,
      paddingVertical: theme.spacing.xxxl,
    },
    loadingText: {
      fontSize: theme.typography.fontSize.lg,
      color: theme.colors.textSecondary,
      fontWeight: theme.typography.fontWeight.semibold,
    },
    // Bottom section with warning
    bottomSection: {
      flex: 0.15,
      justifyContent: 'flex-end',
      paddingBottom: Math.max(theme.spacing.xl, (insets?.bottom || 0) + theme.spacing.xl),
    },
    warningContainer: {
      padding: theme.spacing.lg,
      backgroundColor: theme.colors.error + '10',
      borderRadius: theme.borderRadius.lg,
      borderWidth: 1,
      borderColor: theme.colors.error + '30',
    },
    warningText: {
      fontSize: theme.typography.fontSize.xs,
      color: theme.colors.error,
      textAlign: 'center',
      lineHeight: theme.typography.lineHeight.normal * theme.typography.fontSize.xs,
      fontWeight: theme.typography.fontWeight.medium,
    },
  });
};

// Dummy default export to satisfy Expo Router
export default function ChooseRoleStylesPlaceholder() {
  return null;
}
