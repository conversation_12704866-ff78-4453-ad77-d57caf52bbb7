import {
  Plan as ConfigPlan,
  PlanType,
  getProductLimit,
  PLANS,
} from "@/lib/config/plans";

// Define the possible billing cycle types (for backward compatibility)
type BillingCycle = "monthly" | "yearly";

// Legacy PricingPlan interface for backward compatibility
export interface PricingPlan {
  id: string;
  name: string;
  price: string;
  yearlyPrice?: string;
  period: string;
  description: string;
  features: string[];
  button: string;
  available: boolean;
  featured: boolean;
  recommended?: boolean;
  savings?: string;
  mostPopular?: boolean;
}

// Convert ConfigPlan to PricingPlan for backward compatibility
function convertToPricingPlan(plan: ConfigPlan, billingCycle: BillingCycle): PricingPlan {
  const isYearly = billingCycle === "yearly";
  const price = isYearly ? plan.pricing.yearly : plan.pricing.monthly;
  const isEnterprise = plan.id === "enterprise";
  const isFree = plan.id === "free";
  
  // Calculate savings for yearly plans
  const monthlyCost = plan.pricing.monthly * 12;
  const yearlyCost = plan.pricing.yearly;
  const savings = monthlyCost > 0 && yearlyCost > 0 && monthlyCost > yearlyCost 
    ? `Save ₹${monthlyCost - yearlyCost}` 
    : undefined;

  // Format price display
  let priceDisplay: string;
  if (isEnterprise) {
    priceDisplay = "Contact Sales";
  } else if (isFree) {
    priceDisplay = "Free";
  } else {
    priceDisplay = `₹${price}`;
  }

  // Convert features to string array
  const features = plan.features.map(feature => {
    if (feature.limit && feature.limit !== "unlimited") {
      return `${feature.name} (${feature.limit})`;
    }
    return feature.name;
  });

  // Determine availability
  const available = plan.available !== false;

  return {
    id: plan.id,
    name: plan.name,
    price: priceDisplay,
    yearlyPrice: isYearly ? priceDisplay : `₹${plan.pricing.yearly}`,
    period: isYearly ? "per year" : "per month",
    description: plan.description,
    features,
    button: isEnterprise
      ? "Contact Sales"
      : isFree
      ? "Get Started"
      : "Subscribe Now",
    available,
    featured: plan.id === "free" || plan.id === "basic" || plan.id === "growth" || plan.id === "pro",
    recommended: plan.recommended || false,
    mostPopular: plan.recommended || false, // Use the recommended flag from the plan config
    savings: isYearly ? savings : undefined,
  };
}

// pricingPlans function for backward compatibility
export const pricingPlans = (billingCycle: BillingCycle): PricingPlan[] => {
  return PLANS.map((plan) => convertToPricingPlan(plan, billingCycle));
};

// Export a static list of plans (e.g., monthly) for onboarding or simpler displays
export const onboardingPlans: PricingPlan[] = pricingPlans("monthly");

// Note: All plan features are now defined in lib/config/plans.ts
// This file only provides backward compatibility for existing code

// Helper function to get product/service limits based on plan ID (for backward compatibility)
export const getPlanLimit = (planId: string | null | undefined): number => {
  return getProductLimit(planId as PlanType);
};

// Helper function to get plan by ID
export const getPlanById = (planId: string): PricingPlan | undefined => {
  return onboardingPlans.find(plan => plan.id === planId);
};
