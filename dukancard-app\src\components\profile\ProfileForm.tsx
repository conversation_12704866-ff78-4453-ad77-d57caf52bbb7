import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Input } from '@/src/components/ui/Input';
import { Button } from '@/src/components/ui/Button';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { User, Save } from 'lucide-react-native';
import { updateCustomerProfile } from '@/backend/supabase/services/common/profileService';
import { useAuth } from '@/src/contexts/AuthContext';

interface ProfileFormProps {
  initialName?: string | null;
  onProfileUpdated?: () => void;
}

interface FormData {
  name: string;
}

interface FormErrors {
  name?: string;
}

export default function ProfileForm({ initialName, onProfileUpdated }: ProfileFormProps) {
  const { user } = useAuth();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [formData, setFormData] = useState<FormData>({
    name: initialName || '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    } else if (formData.name.trim().length > 100) {
      newErrors.name = 'Name cannot exceed 100 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    if (!user?.id) {
      Alert.alert('Error', 'User not authenticated');
      return;
    }

    setIsLoading(true);
    try {
      const result = await updateCustomerProfile({
        name: formData.name.trim(),
      });

      if (result.success) {
        Alert.alert('Success', 'Profile updated successfully!');
        onProfileUpdated?.();
      } else {
        Alert.alert('Error', result.error || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Profile update error:', error);
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleNameChange = (text: string) => {
    setFormData(prev => ({ ...prev, name: text }));
    // Clear error when user starts typing
    if (errors.name) {
      setErrors(prev => ({ ...prev, name: undefined }));
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: isDark ? '#1a1a1a' : '#fff' }]}>
      <View style={styles.header}>
        <View style={[styles.iconContainer, { backgroundColor: isDark ? '#2a2a2a' : '#f8f9fa' }]}>
          <User size={24} color={isDark ? '#D4AF37' : '#D4AF37'} />
        </View>
        <Text style={[styles.title, { color: isDark ? '#fff' : '#000' }]}>Edit Profile</Text>
        <Text style={[styles.subtitle, { color: isDark ? '#999' : '#666' }]}>
          Update your personal information
        </Text>
      </View>

      <View style={styles.form}>
        <View style={styles.inputGroup}>
          <Text style={[styles.label, { color: isDark ? '#fff' : '#000' }]}>Full Name</Text>
          <Input
            value={formData.name}
            onChangeText={handleNameChange}
            placeholder="Enter your full name"
            error={errors.name}
            leftIcon={<User size={20} color={isDark ? '#999' : '#666'} />}
            editable={!isLoading}
          />
        </View>

        <View style={styles.buttonContainer}>
          <Button
            title={isLoading ? 'Updating...' : 'Save Changes'}
            onPress={handleSubmit}
            disabled={isLoading || !formData.name.trim()}
            variant="primary"
            icon={
              isLoading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Save size={20} color="#fff" />
              )
            }
          />
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
  form: {
    gap: 20,
  },
  inputGroup: {
    gap: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
  },
  buttonContainer: {
    marginTop: 8,
  },
});
