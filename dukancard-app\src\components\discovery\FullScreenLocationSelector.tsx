/**
 * Full-Screen Location Selector Modal Component
 * Detailed location selection interface with back button navigation
 */

import React, { useState, useCallback, useEffect, useRef } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  SafeAreaView,
  ScrollView,
  <PERSON>ert,
  Modal,
} from "react-native";
import { X, Navigation, ChevronDown } from "lucide-react-native";
import { useColorScheme } from "@/src/hooks/useColorScheme";
import { LocationData } from "@/src/types/discovery";
import {
  LocationService,
  requestLocationPermission,
  reverseGeocodeCoordinates,
} from "@/backend/supabase/services/location/locationService";
import { validatePincodeForCity } from "@/backend/supabase/services/common/profileService";
import * as Location from "expo-location";
import { createLocationSelectorScreenStyles } from "./styles/LocationSelectorScreenStyles";
import { DukancardLogo } from "@/src/components/ui/DukancardLogo";
import { supabase } from "@/src/config/supabase";
import LocalityBottomSheetPicker, {
  LocalityBottomSheetPickerRef,
} from "@/src/components/pickers/LocalityBottomSheetPicker";

export interface FullScreenLocationSelectorProps {
  visible: boolean;
  onClose: () => void;
  onLocationSelect: (location: LocationData) => void;
  initialLocation?: LocationData;
}

export function FullScreenLocationSelector({
  visible,
  onClose,
  onLocationSelect,
  initialLocation,
}: FullScreenLocationSelectorProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === "dark";
  const styles = createLocationSelectorScreenStyles();

  // State
  const [city, setCity] = useState(initialLocation?.city || "");
  const [pincode, setPincode] = useState(initialLocation?.pincode || "");
  const [locality, setLocality] = useState(initialLocation?.locality || "");
  const [localities, setLocalities] = useState<string[]>([]);
  const [isLoadingLocalities, setIsLoadingLocalities] = useState(false);
  const [isDetectingLocation, setIsDetectingLocation] = useState(false);
  const [showLocalityPicker, setShowLocalityPicker] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [gpsDetectedLocality, setGpsDetectedLocality] = useState<string | null>(
    null
  );

  // City search state
  const [citySuggestions, setCitySuggestions] = useState<string[]>([]);
  const [showCitySuggestions, setShowCitySuggestions] = useState(false);
  const [isLoadingCities, setIsLoadingCities] = useState(false);

  // Refs
  const localityPickerRef = useRef<LocalityBottomSheetPickerRef>(null);

  // Effect to fetch localities when modal opens with existing pincode
  useEffect(() => {
    if (visible && pincode && pincode.length === 6 && localities.length === 0) {
      // Store the current locality to restore after fetch
      const currentLocality = locality;

      // Fetch localities for the existing pincode
      const fetchExistingPincodeLocalities = async () => {
        try {
          setIsLoadingLocalities(true);
          const result = await LocationService.getPincodeDetails(pincode);
          if (result && result.length > 0) {
            const localityNames = result.map((item: any) =>
              item.OfficeName.replace(" B.O", "")
                .replace(" S.O", "")
                .replace(" H.O", "")
            );
            setLocalities(localityNames);

            // Restore the original locality if it exists in the fetched list
            if (currentLocality && localityNames.includes(currentLocality)) {
              setLocality(currentLocality);
            } else if (currentLocality === "") {
              // Keep "All Localities" selection
              setLocality("");
            }
          }
        } catch (error) {
          console.error("Error fetching existing pincode localities:", error);
        } finally {
          setIsLoadingLocalities(false);
        }
      };

      fetchExistingPincodeLocalities();
    }
  }, [visible, pincode, localities.length, locality]);

  // Theme colors - Using gold brand colors
  const backgroundColor = isDark ? "#000000" : "#ffffff";
  const cardBackgroundColor = isDark ? "#1a1a1a" : "#f8f9fa";
  const borderColor = isDark ? "#333" : "#e1e5e9";
  const textColor = isDark ? "#ffffff" : "#1a1a1a";
  const secondaryTextColor = isDark ? "#a0a0a0" : "#6b7280";
  const primaryColor = "#C29D5B"; // Gold brand color
  const errorColor = "#ff3b30";

  // Clear functions with cascading logic
  const handleClearCity = () => {
    setCity("");
    setPincode("");
    setLocality("");
    setLocalities([]);
    setErrors({});
    setShowCitySuggestions(false);
    setCitySuggestions([]);
    setShowLocalityPicker(false);
    handleLocationUpdate({});
  };

  const handleClearPincode = () => {
    setPincode("");
    setLocality("");
    setLocalities([]);
    setErrors({ ...errors, pincode: "" });
    setShowLocalityPicker(false);
    handleLocationUpdate({ city });
  };

  // Validate pincode against selected city
  const validatePincodeAgainstCity = async (
    pincodeValue: string,
    cityValue: string
  ) => {
    if (!pincodeValue || !cityValue || pincodeValue.length !== 6) return;

    try {
      const result = await validatePincodeForCity(pincodeValue, cityValue);
      if (!result.success) {
        setErrors({
          ...errors,
          pincode: result.error || "Pincode does not belong to selected city",
        });
        // Clear the pincode after showing error
        setTimeout(() => {
          setPincode("");
          setLocality("");
          setLocalities([]);
          setErrors({ ...errors, pincode: "" });
        }, 3000);
      }
    } catch (error) {
      console.error("Error validating pincode against city:", error);
      setErrors({ ...errors, pincode: "Failed to validate pincode" });
    }
  };

  // City search function
  const searchCities = useCallback(async (query: string) => {
    if (query.length < 2) {
      setCitySuggestions([]);
      setShowCitySuggestions(false);
      setIsLoadingCities(false);
      return;
    }

    setIsLoadingCities(true);
    try {
      // First try the RPC function like Next.js does
      const { data: rpcData, error: rpcError } = await supabase.rpc(
        "get_distinct_cities",
        {
          search_query: `%${query}%`,
          result_limit: 5,
        }
      );

      if (!rpcError && rpcData && rpcData.length > 0) {
        // RPC worked, format the cities
        const cities = rpcData.map((item: any) =>
          item.city
            .toLowerCase()
            .replace(/\b\w/g, (char: string) => char.toUpperCase())
        );
        setCitySuggestions(cities);
        setShowCitySuggestions(true);
        return;
      }

      // Fallback to regular query if RPC fails (same as Next.js fallback)
      const { data: cityData, error } = await supabase
        .from("pincodes")
        .select("DivisionName")
        .ilike("DivisionName", `%${query}%`)
        .order("DivisionName")
        .limit(100); // Get more data to ensure we have 5 unique cities

      if (error) {
        console.error("City search error:", error);
        setCitySuggestions([]);
        setShowCitySuggestions(false);
        return;
      }

      if (cityData && cityData.length > 0) {
        // Get unique cities and format them
        const cities = [
          ...new Set(
            cityData.map((item) =>
              item.DivisionName.toLowerCase().replace(/\b\w/g, (char: string) =>
                char.toUpperCase()
              )
            )
          ),
        ] as string[];

        const topCities = cities.slice(0, 5);
        setCitySuggestions(topCities);
        setShowCitySuggestions(true);
      } else {
        setCitySuggestions([]);
        setShowCitySuggestions(false);
      }
    } catch (error) {
      console.error("Error searching cities:", error);
      setCitySuggestions([]);
      setShowCitySuggestions(false);
    } finally {
      setIsLoadingCities(false);
    }
  }, []);

  // Handle location update
  const handleLocationUpdate = useCallback(
    (locationData: Partial<LocationData>) => {
      // Update individual state
      if (locationData.city !== undefined) setCity(locationData.city);
      if (locationData.pincode !== undefined) setPincode(locationData.pincode);
      if (locationData.locality !== undefined)
        setLocality(locationData.locality);
    },
    []
  );

  // Auto-select GPS-detected locality when localities are fetched
  useEffect(() => {
    if (gpsDetectedLocality && localities.length > 0) {
      // Add a small delay to ensure this runs after pincode change completes
      setTimeout(() => {
        // Find the matching locality in the fetched list using exact matching
        const matchingLocality = localities.find(
          (locality) =>
            locality.toLowerCase().trim() ===
            gpsDetectedLocality.toLowerCase().trim()
        );

        if (matchingLocality) {
          setLocality(matchingLocality);
          setGpsDetectedLocality(null); // Clear the stored value

          // Update location with the auto-selected locality
          handleLocationUpdate({
            city,
            pincode,
            locality: matchingLocality,
          });
        } else {
          console.log(
            "No exact locality match found. GPS:",
            gpsDetectedLocality,
            "Available:",
            localities
          );
          // Clear GPS-detected locality even if no match found
          setGpsDetectedLocality(null);
        }
      }, 50); // Small delay to ensure pincode change completes first
    }
  }, [localities, gpsDetectedLocality, city, pincode, handleLocationUpdate]);

  // Handle city input with search
  const handleCityChange = (value: string) => {
    setCity(value);
    setErrors({ ...errors, city: "" });

    // Trigger city search when user types (but don't auto-select)
    if (value.length >= 2) {
      searchCities(value);
    } else {
      setShowCitySuggestions(false);
      setCitySuggestions([]);
    }

    if (value.length > 0) {
      handleLocationUpdate({ city: value });

      // Validate existing pincode against the new city
      if (pincode && pincode.length === 6) {
        validatePincodeAgainstCity(pincode, value);
      }
    }
  };

  // Handle city selection from suggestions
  const handleCitySelect = (selectedCity: string) => {
    setCity(selectedCity);
    setShowCitySuggestions(false);
    setCitySuggestions([]);
    handleLocationUpdate({ city: selectedCity });

    // Validate existing pincode against the selected city
    if (pincode && pincode.length === 6) {
      validatePincodeAgainstCity(pincode, selectedCity);
    }
  };

  // Handle manual city search (Enter key or search button)
  const handleCitySearch = () => {
    if (city.length >= 2) {
      searchCities(city);
    }
  };

  // Handle pincode input with validation and locality detection
  const handlePincodeChange = async (value: string) => {
    // Only allow digits and limit to 6 characters
    const cleanValue = value.replace(/\D/g, "").slice(0, 6);
    setPincode(cleanValue);
    setErrors({ ...errors, pincode: "" });
    setLocalities([]);
    setLocality("");
    setShowLocalityPicker(false);

    // Validate pincode when 6 digits are entered
    if (cleanValue.length === 6) {
      if (!/^\d{6}$/.test(cleanValue)) {
        setErrors({ pincode: "Pincode must be 6 digits" });
        // Clear the pincode after showing error
        setTimeout(() => {
          setPincode("");
          setErrors({ ...errors, pincode: "" });
        }, 2000);
        return;
      }

      setIsLoadingLocalities(true);
      try {
        const result = await LocationService.getPincodeDetails(cleanValue);
        if (result && result.length > 0) {
          const localityNames = result.map((item: any) =>
            item.OfficeName.replace(" B.O", "")
              .replace(" S.O", "")
              .replace(" H.O", "")
          );
          setLocalities(localityNames);

          // Set localities and city from pincode data
          const cityFromPincode = result[0].DivisionName;

          // Default to "All Localities" for manual pincode entry
          // GPS auto-selection will be handled by the useEffect
          const selectedLocality = gpsDetectedLocality ? "" : ""; // Always default to "All Localities" initially

          setLocality(selectedLocality);
          setCity(cityFromPincode);
          setShowLocalityPicker(localityNames.length > 1);

          handleLocationUpdate({
            city: cityFromPincode,
            pincode: cleanValue,
            locality: selectedLocality,
          });
        } else {
          setErrors({ pincode: "Invalid pincode - not found in database" });
          // Clear the pincode after showing error
          setTimeout(() => {
            setPincode("");
            setErrors({ ...errors, pincode: "" });
          }, 2000);
        }
      } catch (error) {
        console.error("Error fetching localities:", error);
        setErrors({ pincode: "Failed to validate pincode" });
        // Clear the pincode after showing error
        setTimeout(() => {
          setPincode("");
          setErrors({ ...errors, pincode: "" });
        }, 2000);
      } finally {
        setIsLoadingLocalities(false);
      }
    } else if (cleanValue.length > 0) {
      handleLocationUpdate({ pincode: cleanValue });
    }
  };

  // Handle locality selection
  const handleLocalitySelect = (selectedLocality: string) => {
    // Handle "All Localities" option
    const finalLocality =
      selectedLocality === "All Localities" ? "" : selectedLocality;

    setLocality(finalLocality);
    setShowLocalityPicker(false);
    handleLocationUpdate({
      city,
      pincode,
      locality: finalLocality,
    });
  };

  // Handle current location detection
  const handleCurrentLocation = async () => {
    setIsDetectingLocation(true);
    setErrors({});

    try {
      const permission = await requestLocationPermission();
      if (!permission.granted) {
        Alert.alert(
          "Location Permission Required",
          "Please grant location permission to detect your current location.",
          [{ text: "OK" }]
        );
        return;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      const address = await reverseGeocodeCoordinates(
        location.coords.latitude,
        location.coords.longitude
      );

      if (address.success) {
        const {
          city: detectedCity,
          pincode: detectedPincode,
          locality: detectedLocality,
          state,
        } = address;

        setCity(detectedCity || "");
        setPincode(detectedPincode || "");

        // Store GPS-detected locality for auto-selection after localities are fetched
        setGpsDetectedLocality(detectedLocality || "");

        // Don't set locality immediately - let the useEffect handle auto-selection
        setLocality("");

        handleLocationUpdate({
          city: detectedCity,
          pincode: detectedPincode,
          locality: "", // Will be set after auto-selection
          state,
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        });

        // If we have a pincode, fetch localities to enable auto-selection
        if (detectedPincode) {
          handlePincodeChange(detectedPincode);
        }
      } else {
        setErrors({ location: "Failed to detect address from location" });
      }
    } catch (error) {
      console.error("Location detection error:", error);
      setErrors({ location: "Failed to detect current location" });
    } finally {
      setIsDetectingLocation(false);
    }
  };

  // Handle save and close modal
  const handleSave = () => {
    if (!city && !pincode) {
      Alert.alert(
        "Location Required",
        "Please enter at least a city or pincode."
      );
      return;
    }

    const locationData: LocationData = {
      city: city || undefined,
      pincode: pincode || undefined,
      locality: locality || undefined,
    };

    onLocationSelect(locationData);
    onClose();
  };

  // Handle back/close
  const handleClose = () => {
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={handleClose}
    >
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: borderColor }]}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={handleClose}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <X size={24} color={textColor} />
          </TouchableOpacity>
          <View style={styles.headerCenter}>
            <DukancardLogo size="medium" showText={true} showTagline={false} />
          </View>
          <TouchableOpacity
            style={[styles.saveButton, { backgroundColor: primaryColor }]}
            onPress={handleSave}
          >
            <Text style={styles.saveButtonText}>Save</Text>
          </TouchableOpacity>
        </View>

        {/* Content */}
        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.formContainer}>
            {/* Current Location Button */}
            <TouchableOpacity
              style={[styles.currentLocationButton, { borderColor }]}
              onPress={handleCurrentLocation}
              disabled={isDetectingLocation}
            >
              {isDetectingLocation ? (
                <ActivityIndicator size="small" color={primaryColor} />
              ) : (
                <Navigation size={20} color={primaryColor} />
              )}
              <Text
                style={[styles.currentLocationText, { color: primaryColor }]}
              >
                {isDetectingLocation ? "Detecting..." : "Use Current Location"}
              </Text>
            </TouchableOpacity>

            {/* City Input with Search */}
            <View style={styles.inputContainer}>
              <Text style={[styles.label, { color: textColor }]}>City *</Text>
              <View style={styles.cityInputContainer}>
                <TextInput
                  style={[
                    styles.input,
                    styles.cityInput,
                    { borderColor, color: textColor, backgroundColor },
                    errors.city && { borderColor: errorColor },
                  ]}
                  value={city}
                  onChangeText={handleCityChange}
                  onSubmitEditing={handleCitySearch}
                  placeholder="Enter city name"
                  placeholderTextColor={secondaryTextColor}
                  autoCapitalize="words"
                  returnKeyType="search"
                />
                {city.length > 0 && !isLoadingCities && (
                  <TouchableOpacity
                    onPress={handleClearCity}
                    style={styles.clearButton}
                  >
                    <X size={16} color={secondaryTextColor} />
                  </TouchableOpacity>
                )}
                {isLoadingCities && (
                  <ActivityIndicator
                    size="small"
                    color={primaryColor}
                    style={styles.cityLoadingIndicator}
                  />
                )}
              </View>

              {/* City Suggestions Dropdown */}
              {showCitySuggestions && citySuggestions.length > 0 && (
                <View
                  style={[
                    styles.citySuggestionsDropdown,
                    { backgroundColor, borderColor },
                  ]}
                >
                  {citySuggestions.map((item, index) => (
                    <TouchableOpacity
                      key={`${item}-${index}`}
                      style={styles.citySuggestionItem}
                      onPress={() => handleCitySelect(item)}
                    >
                      <Text
                        style={[
                          styles.citySuggestionText,
                          { color: textColor },
                        ]}
                      >
                        {item}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              )}

              {errors.city && (
                <Text style={[styles.errorText, { color: errorColor }]}>
                  {errors.city}
                </Text>
              )}
            </View>

            {/* Pincode Input */}
            <View style={styles.inputContainer}>
              <Text style={[styles.label, { color: textColor }]}>Pincode</Text>
              <View style={styles.pincodeContainer}>
                <TextInput
                  style={[
                    styles.input,
                    styles.pincodeInput,
                    { borderColor, color: textColor, backgroundColor },
                    errors.pincode && { borderColor: errorColor },
                  ]}
                  value={pincode}
                  onChangeText={handlePincodeChange}
                  placeholder="Enter 6-digit pincode"
                  placeholderTextColor={secondaryTextColor}
                  keyboardType="numeric"
                  maxLength={6}
                />
                {pincode.length > 0 && !isLoadingLocalities && (
                  <TouchableOpacity
                    onPress={handleClearPincode}
                    style={styles.clearButton}
                  >
                    <X size={16} color={secondaryTextColor} />
                  </TouchableOpacity>
                )}
                {isLoadingLocalities && (
                  <ActivityIndicator
                    size="small"
                    color={primaryColor}
                    style={styles.loadingIndicator}
                  />
                )}
              </View>
              {errors.pincode && (
                <Text style={[styles.errorText, { color: errorColor }]}>
                  {errors.pincode}
                </Text>
              )}
            </View>

            {/* Locality Selection - Always visible */}
            <View style={styles.inputContainer}>
              <Text style={[styles.label, { color: textColor }]}>Locality</Text>
              <TouchableOpacity
                style={[
                  styles.input,
                  styles.localityPicker,
                  { borderColor, backgroundColor },
                ]}
                onPress={() => {
                  localityPickerRef.current?.present();
                }}
              >
                <Text
                  style={[
                    styles.localityText,
                    {
                      color: locality ? textColor : secondaryTextColor,
                    },
                  ]}
                >
                  {locality
                    ? locality
                    : localities.length === 0
                    ? "Enter pincode to load localities"
                    : "All Localities"}
                </Text>
                <ChevronDown size={20} color={secondaryTextColor} />
              </TouchableOpacity>
            </View>

            {/* Error Messages */}
            {errors.location && (
              <Text style={[styles.errorText, { color: errorColor }]}>
                {errors.location}
              </Text>
            )}
          </View>
        </ScrollView>
      </SafeAreaView>

      {/* Locality Bottom Sheet Picker - positioned at Modal root level */}
      <LocalityBottomSheetPicker
        ref={localityPickerRef}
        localities={["All Localities", ...localities]}
        selectedLocality={locality}
        onLocalitySelect={handleLocalitySelect}
        placeholder="Select your locality"
      />
    </Modal>
  );
}
