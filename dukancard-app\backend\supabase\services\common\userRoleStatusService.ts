/**
 * User Role Status Service for React Native
 * 
 * Handles user role and onboarding status operations using direct Supabase client calls
 * with Row Level Security (RLS) policies for security.
 * 
 * This service replaces the need for Next.js proxy API routes by leveraging:
 * - RLS policies for security (users can only access their own data)
 * - Direct Supabase client calls for better performance
 * - Comprehensive role status determination
 */

import { supabase } from '@/lib/supabase';

// Types for user role status operations
export interface UserRoleStatus {
  hasCustomerProfile: boolean;
  hasBusinessProfile: boolean;
  customerProfile?: any;
  businessProfile?: any;
  onboardingStatus: 'none' | 'customer' | 'business' | 'complete';
  businessOnboardingCompleted: boolean;
  customerProfileComplete: boolean;
  role: 'customer' | 'business' | null;
  needsRoleSelection: boolean;
  needsOnboarding: boolean;
  needsProfileCompletion: boolean;
}

export interface ServiceResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Get comprehensive user role and onboarding status
 * This replaces the /api/user/role-status route
 */
export async function getUserRoleStatus(): Promise<ServiceResult<UserRoleStatus>> {
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return { success: false, error: 'Authentication required' };
    }

    // Fetch both customer and business profiles in parallel
    const [customerResult, businessResult] = await Promise.all([
      supabase
        .from('customer_profiles')
        .select('*')
        .eq('id', user.id)
        .maybeSingle(),
      supabase
        .from('business_profiles')
        .select('*')
        .eq('id', user.id)
        .maybeSingle()
    ]);

    if (customerResult.error) {
      console.error('[USER_ROLE_STATUS_SERVICE] Error fetching customer profile:', customerResult.error);
      return { success: false, error: 'Failed to fetch customer profile' };
    }

    if (businessResult.error) {
      console.error('[USER_ROLE_STATUS_SERVICE] Error fetching business profile:', businessResult.error);
      return { success: false, error: 'Failed to fetch business profile' };
    }

    const customerProfile = customerResult.data;
    const businessProfile = businessResult.data;

    // Determine profile existence
    const hasCustomerProfile = !!customerProfile;
    const hasBusinessProfile = !!businessProfile;

    // Determine onboarding status
    let onboardingStatus: 'none' | 'customer' | 'business' | 'complete' = 'none';
    if (hasCustomerProfile && hasBusinessProfile) {
      onboardingStatus = 'complete';
    } else if (hasBusinessProfile) {
      onboardingStatus = 'business';
    } else if (hasCustomerProfile) {
      onboardingStatus = 'customer';
    }

    // Check if profiles are complete (matching customerProfileCompletionSchema)
    const customerProfileComplete = hasCustomerProfile &&
      customerProfile.name &&
      customerProfile.name.trim() !== '' &&
      customerProfile.pincode &&
      customerProfile.city &&
      customerProfile.state &&
      customerProfile.locality &&
      customerProfile.latitude &&
      customerProfile.longitude;

    const businessOnboardingCompleted = hasBusinessProfile && 
      businessProfile.business_name && 
      businessProfile.business_slug &&
      businessProfile.contact_email &&
      businessProfile.member_name &&
      businessProfile.phone &&
      businessProfile.business_category &&
      businessProfile.address_line &&
      businessProfile.pincode &&
      businessProfile.city &&
      businessProfile.state;

    // Determine primary role (business takes precedence)
    let role: 'customer' | 'business' | null = null;
    if (hasBusinessProfile) {
      role = 'business';
    } else if (hasCustomerProfile) {
      role = 'customer';
    }

    // Determine what the user needs to do next
    const needsRoleSelection = !hasCustomerProfile && !hasBusinessProfile;
    const needsOnboarding = (hasCustomerProfile && !customerProfileComplete) || 
                           (hasBusinessProfile && !businessOnboardingCompleted);
    const needsProfileCompletion = needsOnboarding;

    const roleStatus: UserRoleStatus = {
      hasCustomerProfile,
      hasBusinessProfile,
      customerProfile,
      businessProfile,
      onboardingStatus,
      businessOnboardingCompleted,
      customerProfileComplete,
      role,
      needsRoleSelection,
      needsOnboarding,
      needsProfileCompletion,
    };

    return { success: true, data: roleStatus };
  } catch (error) {
    console.error('[USER_ROLE_STATUS_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Check if user needs role selection
 */
export async function needsRoleSelection(): Promise<ServiceResult<boolean>> {
  try {
    const result = await getUserRoleStatus();
    if (!result.success) {
      return { success: false, error: result.error };
    }

    return { success: true, data: result.data!.needsRoleSelection };
  } catch (error) {
    console.error('[USER_ROLE_STATUS_SERVICE] Error checking role selection need:', error);
    return { success: false, error: 'Failed to check role selection need' };
  }
}

/**
 * Check if user needs onboarding
 */
export async function needsOnboarding(): Promise<ServiceResult<boolean>> {
  try {
    const result = await getUserRoleStatus();
    if (!result.success) {
      return { success: false, error: result.error };
    }

    return { success: true, data: result.data!.needsOnboarding };
  } catch (error) {
    console.error('[USER_ROLE_STATUS_SERVICE] Error checking onboarding need:', error);
    return { success: false, error: 'Failed to check onboarding need' };
  }
}

/**
 * Get user's primary role
 */
export async function getUserRole(): Promise<ServiceResult<'customer' | 'business' | null>> {
  try {
    const result = await getUserRoleStatus();
    if (!result.success) {
      return { success: false, error: result.error };
    }

    return { success: true, data: result.data!.role };
  } catch (error) {
    console.error('[USER_ROLE_STATUS_SERVICE] Error getting user role:', error);
    return { success: false, error: 'Failed to get user role' };
  }
}

/**
 * Check if business onboarding is completed
 */
export async function isBusinessOnboardingCompleted(): Promise<ServiceResult<boolean>> {
  try {
    const result = await getUserRoleStatus();
    if (!result.success) {
      return { success: false, error: result.error };
    }

    return { success: true, data: result.data!.businessOnboardingCompleted };
  } catch (error) {
    console.error('[USER_ROLE_STATUS_SERVICE] Error checking business onboarding:', error);
    return { success: false, error: 'Failed to check business onboarding status' };
  }
}

/**
 * Check if customer profile is complete
 */
export async function isCustomerProfileComplete(): Promise<ServiceResult<boolean>> {
  try {
    const result = await getUserRoleStatus();
    if (!result.success) {
      return { success: false, error: result.error };
    }

    return { success: true, data: result.data!.customerProfileComplete };
  } catch (error) {
    console.error('[USER_ROLE_STATUS_SERVICE] Error checking customer profile:', error);
    return { success: false, error: 'Failed to check customer profile status' };
  }
}

/**
 * Get onboarding status
 */
export async function getOnboardingStatus(): Promise<ServiceResult<'none' | 'customer' | 'business' | 'complete'>> {
  try {
    const result = await getUserRoleStatus();
    if (!result.success) {
      return { success: false, error: result.error };
    }

    return { success: true, data: result.data!.onboardingStatus };
  } catch (error) {
    console.error('[USER_ROLE_STATUS_SERVICE] Error getting onboarding status:', error);
    return { success: false, error: 'Failed to get onboarding status' };
  }
}

/**
 * Determine next action for user based on their status
 */
export async function getNextAction(): Promise<ServiceResult<{
  action: 'role_selection' | 'customer_onboarding' | 'business_onboarding' | 'complete_profile' | 'none';
  message: string;
  route?: string;
}>> {
  try {
    const result = await getUserRoleStatus();
    if (!result.success) {
      return { success: false, error: result.error };
    }

    const status = result.data!;

    if (status.needsRoleSelection) {
      return {
        success: true,
        data: {
          action: 'role_selection',
          message: 'Please select your role to continue',
          route: '/role-selection',
        }
      };
    }

    if (status.hasCustomerProfile && !status.customerProfileComplete) {
      return {
        success: true,
        data: {
          action: 'customer_onboarding',
          message: 'Please complete your customer profile',
          route: '/customer-onboarding',
        }
      };
    }

    if (status.hasBusinessProfile && !status.businessOnboardingCompleted) {
      return {
        success: true,
        data: {
          action: 'business_onboarding',
          message: 'Please complete your business onboarding',
          route: '/business-onboarding',
        }
      };
    }

    if (status.needsProfileCompletion) {
      return {
        success: true,
        data: {
          action: 'complete_profile',
          message: 'Please complete your profile information',
          route: status.role === 'business' ? '/business-onboarding' : '/customer-onboarding',
        }
      };
    }

    return {
      success: true,
      data: {
        action: 'none',
        message: 'Profile is complete',
      }
    };
  } catch (error) {
    console.error('[USER_ROLE_STATUS_SERVICE] Error determining next action:', error);
    return { success: false, error: 'Failed to determine next action' };
  }
}
