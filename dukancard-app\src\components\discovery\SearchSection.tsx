/**
 * SearchSection component for React Native Discovery Screen
 * Based on dukancard/app/(main)/discover/components/ImprovedSearchSection.tsx
 * and dukancard-app/src/components/social/SearchComponent.tsx
 */

import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/src/hooks/useTheme";

interface SearchSectionProps {
  searchTerm: string;
  onSearch: (term: string) => void;
  viewType: "cards" | "products";
  onSortPress: () => void;
  onClearSearch?: () => void; // New prop for external clear functionality
}

export const SearchSection: React.FC<SearchSectionProps> = ({
  searchTerm,
  onSearch,
  viewType,
  onSortPress,
  onClearSearch,
}) => {
  const { colors } = useTheme();
  const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm);

  // Update local value when prop value changes
  useEffect(() => {
    setLocalSearchTerm(searchTerm);
  }, [searchTerm]);

  // Handle manual search trigger
  const handleManualSearch = () => {
    if (localSearchTerm.trim() !== searchTerm) {
      onSearch(localSearchTerm.trim());
    }
  };

  // Handle Enter key press
  const handleSubmitEditing = () => {
    handleManualSearch();
  };

  // Handle clear functionality
  const handleClear = () => {
    setLocalSearchTerm("");
    onSearch("");
    if (onClearSearch) {
      onClearSearch();
    }
  };

  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      {/* Two Column Layout: Search Field + Sort Icon */}
      <View style={styles.rowContainer}>
        {/* Search Field */}
        <View style={styles.searchContainer}>
          <Ionicons
            name={viewType === "cards" ? "business" : "cube"}
            size={20}
            color={colors.textSecondary}
            style={styles.searchIcon}
          />
          <TextInput
            style={styles.searchInput}
            value={localSearchTerm}
            onChangeText={setLocalSearchTerm}
            onSubmitEditing={handleSubmitEditing}
            placeholder={
              viewType === "cards"
                ? "Search businesses..."
                : "Search products or services..."
            }
            placeholderTextColor={colors.textSecondary}
            autoCapitalize="words"
            autoCorrect={false}
            returnKeyType="search"
          />
          {/* Search Icon Button */}
          <TouchableOpacity
            onPress={handleManualSearch}
            style={styles.searchButton}
          >
            <Ionicons name="search" size={20} color={colors.primary} />
          </TouchableOpacity>
        </View>

        {/* Sort Icon */}
        <TouchableOpacity onPress={onSortPress} style={styles.sortButton}>
          <Ionicons name="funnel-outline" size={24} color="#C29D5B" />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      paddingHorizontal: 20,
      paddingVertical: 4,
      backgroundColor: colors.background,
    },
    rowContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: 12,
    },
    searchContainer: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: colors.cardBackground,
      borderRadius: 12,
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderWidth: 1,
      borderColor: colors.border,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    searchIcon: {
      marginRight: 12,
    },
    searchInput: {
      flex: 1,
      fontSize: 16,
      color: colors.textPrimary,
      paddingVertical: 0, // Remove default padding on Android
    },
    searchButton: {
      padding: 4,
      marginLeft: 8,
    },
    hintContainer: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 4,
      marginTop: 4,
    },
    hintText: {
      fontSize: 12,
      color: colors.textSecondary,
      marginLeft: 6,
      flex: 1,
      lineHeight: 16,
    },
    sortButton: {
      width: 48,
      height: 48,
      borderRadius: 12,
      backgroundColor: colors.cardBackground,
      alignItems: "center",
      justifyContent: "center",
      borderWidth: 1,
      borderColor: colors.border,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
  });
