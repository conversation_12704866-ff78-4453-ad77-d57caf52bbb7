import { StyleSheet } from 'react-native';
import { useTheme } from '@/src/hooks/useTheme';
import { responsiveFontSize } from '@/lib/theme/colors';

export const createPasswordUpdateSectionStyles = (theme: ReturnType<typeof useTheme>) => {
  return StyleSheet.create({
    container: {
      backgroundColor: theme.colors.card,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      shadowColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.md,
    },
    header: {
      marginBottom: theme.spacing.lg,
      paddingBottom: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.sm,
    },
    iconContainer: {
      padding: theme.spacing.sm,
      borderRadius: 8,
      backgroundColor: theme.colors.primary + '20', // 20% opacity
    },
    titleContent: {
      flex: 1,
    },
    title: {
      fontSize: responsiveFontSize(18),
      fontWeight: '600',
      color: theme.colors.foreground,
    },
    subtitle: {
      fontSize: responsiveFontSize(12),
      color: theme.colors.mutedForeground,
      marginTop: responsiveFontSize(2),
    },
    content: {
      gap: theme.spacing.md,
    },
    infoContainer: {
      backgroundColor: '#F59E0B' + '10', // Amber with 10% opacity
      borderRadius: 8,
      padding: theme.spacing.md,
      borderWidth: 1,
      borderColor: '#F59E0B' + '30', // Amber with 30% opacity
    },
    infoContent: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.sm,
    },
    infoText: {
      fontSize: responsiveFontSize(14),
      color: theme.colors.foreground,
      flex: 1,
      lineHeight: responsiveFontSize(20),
    },
    form: {
      gap: theme.spacing.md,
    },
    inputContainer: {
      marginBottom: theme.spacing.sm,
    },
    helperText: {
      fontSize: responsiveFontSize(12),
      color: theme.colors.textSecondary,
      lineHeight: responsiveFontSize(16),
      marginTop: -theme.spacing.sm,
      marginBottom: theme.spacing.sm,
    },
    buttonContainer: {
      alignItems: 'flex-end',
      marginTop: theme.spacing.md,
    },
    submitButton: {
      minWidth: responsiveFontSize(140),
    },
  });
};
