"use client";

import { useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { itemFadeIn } from "@/app/(main)/components/landing/animations";

declare global {
  interface Window {
    hbspt: {
      forms: {
        create: (_options: {
          portalId: string;
          formId: string;
          region: string;
          target?: string;
        }) => void;
      };
    };
  }
}

export default function HubSpotForm() {
  const formRef = useRef<HTMLDivElement>(null);
  const scriptLoadedRef = useRef(false);
  const formIdRef = useRef<string>("");

  useEffect(() => {
    // Generate a unique form ID
    const formId = `hubspot-form-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    formIdRef.current = formId;

    // Set the ID on the form container
    if (formRef.current) {
      formRef.current.id = formId;
    }

    const loadHubSpotScript = () => {
      // Check if script is already loaded
      if (window.hbspt) {
        // Add a small delay to ensure DOM is ready
        setTimeout(createForm, 100);
        return;
      }

      // Create and load the HubSpot script
      const script = document.createElement("script");
      script.src = "//js-na2.hsforms.net/forms/embed/v2.js";
      script.type = "text/javascript";
      script.async = true;

      script.onload = () => {
        // Add a small delay to ensure DOM is ready
        setTimeout(createForm, 100);
      };

      script.onerror = () => {
        console.error("Failed to load HubSpot form script");
      };

      document.head.appendChild(script);
      scriptLoadedRef.current = true;
    };

    const createForm = () => {
      const targetElement = document.getElementById(formIdRef.current);

      if (window.hbspt && targetElement) {
        // Clear any existing form content
        targetElement.innerHTML = "";

        try {
          // Create the form
          window.hbspt.forms.create({
            portalId: "242513860",
            formId: "cfe182d4-bcc0-4b48-a7e5-9827605f2636",
            region: "na2",
            target: `#${formIdRef.current}`
          });
        } catch (error) {
          console.error("Error creating HubSpot form:", error);
        }
      } else {
        console.warn("HubSpot script not loaded or target element not found");
      }
    };

    // Only load the script once
    if (!scriptLoadedRef.current) {
      loadHubSpotScript();
    } else if (window.hbspt) {
      // If script is already loaded, just create the form
      setTimeout(createForm, 100);
    }

    // Cleanup function
    return () => {
      // Note: HubSpot forms don't provide a clean destroy method
      // The form will be cleaned up when the component unmounts
    };
  }, []);

  return (
    <motion.div
      variants={itemFadeIn}
      custom={3}
      className="bg-white/90 dark:bg-black/60 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-800/50 rounded-2xl p-8 shadow-lg"
    >
      <h3 className="text-2xl font-semibold mb-6 text-foreground text-center">
        Get Started Today
      </h3>
      <p className="text-muted-foreground text-center mb-6">
        Fill out the form below and we&apos;ll get back to you with a customized advertising solution.
      </p>
      
      {/* HubSpot Form Container */}
      <div 
        ref={formRef}
        className="hubspot-form-container"
        style={{
          // Custom styles for the HubSpot form
          '--hs-form-bg': 'transparent',
          '--hs-form-border': 'var(--border)',
          '--hs-form-text': 'var(--foreground)',
        } as React.CSSProperties}
      />
      
      {/* Custom CSS for HubSpot form styling */}
      <style jsx>{`
        :global(.hubspot-form-container .hs-form) {
          font-family: inherit !important;
        }
        
        :global(.hubspot-form-container .hs-form-field) {
          margin-bottom: 1rem !important;
        }
        
        :global(.hubspot-form-container .hs-form-field label) {
          color: var(--foreground) !important;
          font-weight: 500 !important;
          margin-bottom: 0.5rem !important;
          display: block !important;
        }
        
        :global(.hubspot-form-container .hs-input) {
          width: 100% !important;
          padding: 0.75rem !important;
          border: 1px solid var(--border) !important;
          border-radius: 0.5rem !important;
          background-color: var(--background) !important;
          color: var(--foreground) !important;
          font-size: 0.875rem !important;
          transition: border-color 0.2s ease !important;
        }
        
        :global(.hubspot-form-container .hs-input:focus) {
          outline: none !important;
          border-color: var(--brand-gold) !important;
          box-shadow: 0 0 0 2px rgba(var(--brand-gold-rgb), 0.1) !important;
        }
        
        :global(.hubspot-form-container .hs-button) {
          background-color: var(--brand-gold) !important;
          color: black !important;
          border: none !important;
          padding: 0.75rem 2rem !important;
          border-radius: 0.5rem !important;
          font-weight: 600 !important;
          cursor: pointer !important;
          transition: all 0.2s ease !important;
          width: 100% !important;
          margin-top: 1rem !important;
        }
        
        :global(.hubspot-form-container .hs-button:hover) {
          background-color: var(--brand-gold) !important;
          opacity: 0.9 !important;
          transform: translateY(-1px) !important;
        }
        
        :global(.hubspot-form-container .hs-error-msgs) {
          color: #ef4444 !important;
          font-size: 0.75rem !important;
          margin-top: 0.25rem !important;
        }
        
        :global(.hubspot-form-container .hs-form-required) {
          color: #ef4444 !important;
        }
        
        /* Dark mode adjustments */
        :global(.dark .hubspot-form-container .hs-input) {
          background-color: var(--background) !important;
          border-color: var(--border) !important;
          color: var(--foreground) !important;
        }
        
        :global(.dark .hubspot-form-container .hs-form-field label) {
          color: var(--foreground) !important;
        }
      `}</style>
    </motion.div>
  );
}
