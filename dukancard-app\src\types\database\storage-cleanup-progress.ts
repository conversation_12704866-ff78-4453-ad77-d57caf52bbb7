export interface StorageCleanupProgressRow {
  id: number;
  run_id: string | null;
  bucket_name: string;
  user_id: string;
  files_deleted: number | null;
  status: string;
  error_message: string | null;
  processed_at: string | null;
}

export interface StorageCleanupProgressInsert {
  id?: number;
  run_id?: string | null;
  bucket_name: string;
  user_id: string;
  files_deleted?: number | null;
  status: string;
  error_message?: string | null;
  processed_at?: string | null;
}

export interface StorageCleanupProgressUpdate {
  id?: number;
  run_id?: string | null;
  bucket_name?: string;
  user_id?: string;
  files_deleted?: number | null;
  status?: string;
  error_message?: string | null;
  processed_at?: string | null;
}

export type StorageCleanupProgress = StorageCleanupProgressRow;
