"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { BarChart3, ChevronLeft, ChevronRight } from "lucide-react";
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useIsMobile } from "@/hooks/use-mobile";
import { formatIndianNumberShort } from "@/lib/utils";
import PremiumFeatureLock from "./PremiumFeatureLock";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

interface MonthlyVisitTrendChartProps {
  monthlyTrend: { year: number; month: number; visits: number }[];
  availableYears: number[];
  userPlan?: string | null;
}

const chartConfig = {
  visits: {
    label: "Visits",
    color: "var(--chart-3)",
  },
} satisfies ChartConfig;

export default function MonthlyVisitTrendChart({
  monthlyTrend,
  availableYears,
  userPlan,
}: MonthlyVisitTrendChartProps) {
  const isMobile = useIsMobile();
  const [selectedYear, setSelectedYear] = useState<number>(
    // Default to current year or the latest available year
    new Date().getFullYear()
  );

  // Filter data for the selected year
  const filteredData = monthlyTrend.filter(
    (item) => item.year === selectedYear
  );

  // Format month number to month name
  const formatMonth = (monthNum: number) => {
    const monthNames = [
      "Jan", "Feb", "Mar", "Apr", "May", "Jun",
      "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
    ];
    return monthNames[monthNum - 1];
  };

  // Generate all 12 months
  const generateAllMonths = () => {
    const months = [];
    for (let i = 1; i <= 12; i++) {
      months.push(i);
    }
    return months;
  };

  // Get all months
  const allMonths = generateAllMonths();

  // Create a map of existing data
  const dataMap = new Map();
  filteredData.forEach(item => {
    dataMap.set(item.month, item.visits);
  });

  // Format data for the chart with all months, using 0 for missing data
  const chartData = allMonths.map(month => ({
    month: formatMonth(month),
    visits: dataMap.has(month) ? dataMap.get(month) : 0,
    monthNum: month, // Keep the month number for sorting
  }));

  // Calculate the maximum value for better Y-axis scaling
  const maxVisits = Math.max(...chartData.map(item => item.visits));
  // Calculate a nice rounded upper bound for the Y-axis
  const calculateYAxisMax = (maxValue: number) => {
    if (maxValue <= 0) return 5; // Default if no data
    if (maxValue <= 5) return Math.ceil(maxValue * 1.2); // Add 20% padding for small values
    if (maxValue <= 10) return Math.ceil(maxValue * 1.1); // Add 10% padding for medium values
    return Math.ceil(maxValue * 1.05); // Add 5% padding for large values
  };
  const yAxisMax = calculateYAxisMax(maxVisits);





  // Custom formatter for Y-axis ticks using Indian number format
  const formatYAxisTick = (value: number) => {
    return formatIndianNumberShort(Math.floor(value));
  };

  // Handle year change
  const handleYearChange = (value: string) => {
    setSelectedYear(parseInt(value));
  };

  // Navigate to previous year if available
  const goToPreviousYear = () => {
    const availableYearsSorted = [...availableYears].sort((a, b) => a - b);
    const currentIndex = availableYearsSorted.indexOf(selectedYear);
    if (currentIndex > 0) {
      setSelectedYear(availableYearsSorted[currentIndex - 1]);
    }
  };

  // Navigate to next year if available
  const goToNextYear = () => {
    const availableYearsSorted = [...availableYears].sort((a, b) => a - b);
    const currentIndex = availableYearsSorted.indexOf(selectedYear);
    if (currentIndex < availableYearsSorted.length - 1) {
      setSelectedYear(availableYearsSorted[currentIndex + 1]);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  // Check if user has access to this premium feature
  const isPremiumUser = userPlan === "growth" || userPlan === "pro" || userPlan === "enterprise";

  // If user doesn't have a premium plan, show the premium feature lock component
  if (!isPremiumUser) {
    return (
      <PremiumFeatureLock
        title="Monthly Visit Trend"
        description="Upgrade to Growth plan or higher to see detailed monthly visit trends for your business."
      />
    );
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black p-3 sm:p-4 md:p-6 shadow-sm"
    >
      <div className="mb-4">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
          <h3 className="text-base sm:text-lg font-semibold text-neutral-900 dark:text-neutral-100">
            Monthly Visit Trend
          </h3>
          <div className="flex items-center space-x-2 text-xs sm:text-sm text-neutral-500 dark:text-neutral-400">
            <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4" />
            <span>Unique visitors by month</span>
          </div>
        </div>
      </div>

      {/* Year Selection Controls */}
      <div className="mb-4 flex flex-col sm:flex-row items-center gap-3 sm:gap-2">
        <div className="flex w-full sm:w-auto justify-between gap-2 mb-3 sm:mb-0">
          <Button
            variant="outline"
            size="sm"
            onClick={goToPreviousYear}
            disabled={availableYears.indexOf(selectedYear) === 0}
            className="px-2 sm:px-3"
          >
            <ChevronLeft className="h-4 w-4" />
            <span className={isMobile ? "sr-only" : "ml-1"}>Previous Year</span>
          </Button>

          <Select value={selectedYear.toString()} onValueChange={handleYearChange}>
            <SelectTrigger className={isMobile ? "w-[120px]" : "w-[180px]"}>
              <SelectValue placeholder="Select Year" />
            </SelectTrigger>
            <SelectContent>
              {availableYears.map((year) => (
                <SelectItem key={year} value={year.toString()}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            size="sm"
            onClick={goToNextYear}
            disabled={availableYears.indexOf(selectedYear) === availableYears.length - 1}
            className="px-2 sm:px-3"
          >
            <span className={isMobile ? "sr-only" : "mr-1"}>Next Year</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Chart */}
      <div className="h-[250px] sm:h-[300px] w-full px-1 pb-2">
        <ChartContainer config={chartConfig} className="h-full w-full">
          <AreaChart
            data={chartData}
            margin={isMobile
              ? { top: 5, right: 5, left: 0, bottom: 5 }
              : { top: 10, right: 10, left: 0, bottom: 5 }
            }
          >
            <defs>
              <linearGradient id="fillVisits" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-visits)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-visits)"
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis
              dataKey="month"
              axisLine={false}
              tickLine={false}
              tickMargin={8}
              minTickGap={32}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              domain={[0, yAxisMax]}
              allowDecimals={false}
              tickFormatter={formatYAxisTick}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  labelFormatter={(label) => `Month: ${label}`}
                  formatter={(value) => [
                    formatIndianNumberShort(Number(value)),
                    " Visits"
                  ]}
                />
              }
            />
            <Area
              dataKey="visits"
              type="natural"
              fill="url(#fillVisits)"
              stroke="var(--color-visits)"
              strokeWidth={isMobile ? 1.5 : 2}
            />
          </AreaChart>
        </ChartContainer>
      </div>
    </motion.div>
  );
}
