/**
 * API Client for React Native to Next.js Communication
 * 
 * This utility handles authenticated API calls to the Next.js backend,
 * automatically including required headers and authentication.
 */

import { BACKEND_CONFIG } from '@/src/config/publicKeys';
import { supabase } from '@/lib/supabase';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface ApiRequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  body?: any;
  headers?: Record<string, string>;
  timeout?: number;
}

/**
 * Make an authenticated API call to the Next.js backend
 */
export async function apiCall<T = any>(
  endpoint: string,
  options: ApiRequestOptions = {}
): Promise<ApiResponse<T>> {
  try {
    const {
      method = 'GET',
      body,
      headers = {},
      timeout = 30000,
    } = options;

    // Get the current session for authentication
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return {
        success: false,
        error: 'Authentication required. Please log in again.',
      };
    }

    // Prepare the request URL
    const url = `${BACKEND_CONFIG.baseUrl}${BACKEND_CONFIG.apiPrefix}${endpoint}`;

    // Prepare headers
    const requestHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session.access_token}`,
      'x-client-info': 'dukancard-mobile-client',
      'User-Agent': 'dukancard-app',
      ...headers,
    };

    // Prepare the request
    const requestOptions: RequestInit = {
      method,
      headers: requestHeaders,
    };

    // Add body for non-GET requests
    if (body && method !== 'GET') {
      requestOptions.body = JSON.stringify(body);
    }

    // Create abort controller for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      // Make the request
      const response = await fetch(url, {
        ...requestOptions,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Parse the response
      const responseData = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: responseData.error || `HTTP ${response.status}: ${response.statusText}`,
        };
      }

      return responseData as ApiResponse<T>;

    } catch (fetchError) {
      clearTimeout(timeoutId);
      
      if (fetchError instanceof Error) {
        if (fetchError.name === 'AbortError') {
          return {
            success: false,
            error: 'Request timeout. Please try again.',
          };
        }
        
        return {
          success: false,
          error: `Network error: ${fetchError.message}`,
        };
      }
      
      return {
        success: false,
        error: 'Unknown network error occurred.',
      };
    }

  } catch (error) {
    console.error('API call error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

/**
 * Convenience methods for different HTTP verbs
 */
export const api = {
  get: <T = any>(endpoint: string, options?: Omit<ApiRequestOptions, 'method'>) =>
    apiCall<T>(endpoint, { ...options, method: 'GET' }),

  post: <T = any>(endpoint: string, body?: any, options?: Omit<ApiRequestOptions, 'method' | 'body'>) =>
    apiCall<T>(endpoint, { ...options, method: 'POST', body }),

  put: <T = any>(endpoint: string, body?: any, options?: Omit<ApiRequestOptions, 'method' | 'body'>) =>
    apiCall<T>(endpoint, { ...options, method: 'PUT', body }),

  patch: <T = any>(endpoint: string, body?: any, options?: Omit<ApiRequestOptions, 'method' | 'body'>) =>
    apiCall<T>(endpoint, { ...options, method: 'PATCH', body }),

  delete: <T = any>(endpoint: string, options?: Omit<ApiRequestOptions, 'method'>) =>
    apiCall<T>(endpoint, { ...options, method: 'DELETE' }),
};

/**
 * Helper function to handle API errors consistently
 */
export function handleApiError(response: ApiResponse, defaultMessage = 'An error occurred') {
  if (!response.success) {
    const errorMessage = response.error || defaultMessage;
    console.error('API Error:', errorMessage);
    throw new Error(errorMessage);
  }
  return response.data;
}

/**
 * Type-safe API call with automatic error handling
 */
export async function safeApiCall<T = any>(
  endpoint: string,
  options?: ApiRequestOptions,
  defaultErrorMessage?: string
): Promise<T> {
  const response = await apiCall<T>(endpoint, options);
  return handleApiError(response, defaultErrorMessage);
}
