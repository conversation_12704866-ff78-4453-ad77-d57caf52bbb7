import { useColorScheme } from "@/src/hooks/useColorScheme";
import React, { useState, useEffect } from "react";
import { StyleSheet, View } from "react-native";
import {
  useRouter,
  useLocalSearchParams,
  useSegments,
  Stack,
} from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useNetworkStatus } from "@/src/utils/networkStatus";
import { OfflineBanner } from "@/src/components/ui/OfflineComponents";
import { useAuth } from "@/src/contexts/AuthContext";
import UnifiedBottomNavigation from "@/src/components/shared/navigation/UnifiedBottomNavigation";

// Import screen components
import DiscoverScreen from "@/src/components/shared/screens/DiscoverScreenNew";
import CustomerFeedScreen from "./index";
import CustomerProfileScreen from "./profile";

export default function CustomerTabLayout() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const networkStatus = useNetworkStatus();
  const { user, profileStatus } = useAuth();
  const { tab } = useLocalSearchParams<{ tab?: string }>();
  const segments = useSegments();
  const [activeTab, setActiveTab] = useState("home");
  const insets = useSafeAreaInsets();

  // Check if we're on a specific route that should use Stack navigation
  const currentSegment = segments.length > 2 ? (segments as string[])[2] : null;
  const isSpecificRoute =
    currentSegment &&
    currentSegment !== "index" &&
    currentSegment !== "profile" &&
    !currentSegment.startsWith("?");

  // Set initial tab based on URL parameter
  useEffect(() => {
    if (tab && ["home", "discover", "profile"].includes(tab)) {
      setActiveTab(tab);
    }
  }, [tab]);

  // Get customer name from profile
  const customerName = profileStatus?.roleStatus?.hasCustomerProfile
    ? user?.user_metadata?.name ||
      user?.email?.split("@")[0] ||
      "Valued Customer"
    : "Valued Customer";

  // If we're on a specific route (like edit-profile), use Stack navigation
  if (isSpecificRoute) {
    return (
      <View style={styles.container}>
        {/* Offline Banner */}
        <OfflineBanner visible={!networkStatus.isConnected} />

        <Stack>
          <Stack.Screen
            name="edit-profile"
            options={{
              headerShown: false,
              title: "Edit Profile",
            }}
          />
          <Stack.Screen
            name="settings"
            options={{
              headerShown: false,
              title: "Settings",
            }}
          />
          <Stack.Screen
            name="notifications"
            options={{
              headerShown: false,
              title: "Notifications",
            }}
          />
          <Stack.Screen
            name="favorites"
            options={{
              headerShown: false,
              title: "Favorites",
            }}
          />
          <Stack.Screen
            name="reviews"
            options={{
              headerShown: false,
              title: "Reviews",
            }}
          />
          <Stack.Screen
            name="subscriptions"
            options={{
              headerShown: false,
              title: "Subscriptions",
            }}
          />
          <Stack.Screen
            name="activity"
            options={{
              headerShown: false,
              title: "Activity",
            }}
          />
        </Stack>
      </View>
    );
  }

  const renderActiveScreen = () => {
    switch (activeTab) {
      case "home":
        return <CustomerFeedScreen />;
      case "discover":
        return (
          <DiscoverScreen />
        );
      case "profile":
        return <CustomerProfileScreen />;
      default:
        return <CustomerFeedScreen />;
    }
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: colorScheme === 'dark' ? '#000000' : '#FFFFFF' }]}>
      {/* Offline Banner */}
      <OfflineBanner visible={!networkStatus.isConnected} />

      <View style={styles.screenContainer}>{renderActiveScreen()}</View>

      <UnifiedBottomNavigation
        activeTab={activeTab}
        onTabChange={handleTabChange}
        showQRScanner={true}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  screenContainer: {
    flex: 1,
    marginBottom: 0, // Ensure no extra margin
  },
});
