import { ErrorType } from '@/src/components/ui/ErrorState';

export interface AppError {
  type: ErrorType;
  title: string;
  message: string;
  code?: string;
  statusCode?: number;
  originalError?: any;
}

/**
 * Creates a standardized error object
 */
export function createAppError(
  type: ErrorType,
  title: string,
  message: string,
  options?: {
    code?: string;
    statusCode?: number;
    originalError?: any;
  }
): AppError {
  return {
    type,
    title,
    message,
    code: options?.code,
    statusCode: options?.statusCode,
    originalError: options?.originalError,
  };
}

/**
 * Handles network errors and converts them to user-friendly messages
 */
export function handleNetworkError(error: any): AppError {
  console.error('Network error:', error);

  // Check if it's a network connectivity issue
  if (!error.response && error.request) {
    return createAppError(
      'network',
      'No Internet Connection',
      'Please check your internet connection and try again.',
      { originalError: error }
    );
  }

  // Handle HTTP status codes
  if (error.response) {
    const statusCode = error.response.status;
    
    switch (statusCode) {
      case 400:
        return createAppError(
          'validation',
          'Invalid Request',
          'The request contains invalid data. Please check your input.',
          { statusCode, originalError: error }
        );
      
      case 401:
        return createAppError(
          'unauthorized',
          'Authentication Required',
          'Please sign in to access this content.',
          { statusCode, originalError: error }
        );
      
      case 403:
        return createAppError(
          'unauthorized',
          'Access Denied',
          'You don\'t have permission to access this content.',
          { statusCode, originalError: error }
        );
      
      case 404:
        return createAppError(
          'notFound',
          'Not Found',
          'The requested content could not be found.',
          { statusCode, originalError: error }
        );
      
      case 429:
        return createAppError(
          'server',
          'Too Many Requests',
          'You\'re making too many requests. Please wait a moment and try again.',
          { statusCode, originalError: error }
        );
      
      case 500:
      case 502:
      case 503:
      case 504:
        return createAppError(
          'server',
          'Server Error',
          'Something went wrong on our end. Please try again later.',
          { statusCode, originalError: error }
        );
      
      default:
        return createAppError(
          'generic',
          'Request Failed',
          'The request failed. Please try again.',
          { statusCode, originalError: error }
        );
    }
  }

  // Handle other types of errors
  if (error.message) {
    if (error.message.includes('timeout')) {
      return createAppError(
        'network',
        'Request Timeout',
        'The request took too long. Please try again.',
        { originalError: error }
      );
    }
    
    if (error.message.includes('Network Error')) {
      return createAppError(
        'network',
        'Network Error',
        'Unable to connect to the server. Please check your connection.',
        { originalError: error }
      );
    }
  }

  // Generic error fallback
  return createAppError(
    'generic',
    'Something went wrong',
    'An unexpected error occurred. Please try again.',
    { originalError: error }
  );
}

/**
 * Handles Supabase errors and converts them to user-friendly messages
 */
export function handleSupabaseError(error: any): AppError {
  console.error('Supabase error:', error);

  if (!error) {
    return createAppError(
      'generic',
      'Unknown Error',
      'An unknown error occurred.',
      { originalError: error }
    );
  }

  // Handle Supabase auth errors
  if (error.message) {
    const message = error.message.toLowerCase();
    
    if (message.includes('invalid login credentials')) {
      return createAppError(
        'unauthorized',
        'Invalid Credentials',
        'The email or password you entered is incorrect.',
        { code: error.code, originalError: error }
      );
    }
    
    if (message.includes('email not confirmed')) {
      return createAppError(
        'validation',
        'Email Not Verified',
        'Please check your email and click the verification link.',
        { code: error.code, originalError: error }
      );
    }
    
    if (message.includes('user not found')) {
      return createAppError(
        'notFound',
        'Account Not Found',
        'No account found with this email address.',
        { code: error.code, originalError: error }
      );
    }

    if (message.includes('user from sub claim in jwt does not exist') ||
        message.includes('session_not_found') ||
        message.includes('invalid_token')) {
      return createAppError(
        'unauthorized',
        'Session Expired',
        'Your session has expired. Please sign in again.',
        { code: error.code, originalError: error }
      );
    }
    
    if (message.includes('email already registered')) {
      return createAppError(
        'validation',
        'Email Already Exists',
        'An account with this email already exists.',
        { code: error.code, originalError: error }
      );
    }
    
    if (message.includes('password')) {
      return createAppError(
        'validation',
        'Password Error',
        'Password must be at least 6 characters long.',
        { code: error.code, originalError: error }
      );
    }
    
    if (message.includes('network') || message.includes('connection')) {
      return createAppError(
        'network',
        'Connection Error',
        'Unable to connect to the server. Please check your internet connection.',
        { code: error.code, originalError: error }
      );
    }
  }

  // Handle Supabase database errors
  if (error.code) {
    switch (error.code) {
      case 'PGRST116':
        return createAppError(
          'notFound',
          'Not Found',
          'The requested data could not be found.',
          { code: error.code, originalError: error }
        );
      
      case 'PGRST301':
        return createAppError(
          'validation',
          'Invalid Data',
          'The provided data is invalid or incomplete.',
          { code: error.code, originalError: error }
        );
      
      default:
        return createAppError(
          'server',
          'Database Error',
          'A database error occurred. Please try again.',
          { code: error.code, originalError: error }
        );
    }
  }

  // Generic Supabase error
  return createAppError(
    'generic',
    'Service Error',
    error.message || 'An error occurred with the service.',
    { originalError: error }
  );
}

/**
 * Logs errors for debugging and analytics
 */
export function logError(error: AppError | any, context?: string) {
  const timestamp = new Date().toISOString();
  const logData = {
    timestamp,
    context,
    error: error instanceof Error ? {
      name: error.name,
      message: error.message,
      stack: error.stack,
    } : error,
  };

  // Log to console in development
  if (__DEV__) {
    console.error('Error logged:', logData);
  }

  // In production, you might want to send this to a logging service
  // Example: Analytics.logError(logData);
}

/**
 * Retry utility with exponential backoff
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: any;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        throw error;
      }

      // Exponential backoff: delay = baseDelay * 2^attempt
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}
