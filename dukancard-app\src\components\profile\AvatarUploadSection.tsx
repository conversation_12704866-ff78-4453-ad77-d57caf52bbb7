import React from "react";
import { View, Text, TouchableOpacity, Image } from "react-native";
import { User, X } from "lucide-react-native";
import { ImagePickerBottomSheetRef } from "@/src/components/pickers/ImagePickerBottomSheet";
import { useToast } from "@/src/components/ui/Toast";
import { useAlertDialog } from "@/src/components/providers/AlertProvider";
import { deleteCustomerAvatar } from "@/backend/supabase/services/storage/avatarUploadService";
import { getCurrentUser } from "@/lib/auth/customerAuth";
import { router } from "expo-router";
import { useFormContext } from "react-hook-form";

interface AvatarUploadSectionProps {
  avatarUri: string;
  isLoading: boolean;
  theme: any; // TODO: Define a proper theme type
  imagePickerRef: React.RefObject<ImagePickerBottomSheetRef | null>;
}

const AvatarUploadSection: React.FC<AvatarUploadSectionProps> = ({
  avatarUri,
  isLoading,
  theme,
  imagePickerRef,
}) => {
  const toast = useToast();
  const { confirm } = useAlertDialog();
  const goldColor = "#D4AF37";
  const { setValue, watch } = useFormContext();

  // Handle avatar deletion
  const handleAvatarDeletePress = () => {
    confirm(
      "Remove Avatar",
      "Are you sure you want to remove your profile picture?",
      () => {
        // Only attempt to delete from storage if there was an existing avatar
        const currentAvatarUri = watch("avatarUri");
        if (currentAvatarUri) {
          deleteCustomerAvatar(currentAvatarUri);
        }
        setValue("avatarUri", "");
        toast.success(
          "Avatar Removed",
          "Your profile picture has been removed."
        );
      },
      undefined, // onCancel - no action needed
      {
        confirmText: "Remove",
        cancelText: "Cancel",
        type: "warning",
        destructive: true,
      }
    );
  };

  return (
    <View style={{ alignItems: "center", marginBottom: 16 }}>
      <View style={{ position: "relative", marginBottom: 8 }}>
        <TouchableOpacity
          onPress={() => imagePickerRef.current?.present()}
          style={{
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          {avatarUri ? (
            <Image
              source={{ uri: avatarUri }}
              style={{
                width: 120,
                height: 120,
                borderRadius: 60,
                borderWidth: 2,
                borderColor: goldColor,
              }}
            />
          ) : (
            <View
              style={{
                width: 120,
                height: 120,
                borderRadius: 60,
                backgroundColor: theme.colors.card,
                alignItems: "center",
                justifyContent: "center",
                borderWidth: 2,
                borderColor: goldColor,
              }}
            >
              <User size={60} color={theme.colors.textMuted} />
            </View>
          )}
        </TouchableOpacity>

        {/* Cross button - only show when avatar exists */}
        {avatarUri && (
          <TouchableOpacity
            onPress={handleAvatarDeletePress}
            style={{
              position: "absolute",
              top: 0,
              right: 0,
              width: 32,
              height: 32,
              borderRadius: 16,
              backgroundColor: "#ef4444", // red-500
              alignItems: "center",
              justifyContent: "center",
              shadowColor: "#000",
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.25,
              shadowRadius: 3.84,
              elevation: 5,
            }}
          >
            <X size={16} color="white" />
          </TouchableOpacity>
        )}
      </View>

      <Text
        style={{
          color: theme.colors.textMuted,
          marginBottom: 16,
          textAlign: "center",
        }}
      >
        Upload Image
      </Text>
    </View>
  );
};

export default AvatarUploadSection;
