import { useTheme } from '@/src/hooks/useTheme';
import React from 'react';
import {
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    View,
    ViewStyle,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface AuthContainerProps {
  children: React.ReactNode;
  scrollable?: boolean;
  showKeyboardAvoidingView?: boolean;
  contentContainerStyle?: ViewStyle;
  style?: ViewStyle;
}

/**
 * AuthContainer - Layout component for authentication screens
 * Provides consistent styling, safe area handling, and keyboard avoidance
 */
export function AuthContainer({
  children,
  scrollable = true,
  showKeyboardAvoidingView = true,
  contentContainerStyle,
  style,
}: AuthContainerProps) {
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  const styles = StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    scrollView: {
      flex: 1,
    },
    container: {
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      flexGrow: 1,
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.md,
    },
    contentContainer: {
      minHeight: '100%',
      justifyContent: 'space-between',
    },
    nonScrollableContent: {
      flex: 1,
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.md,
      justifyContent: 'center',
    },
  });

  const content = scrollable ? (
    <ScrollView
      style={styles.scrollView}
      contentContainerStyle={[styles.scrollContainer]}
      showsVerticalScrollIndicator={false}
      keyboardShouldPersistTaps="handled"
    >
      <View style={[styles.container, style]}>
        <View style={[styles.contentContainer, contentContainerStyle]}>
          {children}
        </View>
      </View>
    </ScrollView>
  ) : (
    <View style={[styles.container, style]}>
      <View style={[styles.nonScrollableContent, contentContainerStyle]}>
        {children}
      </View>
    </View>
  );

  const safeAreaStyle = {
    paddingTop: insets.top,
    paddingBottom: insets.bottom,
    paddingLeft: insets.left,
    paddingRight: insets.right,
  };

  if (showKeyboardAvoidingView) {
    return (
      <View style={[styles.safeArea, safeAreaStyle]}>
        <KeyboardAvoidingView
          style={styles.keyboardAvoidingView}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
        >
          {content}
        </KeyboardAvoidingView>
      </View>
    );
  }

  return (
    <View style={[styles.safeArea, safeAreaStyle]}>
      {content}
    </View>
  );
}

/**
 * AuthCard - Card component for authentication forms
 * Provides consistent card styling with theme-aware colors and shadows
 */
interface AuthCardProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export function AuthCard({ children, style }: AuthCardProps) {
  const theme = useTheme();
  
  const styles = StyleSheet.create({
    card: {
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.xl,
      padding: theme.spacing.xl,
      marginVertical: theme.spacing.md,
      ...theme.shadows.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
  });

  return (
    <View style={[styles.card, style]}>
      {children}
    </View>
  );
}

/**
 * AuthHeader - Header component for authentication screens
 * Displays logo, title, and subtitle with consistent styling
 */
interface AuthHeaderProps {
  title?: string;
  subtitle?: string;
  showLogo?: boolean;
  style?: ViewStyle;
}

export function AuthHeader({ 
  title, 
  subtitle, 
  showLogo = true, 
  style 
}: AuthHeaderProps) {
  const theme = useTheme();
  
  const styles = StyleSheet.create({
    header: {
      alignItems: 'center',
      marginBottom: theme.spacing.xl,
    },
    logoContainer: {
      marginBottom: theme.spacing.lg,
    },
    title: {
      fontSize: theme.typography.fontSize.xxxl,
      fontWeight: theme.typography.fontWeight.bold,
      color: theme.colors.textPrimary,
      textAlign: 'center',
      marginBottom: theme.spacing.sm,
    },
    subtitle: {
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: theme.typography.lineHeight.relaxed * theme.typography.fontSize.base,
    },
  });

  return (
    <View style={[styles.header, style]}>
      {showLogo && (
        <View style={styles.logoContainer}>
          {/* Logo component would go here */}
        </View>
      )}
      {title && (
        <Text style={styles.title}>{title}</Text>
      )}
      {subtitle && (
        <Text style={styles.subtitle}>{subtitle}</Text>
      )}
    </View>
  );
}

/**
 * AuthFooter - Footer component for authentication screens
 * Displays links and additional information with consistent styling
 */
interface AuthFooterProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export function AuthFooter({ children, style }: AuthFooterProps) {
  const theme = useTheme();
  
  const styles = StyleSheet.create({
    footer: {
      alignItems: 'center',
      marginTop: theme.spacing.xl,
      paddingTop: theme.spacing.lg,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
  });

  return (
    <View style={[styles.footer, style]}>
      {children}
    </View>
  );
}
