import { StyleSheet, ColorSchemeName } from 'react-native';

export const createEnhancedAdSectionStyles = (colorScheme: ColorSchemeName) => {
  const isDark = colorScheme === 'dark';
  
  return StyleSheet.create({
    sectionContainer: {
      paddingHorizontal: 0, // Full width - no horizontal padding
      paddingVertical: 16,
    },
    adContainer: {
      width: '100%',
      borderRadius: 0, // Remove border radius for full width
      overflow: 'hidden',
      backgroundColor: isDark ? '#1F2937' : '#F9FAFB',
      elevation: 0, // Remove shadow for full width
      shadowColor: 'transparent',
      shadowOffset: {
        width: 0,
        height: 0,
      },
      shadowOpacity: 0,
      shadowRadius: 0,
    },
    clickableAd: {
      borderRadius: 0, // Remove border radius for full width
      overflow: 'hidden',
    },
    imageContainer: {
      position: 'relative',
      width: '100%',
      aspectRatio: 16/9, // Use aspect ratio instead of fixed height
    },
    adImage: {
      width: '100%',
      height: '100%',
      backgroundColor: isDark ? '#374151' : '#E5E7EB',
    },
    hiddenImage: {
      opacity: 0,
    },
    loadingContainer: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: isDark ? '#374151' : '#E5E7EB',
    },
    sponsoredBadge: {
      position: 'absolute',
      top: 12,
      right: 12,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      paddingHorizontal: 10,
      paddingVertical: 6,
      borderRadius: 6,
    },
    sponsoredText: {
      color: '#FFFFFF',
      fontSize: 12,
      fontWeight: '600',
      letterSpacing: 0.5,
    },
    adBadge: {
      position: 'absolute',
      top: 12,
      right: 12,
      backgroundColor: 'rgba(212, 175, 55, 0.95)',
      paddingHorizontal: 10,
      paddingVertical: 6,
      borderRadius: 6,
    },
    adText: {
      color: '#000000',
      fontSize: 12,
      fontWeight: '600',
      letterSpacing: 0.5,
    },
    placeholderContainer: {
      width: '100%',
      borderRadius: 0, // Remove border radius for full width
      overflow: 'hidden',
      backgroundColor: isDark ? '#1F2937' : '#F9FAFB',
      borderWidth: 0, // Remove border for full width
      borderStyle: 'solid',
      borderColor: 'transparent',
      minHeight: 180,
    },
    placeholderContent: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32,
    },
    placeholderTitle: {
      fontSize: 20,
      fontWeight: '700',
      color: isDark ? '#F9FAFB' : '#1F2937',
      marginBottom: 8,
      textAlign: 'center',
    },
    placeholderSubtitle: {
      fontSize: 14,
      color: isDark ? '#9CA3AF' : '#6B7280',
      marginBottom: 20,
      textAlign: 'center',
      lineHeight: 20,
      paddingHorizontal: 16,
    },
    advertiseButton: {
      backgroundColor: '#D4AF37',
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 8,
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.2,
      shadowRadius: 2,
    },
    advertiseButtonText: {
      color: '#000000',
      fontSize: 14,
      fontWeight: '700',
      letterSpacing: 0.5,
    },
  });
};
