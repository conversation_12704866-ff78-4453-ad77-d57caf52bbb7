/**
 * Navigation handlers for Discovery Screen
 * Based on dukancard-app/app/business/[businessSlug].tsx and dukancard-app/app/product/[productId].tsx
 */

import { useRouter } from "expo-router";
import { BusinessCardData, NearbyProduct } from "@/src/types/discovery";
import { useToast } from "@/src/components/ui/Toast";

export const useDiscoveryNavigation = () => {
  const router = useRouter();
  const toast = useToast();

  /**
   * Navigate to business profile page
   * @param business Business card data
   */
  const navigateToBusinessProfile = (business: BusinessCardData) => {
    try {
      if (!business.business_slug) {
        toast.error("Navigation Error", "Business profile not available");
        return;
      }

      // Navigate to business profile using the slug
      router.push(`/business/${business.business_slug}`);
    } catch (error) {
      console.error("Error navigating to business profile:", error);
      toast.error("Navigation Error", "Failed to open business profile");
    }
  };

  /**
   * Navigate to product detail page
   * @param product Product data
   */
  const navigateToProductDetail = (product: NearbyProduct) => {
    try {
      if (!product.id) {
        toast.error("Navigation Error", "Product details not available");
        return;
      }

      // Navigate to product detail page using the product ID
      router.push(`/product/${product.id}`);
    } catch (error) {
      console.error("Error navigating to product detail:", error);
      toast.error("Navigation Error", "Failed to open product details");
    }
  };

  /**
   * Navigate to business profile from product
   * @param product Product data containing business information
   */
  const navigateToBusinessFromProduct = (product: NearbyProduct) => {
    try {
      if (!product.business_slug) {
        toast.error("Navigation Error", "Business profile not available");
        return;
      }

      // Navigate to business profile using the business slug from product
      router.push(`/business/${product.business_slug}`);
    } catch (error) {
      console.error("Error navigating to business from product:", error);
      toast.error("Navigation Error", "Failed to open business profile");
    }
  };

  /**
   * Navigate to discovery screen with specific filters
   * @param filters Discovery filters to apply
   */
  const navigateToDiscoveryWithFilters = (filters: {
    category?: string;
    viewType?: "cards" | "products";
    businessName?: string;
    productName?: string;
  }) => {
    try {
      const params = new URLSearchParams();

      if (filters.category) params.append("category", filters.category);
      if (filters.viewType) params.append("viewType", filters.viewType);
      if (filters.businessName)
        params.append("businessName", filters.businessName);
      if (filters.productName)
        params.append("productName", filters.productName);

      const queryString = params.toString();
      const path = queryString ? `/discovery?${queryString}` : "/discovery";

      router.push(path);
    } catch (error) {
      console.error("Error navigating to discovery with filters:", error);
      toast.error("Navigation Error", "Failed to apply filters");
    }
  };

  /**
   * Navigate back to previous screen
   */
  const navigateBack = () => {
    try {
      if (router.canGoBack()) {
        router.back();
      } else {
        // Fallback to home screen if no back history
        router.replace("/(tabs)/home");
      }
    } catch (error) {
      console.error("Error navigating back:", error);
      // Fallback to home screen
      router.replace("/(tabs)/home");
    }
  };

  /**
   * Navigate to home screen
   */
  const navigateToHome = () => {
    try {
      router.replace("/(tabs)/home");
    } catch (error) {
      console.error("Error navigating to home:", error);
      toast.error("Navigation Error", "Failed to navigate to home");
    }
  };

  /**
   * Navigate to search screen with pre-filled query
   * @param query Search query to pre-fill
   */
  const navigateToSearch = (query?: string) => {
    try {
      const path = query ? `/search?q=${encodeURIComponent(query)}` : "/search";
      router.push(path);
    } catch (error) {
      console.error("Error navigating to search:", error);
      toast.error("Navigation Error", "Failed to open search");
    }
  };

  /**
   * Share business profile
   * @param business Business card data
   */
  const shareBusinessProfile = async (business: BusinessCardData) => {
    try {
      const { Share } = await import("react-native");

      if (!business.business_slug) {
        toast.error(
          "Share Error",
          "Business profile not available for sharing"
        );
        return;
      }

      const shareUrl = `https://dukancard.com/business/${business.business_slug}`;
      const shareMessage = `Check out ${business.business_name} on DukanCard!\n\n${shareUrl}`;

      await Share.share({
        message: shareMessage,
        url: shareUrl,
        title: `${business.business_name} - DukanCard`,
      });
    } catch (error) {
      console.error("Error sharing business profile:", error);
      toast.error("Share Error", "Failed to share business profile");
    }
  };

  /**
   * Share product
   * @param product Product data
   */
  const shareProduct = async (product: NearbyProduct) => {
    try {
      const { Share } = await import("react-native");

      if (!product.id) {
        toast.error("Share Error", "Product not available for sharing");
        return;
      }

      const shareUrl = `https://dukancard.com/product/${product.id}`;
      const shareMessage = `Check out ${product.name} on DukanCard!\n\n${shareUrl}`;

      await Share.share({
        message: shareMessage,
        url: shareUrl,
        title: `${product.name} - DukanCard`,
      });
    } catch (error) {
      console.error("Error sharing product:", error);
      toast.error("Share Error", "Failed to share product");
    }
  };

  return {
    navigateToBusinessProfile,
    navigateToProductDetail,
    navigateToBusinessFromProduct,
    navigateToDiscoveryWithFilters,
    navigateBack,
    navigateToHome,
    navigateToSearch,
    shareBusinessProfile,
    shareProduct,
  };
};
