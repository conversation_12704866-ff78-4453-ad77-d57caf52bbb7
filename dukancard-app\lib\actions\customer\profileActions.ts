import { supabase } from '@/lib/supabase';
import {
  validateProfileName,
  validatePhone,
  validateEmail,
  validateMobile,
  validateAddress,
  cleanProfileData,
  cleanPhoneData,
  cleanEmailData,
  cleanMobileData,
  cleanAddressData,
  type ProfileData,
  type PhoneData,
  type EmailData,
  type MobileData,
  type AddressData,
} from '@/backend/supabase/utils/profileValidation';
import { CustomerProfileService } from '@/backend/supabase/services/customer/customerProfileService';

export type ProfileFormState = {
  message: string | null;
  errors?: {
    name?: string[];
    // Add other fields here
  };
  success: boolean;
};

export type PhoneFormState = {
  message: string | null;
  errors?: {
    phone?: string[];
  };
  success: boolean;
};

export type AddressFormState = {
  message: string | null;
  errors?: {
    address?: string[];
    pincode?: string[];
    city?: string[];
    state?: string[];
    locality?: string[];
  };
  success: boolean;
};

export type EmailFormState = {
  message: string | null;
  errors?: {
    email?: string[];
  };
  success: boolean;
};

export type MobileFormState = {
  message: string | null;
  errors?: {
    mobile?: string[];
  };
  success: boolean;
};

export async function updateCustomerProfile(
  prevState: ProfileFormState,
  formData: FormData
): Promise<ProfileFormState> {
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return { message: 'Not authenticated', success: false };
  }

  const rawName = (formData.get('name') ?? '') as string;
  const nameValidation = validateProfileName(rawName);

  if (!nameValidation.isValid) {
    return {
      message: 'Invalid data provided.',
      errors: { name: [nameValidation.error!] },
      success: false,
    };
  }

  const cleanedData = cleanProfileData({ name: rawName });
  const { name } = cleanedData;

  try {
    if (!user.id) {
      return { message: 'User ID not found', success: false };
    }

    // Update name in auth.users table (full_name in user_metadata)
    // The database trigger will automatically sync this to customer_profiles table
    const { error: authUpdateError } = await CustomerProfileService.updateCustomerName(user.id, name as string);

    if (authUpdateError) {
      console.error('Error updating auth user metadata:', authUpdateError);
      return { message: `Auth Error: ${authUpdateError.message}`, success: false };
    }

    return { message: 'Profile updated successfully!', success: true };
  } catch (error) {
    console.error('Unexpected error updating profile:', error);
    return { message: 'An unexpected error occurred.', success: false };
  }
}

export async function updateCustomerAddress(
  prevState: AddressFormState,
  formData: FormData
): Promise<AddressFormState> {
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return { message: 'Not authenticated', success: false };
  }

  const rawAddressData: AddressData = {
    address: (formData.get('address') ?? '') as string,
    pincode: (formData.get('pincode') ?? '') as string,
    city: (formData.get('city') ?? '') as string,
    state: (formData.get('state') ?? '') as string,
    locality: (formData.get('locality') ?? '') as string,
  };

  const addressValidation = validateAddress(rawAddressData);

  if (!addressValidation.isValid) {
    return {
      message: 'Invalid data provided.',
      errors: addressValidation.errors,
      success: false,
    };
  }

  const cleanedData = cleanAddressData(rawAddressData);
  const { address, pincode, city, state, locality } = cleanedData;

  try {
    const { error: updateError } = await CustomerProfileService.updateCustomerAddress(user.id, { address, pincode, city, state, locality });

    if (updateError) {
      console.error('Error updating customer address:', updateError);
      return { message: `Database Error: ${updateError.message}`, success: false };
    }

    return { message: 'Address updated successfully!', success: true };
  } catch (error) {
    console.error('Unexpected error updating address:', error);
    return { message: 'An unexpected error occurred.', success: false };
  }
}

export async function updateCustomerPhone(
  prevState: PhoneFormState,
  formData: FormData
): Promise<PhoneFormState> {
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return { message: 'Not authenticated', success: false };
  }

  const rawPhone = (formData.get('phone') ?? '') as string;
  const phoneValidation = validatePhone(rawPhone);

  if (!phoneValidation.isValid) {
    return {
      message: 'Invalid data provided.',
      errors: { phone: [phoneValidation.error!] },
      success: false,
    };
  }

  const cleanedData = cleanPhoneData({ phone: rawPhone });
  const { phone } = cleanedData;

  try {
    if (!user.id) {
      return { message: 'User ID not found', success: false };
    }

    // Note: Phone uniqueness check removed as multiple businesses/customers can share the same number

    // Update phone in customer_profiles table
    const { error: updateError } = await CustomerProfileService.updateCustomerPhone(user.id, phone as string);

    if (updateError) {
      console.error('Error updating customer phone:', updateError);
      return { message: `Database Error: ${updateError.message}`, success: false };
    }

    return { message: 'Phone number updated successfully!', success: true };
  } catch (error) {
    console.error('Unexpected error updating phone:', error);
    return { message: 'An unexpected error occurred.', success: false };
  }
}

export async function updateCustomerEmail(
  prevState: EmailFormState,
  formData: FormData
): Promise<EmailFormState> {
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return { message: 'Not authenticated', success: false };
  }

  const rawEmail = (formData.get('email') ?? '') as string;
  const emailValidation = validateEmail(rawEmail);

  if (!emailValidation.isValid) {
    return {
      message: 'Invalid data provided.',
      errors: { email: [emailValidation.error!] },
      success: false,
    };
  }

  const cleanedData = cleanEmailData({ email: rawEmail });
  const { email } = cleanedData;

  try {
    // Check if user registered with Google OAuth (matching settings page logic)
    const isGoogleLogin = user.app_metadata?.provider === 'google';

    // Check if the user has email/password authentication
    let hasEmailAuth = false;

    if (isGoogleLogin && user.email) {
      try {
        // For now, assume Google users don't have email/password auth
        // TODO: Implement API endpoint to check user identities if needed
        hasEmailAuth = false;
      } catch (error) {
        console.error("Error checking user auth methods:", error);
        hasEmailAuth = false;
      }
    }

    // Only disable email changes if they're using Google and don't have email auth
    const shouldDisableEmailChange = isGoogleLogin && !hasEmailAuth;

    if (shouldDisableEmailChange) {
      return {
        message: 'Email cannot be changed for Google accounts. Your email is linked to your Google account.',
        success: false,
      };
    }

    // Check if email is the same as current
    if (user.email === email) {
      return {
        message: 'Email address is the same as current.',
        success: false,
      };
    }

    if (!email) {
      return { message: 'Email is required', success: false };
    }

    // Update email in Supabase auth.users table
    // This is the primary source of truth for email
    const { error: authUpdateError } = await CustomerProfileService.updateCustomerEmail(email);

    if (authUpdateError) {
      console.error('Error updating auth email:', authUpdateError);

      // Provide user-friendly error messages
      let errorMessage = 'Failed to update email address.';
      if (authUpdateError.message.includes('duplicate key value violates unique constraint')) {
        errorMessage = 'This email address is already in use by another account.';
      } else if (authUpdateError.message.includes('check constraint')) {
        errorMessage = 'Invalid email format provided.';
      } else if (authUpdateError.message.includes('rate limit')) {
        errorMessage = 'Too many requests. Please try again later.';
      }

      return {
        message: errorMessage,
        success: false
      };
    }

    // Note: customer_profiles table will be automatically updated via database trigger

    return {
      message: 'Email address updated successfully! You may need to verify the new email address.',
      success: true
    };
  } catch (error) {
    console.error('Unexpected error updating email:', error);
    return { message: 'An unexpected error occurred.', success: false };
  }
}

export async function updateCustomerMobile(
  prevState: MobileFormState,
  formData: FormData
): Promise<MobileFormState> {
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return { message: 'Not authenticated', success: false };
  }

  const rawMobile = (formData.get('mobile') ?? '') as string;
  const mobileValidation = validateMobile(rawMobile);

  if (!mobileValidation.isValid) {
    return {
      message: 'Invalid data provided.',
      errors: { mobile: [mobileValidation.error!] },
      success: false,
    };
  }

  const cleanedData = cleanMobileData({ mobile: rawMobile });
  const { mobile } = cleanedData;

  try {
    // Check if mobile is the same as current
    const currentMobile = user.phone ? user.phone.replace(/^\+91/, '') : '';
    if (mobile === currentMobile) {
      return {
        message: 'Mobile number is the same as current.',
        success: false,
      };
    }

    if (!user.id) {
      return { message: 'User ID not found', success: false };
    }

    if (!mobile) {
      return { message: 'Mobile number is required', success: false };
    }

    // Note: Mobile uniqueness check removed as multiple businesses/customers can share the same number

    // Update mobile in Supabase auth.users table
    // This is the primary source of truth for mobile
    const { error: authUpdateError } = await CustomerProfileService.updateCustomerPhone(user.id, mobile);

    if (authUpdateError) {
      console.error('Error updating auth mobile:', authUpdateError);

      // Provide user-friendly error messages
      let errorMessage = 'Failed to update mobile number.';
      if (authUpdateError.message.includes('duplicate key value violates unique constraint')) {
        errorMessage = 'This mobile number is already in use by another account.';
      } else if (authUpdateError.message.includes('check constraint')) {
        errorMessage = 'Invalid mobile number format provided.';
      } else if (authUpdateError.message.includes('rate limit')) {
        errorMessage = 'Too many requests. Please try again later.';
      }

      return {
        message: errorMessage,
        success: false
      };
    }

    // Note: customer_profiles table will be automatically updated via database trigger

    return { message: 'Mobile number updated successfully!', success: true };
  } catch (error) {
    console.error('Unexpected error updating mobile:', error);
    return { message: 'An unexpected error occurred.', success: false };
  }
}
