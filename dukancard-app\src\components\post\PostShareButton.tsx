import React, { useState } from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  Share,
  Alert,
} from 'react-native';
import { Share2, Check } from 'lucide-react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { generatePostUrl } from '@/src/utils/postUrl';

interface PostShareButtonProps {
  postId: string;
  style?: any;
}

/**
 * Native share button component for posts using React Native Share API
 */
export default function PostShareButton({ postId, style }: PostShareButtonProps) {
  const [sharing, setSharing] = useState(false);
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Theme colors
  const buttonColor = '#3B82F6'; // Blue-600
  const textColor = '#FFFFFF';

  const handleShare = async () => {
    try {
      setSharing(true);
      const postUrl = generatePostUrl(postId);
      
      const result = await Share.share({
        message: `Check out this post on Dukancard: ${postUrl}`,
        url: postUrl, // iOS only
        title: 'Dukancard Post', // Android only
      });

      if (result.action === Share.sharedAction) {
        // Post was shared successfully
      } else if (result.action === Share.dismissedAction) {
        // Share dialog was dismissed
      }
    } catch (error) {
      console.error('Error sharing post:', error);
      Alert.alert(
        'Share Failed',
        'Unable to share this post. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setSharing(false);
    }
  };

  return (
    <TouchableOpacity
      onPress={handleShare}
      disabled={sharing}
      style={[
        styles.shareButton,
        { backgroundColor: buttonColor },
        sharing && styles.sharingButton,
        style,
      ]}
      activeOpacity={0.8}
    >
      {sharing ? (
        <Check size={16} color={textColor} />
      ) : (
        <Share2 size={16} color={textColor} />
      )}
      <Text style={[styles.shareText, { color: textColor }]}>
        {sharing ? 'Sharing...' : 'Share'}
      </Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  shareButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  sharingButton: {
    opacity: 0.7,
  },
  shareText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '600',
  },
});
