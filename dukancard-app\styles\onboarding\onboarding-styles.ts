import { useTheme } from '@/src/hooks/useTheme';
import { StyleSheet } from 'react-native';
import { EdgeInsets } from 'react-native-safe-area-context';

export const createOnboardingStyles = (theme: ReturnType<typeof useTheme>, insets?: EdgeInsets) => {
  return StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    container: {
      flex: 1,
      paddingHorizontal: theme.spacing.md, // Reduced from lg
    },
    
    // Top section with logo and title
    topSection: {
      flex: 0.35,
      justifyContent: 'center',
      alignItems: 'center',
      paddingTop: theme.spacing.xl,
    },
    logoContainer: {
      alignItems: 'center',
      marginBottom: theme.spacing.xl,
    },
    headerContainer: {
      alignItems: 'center',
      paddingHorizontal: theme.spacing.md,
    },
    title: {
      fontSize: theme.typography.fontSize.xxl,
      fontWeight: theme.typography.fontWeight.bold,
      textAlign: 'center',
      marginBottom: theme.spacing.sm,
      color: theme.colors.textPrimary,
      letterSpacing: -0.5,
    },
    subtitle: {
      fontSize: theme.typography.fontSize.base,
      textAlign: 'center',
      color: theme.colors.textSecondary,
      lineHeight: theme.typography.lineHeight.relaxed * theme.typography.fontSize.base,
      maxWidth: 320,
      fontWeight: theme.typography.fontWeight.normal,
      marginBottom: theme.spacing.lg,
    },
    
    // Progress indicator
    progressContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      gap: theme.spacing.sm,
      marginTop: theme.spacing.lg,
    },
    progressDot: {
      width: 40,
      height: 4,
      borderRadius: theme.borderRadius.full,
      backgroundColor: theme.colors.muted,
    },
    progressDotActive: {
      backgroundColor: theme.colors.primary,
    },
    stepText: {
      fontSize: theme.typography.fontSize.xs,
      color: theme.colors.textMuted,
      textAlign: 'center',
      marginTop: theme.spacing.sm,
      fontWeight: theme.typography.fontWeight.medium,
    },
    
    // Middle section with form content
    middleSection: {
      flex: 0.5,
      justifyContent: 'center',
      paddingVertical: theme.spacing.xl,
    },
    formCard: {
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.xl,
      padding: theme.spacing.xl,
      ...theme.shadows.sm,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    formContainer: {
      gap: theme.spacing.md, // Reduced from lg
    },
    
    // Bottom section with navigation
    bottomSection: {
      flex: 0.15,
      justifyContent: 'flex-end',
      paddingBottom: Math.max(theme.spacing.xl, (insets?.bottom || 0) + theme.spacing.xl),
    },
    navigationContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.md,
    },
    backButton: {
      flex: 1, // Equal width
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    nextButton: {
      flex: 1, // Equal width
    },
    
    // Loading states
    loadingContainer: {
      alignItems: 'center',
      gap: theme.spacing.lg,
      paddingVertical: theme.spacing.xxxl,
    },
    loadingText: {
      fontSize: theme.typography.fontSize.lg,
      color: theme.colors.textSecondary,
      fontWeight: theme.typography.fontWeight.semibold,
    },
    
    // Special styles for specific screens
    categoryPickerButton: {
      borderRadius: theme.borderRadius.lg,
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.md,
      minHeight: 48,
      backgroundColor: theme.colors.input,
      borderWidth: 1,
      borderColor: theme.colors.border,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    categoryPickerText: {
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.textPrimary,
      fontWeight: theme.typography.fontWeight.medium,
    },
    categoryPickerPlaceholder: {
      color: theme.colors.textMuted,
    },
    
    // URL preview styles
    urlPreviewContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.sm,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      backgroundColor: theme.colors.muted,
      borderRadius: theme.borderRadius.md,
    },
    urlPreviewText: {
      color: theme.colors.textSecondary,
      fontSize: theme.typography.fontSize.sm,
      marginLeft: theme.spacing.sm,
    },
    
    // Plan selection styles
    planContainer: {
      gap: theme.spacing.md,
    },
    planCard: {
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.lg,
      borderWidth: 2,
      borderColor: theme.colors.border,
    },
    planCardSelected: {
      borderColor: theme.colors.primary,
      backgroundColor: theme.colors.primary + '10',
    },
    planTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: theme.typography.fontWeight.bold,
      color: theme.colors.textPrimary,
      marginBottom: theme.spacing.sm,
    },
    planPrice: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: theme.typography.fontWeight.bold,
      color: theme.colors.primary,
      marginBottom: theme.spacing.sm,
    },
    planDescription: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      lineHeight: theme.typography.lineHeight.relaxed * theme.typography.fontSize.sm,
    },
    
    // Error states
    errorContainer: {
      padding: theme.spacing.md,
      backgroundColor: theme.colors.error + '10',
      borderRadius: theme.borderRadius.md,
      borderWidth: 1,
      borderColor: theme.colors.error + '30',
      marginTop: theme.spacing.sm,
    },
    errorText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.error,
      textAlign: 'center',
      fontWeight: theme.typography.fontWeight.medium,
    },
  });
};

// Dummy default export to satisfy Expo Router
export default function OnboardingStylesPlaceholder() {
  return null;
}
