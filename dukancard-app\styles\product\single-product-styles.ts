import { StyleSheet, Dimensions } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { responsiveFontSize } from "@/lib/theme/colors";

const { width: screenWidth } = Dimensions.get("window");

export const createSingleProductStyles = (theme: ReturnType<typeof useTheme>) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: 20,
    },
    loadingText: {
      marginTop: theme.spacing.md,
      fontSize: theme.typography.fontSize.base,
      fontWeight: "500",
    },
    errorContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: 20,
    },
    errorTitle: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: "bold",
      textAlign: "center",
      marginBottom: theme.spacing.sm,
    },
    errorMessage: {
      fontSize: theme.typography.fontSize.base,
      textAlign: "center",
      marginBottom: theme.spacing.lg,
    },
    retryButton: {
      paddingVertical: theme.spacing.sm,
      paddingHorizontal: theme.spacing.lg,
      backgroundColor: "#D4AF37",
      borderRadius: theme.borderRadius.md,
    },
    retryButtonText: {
      color: "#FFFFFF",
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderBottomWidth: 1,
      borderColor: theme.colors.border,
    },
    backButton: {
      padding: 8,
    },
    headerTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: "600",
      flex: 1,
      textAlign: "center",
      color: theme.colors.textPrimary,
    },
    headerSpacer: {
      flex: 1,
    },
    shareButton: {
      padding: 8,
    },
    content: {
      flex: 1,
    },
    imageContainer: {
      width: screenWidth,
      height: screenWidth, // Square aspect ratio to match Next.js
      position: "relative",
    },
    productImage: {
      width: "100%",
      height: "100%",
      resizeMode: "cover",
    },
    placeholderImage: {
      width: "100%",
      height: "100%",
      justifyContent: "center",
      alignItems: "center",
    },
    imageLoadingOverlay: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "rgba(0, 0, 0, 0.3)",
    },
    productInfo: {
      margin: theme.spacing.md,
    },
    productName: {
      fontSize: theme.typography.fontSize.xxl,
      fontWeight: "bold",
      marginBottom: theme.spacing.sm,
      color: theme.colors.textPrimary,
    },
    priceContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: theme.spacing.sm,
      gap: theme.spacing.xs,
    },
    price: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: "600",
      color: theme.colors.textPrimary,
    },
    discountedPrice: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: "600",
      color: theme.colors.textPrimary,
    },
    originalPrice: {
      fontSize: theme.typography.fontSize.base,
      textDecorationLine: "line-through",
      color: theme.colors.textSecondary,
    },
    description: {
      fontSize: theme.typography.fontSize.base,
      lineHeight:
        theme.typography.lineHeight.normal * theme.typography.fontSize.base,
      color: theme.colors.textSecondary,
    },
    businessInfo: {
      margin: theme.spacing.md,
      marginTop: 0,
      padding: theme.spacing.md,
      borderRadius: theme.borderRadius.lg,
      backgroundColor: theme.colors.card,
    },
    businessHeader: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    businessMainInfo: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1,
    },
    businessAvatar: {
      width: responsiveFontSize(48),
      height: responsiveFontSize(48),
      borderRadius: responsiveFontSize(24),
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: theme.colors.secondary,
      marginRight: theme.spacing.sm,
      borderWidth: 2,
      borderColor: theme.colors.primary,
      overflow: "hidden",
    },
    businessLogo: {
      width: responsiveFontSize(44),
      height: responsiveFontSize(44),
      borderRadius: responsiveFontSize(22),
    },
    businessAvatarImage: {
      width: responsiveFontSize(48),
      height: responsiveFontSize(48),
      borderRadius: responsiveFontSize(24),
    },
    businessDetails: {
      flex: 1,
    },
    businessName: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: "600",
      marginBottom: theme.spacing.xs,
      color: theme.colors.textPrimary,
    },
    businessSlug: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
    },
    distanceContainer: {
      alignItems: "center",
      justifyContent: "center",
    },
    distanceText: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: "600",
      color: theme.colors.textSecondary,
    },
    contactButtons: {
      flexDirection: "row",
      margin: theme.spacing.md,
      marginTop: 0,
      gap: theme.spacing.sm,
    },
    whatsappButton: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: "#25D366",
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      gap: theme.spacing.xs,
    },
    callButton: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: "#007AFF",
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      gap: theme.spacing.xs,
    },
    contactButtonText: {
      color: theme.colors.textOnPrimary,
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
    },
    shareButtonContainer: {
      marginHorizontal: 16,
      marginTop: 12,
    },
    shareButtonRelocated: {
      backgroundColor: "#6B7280",
      paddingVertical: 12,
      paddingHorizontal: 20,
      borderRadius: 8,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
    },
  });
