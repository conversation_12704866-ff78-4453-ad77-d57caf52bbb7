/**
 * Styles for Customer Reviews Activity Screen
 */

import { StyleSheet } from 'react-native';

export const createReviewsActivityStyles = (colorScheme: 'light' | 'dark' | null) => {
  const isDark = colorScheme === 'dark';
  
  return StyleSheet.create({
    // Header styles
    headerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: isDark ? '#333333' : '#e5e5e5',
      backgroundColor: isDark ? '#000000' : '#ffffff',
    },
    backButton: {
      padding: 8,
      marginRight: 8,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: isDark ? '#ffffff' : '#1a1a1a',
      flex: 1,
      textAlign: 'center',
    },
    headerSpacer: {
      width: 40, // Same width as back button to center title
    },
    container: {
      flex: 1,
      backgroundColor: isDark ? '#000000' : '#ffffff',
    },
    header: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: isDark ? '#333333' : '#e5e5e5',
    },
    countText: {
      fontSize: 16,
      fontWeight: '600',
      color: isDark ? '#ffffff' : '#1a1a1a',
      textAlign: 'center',
    },
    sortContainer: {
      paddingHorizontal: 16,
      paddingVertical: 8,
    },
    listContainer: {
      paddingHorizontal: 16,
      paddingBottom: 20,
    },
    emptyListContainer: {
      flexGrow: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingTop: 60,
    },
    footerLoader: {
      paddingVertical: 20,
      alignItems: 'center',
    },
    // Loading states
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: isDark ? '#000000' : '#ffffff',
    },
    loadingText: {
      marginTop: 12,
      fontSize: 16,
      color: isDark ? '#cccccc' : '#666666',
    },
    // Error states
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 32,
      backgroundColor: isDark ? '#000000' : '#ffffff',
    },
    errorText: {
      fontSize: 16,
      color: isDark ? '#ff6b6b' : '#dc3545',
      textAlign: 'center',
      marginBottom: 16,
    },
    retryButton: {
      backgroundColor: '#D4AF37',
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 8,
    },
    retryButtonText: {
      color: '#ffffff',
      fontSize: 16,
      fontWeight: '600',
    },
  });
};
