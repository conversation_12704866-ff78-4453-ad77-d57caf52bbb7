# 🔐 Dukancard App - Keystore & Credentials Management

## 📱 App Information
- **App Name**: Dukancard
- **Package Name**: com.dukancardapp.dukancard
- **Bundle Identifier (iOS)**: com.dukancardapp.dukancard
- **Version**: 1.0.0

## 🔑 Android Keystore Information

### Development Keystore (Debug)
```
Keystore File: debug.keystore (auto-generated by React Native)
Location: android/app/debug.keystore
Store Password: android
Key Alias: androiddebugkey
Key Password: android
Validity: Default (1 year)
```

### Production Keystore (Upload Key for Google Play) ✅ GENERATED
```
Keystore File: dukancard-upload-key.keystore
Location: android/app/dukancard-upload-key.keystore
Store Password: DukancardProd2025
Key Alias: dukancard-upload-key
Key Password: DukancardProd2025
Validity: 10000 days (~27 years)
Algorithm: RSA
Key Size: 2048
Distinguished Name: CN=Dukancard App, OU=Development Team, O=Dukancard, L=Rourkela, ST=Odisha, C=IN
```

## 🛠️ Keystore Generation Commands

### For Windows (Run as Administrator from JDK bin folder):
```bash
keytool -genkeypair -v -storetype PKCS12 -keystore dukancard-upload-key.keystore -alias dukancard-upload-key -keyalg RSA -keysize 2048 -validity 10000
```

### For macOS/Linux:
```bash
sudo keytool -genkey -v -keystore dukancard-upload-key.keystore -alias dukancard-upload-key -keyalg RSA -keysize 2048 -validity 10000
```

## 📋 Keystore Generation Prompts & Answers

When running the keytool command, you'll be prompted for:

1. **Keystore Password**: [ENTER SECURE PASSWORD - SAVE THIS]
2. **Key Password**: [ENTER SECURE PASSWORD - SAVE THIS]
3. **First and Last Name**: Dukancard App
4. **Organizational Unit**: Development Team
5. **Organization**: Dukancard
6. **City/Locality**: Rourkela
7. **State/Province**: Odisha
8. **Country Code**: [Your Country Code - e.g., IN for India]

## 🔐 Gradle Properties Configuration

### File: ~/.gradle/gradle.properties (Global - Recommended)
```properties
DUKANCARD_UPLOAD_STORE_FILE=dukancard-upload-key.keystore
DUKANCARD_UPLOAD_KEY_ALIAS=dukancard-upload-key
DUKANCARD_UPLOAD_STORE_PASSWORD=DukancardProd2025
DUKANCARD_UPLOAD_KEY_PASSWORD=DukancardProd2025
```

### Alternative: android/gradle.properties (Project-specific)
```properties
# Add the same properties as above
# Note: This file should be added to .gitignore for security
```

## 🌐 Google OAuth Credentials

### Current Configuration (Development):
```
Google iOS Client ID: 110991972471-9g5hbuj78s88b5fi0m97i4k0823v7430.apps.googleusercontent.com
Google Web Client ID: 110991972471-ek9ra016ca12ucaobil2s8oid86k6to4.apps.googleusercontent.com
Google Android Client ID (Dev): 110991972471-jskkg6qg8g33mk1qrv9el5u0bj0f8cql.apps.googleusercontent.com
Debug SHA-1: 5E:8F:16:06:2E:A3:CD:2C:4A:0D:54:78:76:BA:A6:F3:8C:AB:F6:25
```

### Production OAuth Setup:
1. **Google Cloud Console Project**: ✅ CONFIGURED
2. **Android OAuth Client ID (Production)**: `110991972471-bauq8cmll9nlrdl6ok7svvt5rgfuhno7.apps.googleusercontent.com`
3. **Production SHA-1 Fingerprint**: `67:AA:47:E7:6B:0C:FA:4A:53:4A:82:2C:70:DA:6F:46:1D:81:7F:8D`
4. **Production SHA-256 Fingerprint**: `81:2E:84:6A:D7:91:A3:E8:A2:AF:44:29:B8:95:2D:D2:B5:4E:80:EE:19:71:5D:EA:BF:F9:2D:EC:39:A3:7F:C3`

## 🔍 SHA Fingerprint Extraction Commands

### Get SHA-1 and SHA-256 from Production Keystore:
```bash
keytool -list -v -keystore dukancard-upload-key.keystore -alias dukancard-upload-key
```

### Get SHA-1 from Debug Keystore (for development):
```bash
keytool -list -v -keystore android/app/debug.keystore -alias androiddebugkey -storepass android -keypass android
```

## 🗄️ Supabase Configuration

### Current Hardcoded Values (TO BE MOVED TO BUILD CONFIG):
```
Supabase URL: https://rnjolcoecogzgglnblqn.supabase.co
Supabase Anon Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o
Supabase Service Role Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzA1MjA1NiwiZXhwIjoyMDU4NjI4MDU2fQ.HGJ--VU4KdlcrT9Dsa2rWUPNtL_bYHEl6YfczRK8I0E
```

## 📝 Security Checklist

- [ ] Generate production keystore
- [ ] Store keystore passwords securely
- [ ] Add gradle.properties to .gitignore
- [ ] Move keystore file to secure location
- [ ] Generate Google OAuth credentials for production
- [ ] Extract SHA fingerprints
- [ ] Update app.config.js with build-time configuration
- [ ] Remove hardcoded API keys from source code
- [ ] Test production build signing
- [ ] Backup keystore and credentials securely

## ⚠️ IMPORTANT SECURITY NOTES

1. **NEVER commit keystore files to git**
2. **NEVER commit gradle.properties with passwords to git**
3. **Keep multiple secure backups of your production keystore**
4. **If you lose your production keystore, you cannot update your app on Play Store**
5. **Store credentials in a secure password manager**
6. **Use different passwords for keystore and key**

## 🚀 Build Commands

### Development Build:
```bash
npm run android                    # Uses debug keystore
npx expo run:android               # Alternative with Expo
```

### Production Build:
```bash
npx react-native build-android --mode=release    # Generates AAB for Play Store
npm run android -- --mode="release"              # Test production build on device
```

## 📁 File Locations

```
dukancard-app/
├── android/
│   ├── app/
│   │   ├── debug.keystore                    # Debug keystore (auto-generated)
│   │   ├── dukancard-upload-key.keystore     # Production keystore (to be generated)
│   │   └── build.gradle                      # Signing configuration
│   └── gradle.properties                     # Local gradle properties (optional)
├── ~/.gradle/gradle.properties               # Global gradle properties (recommended)
└── KEYSTORE_CREDENTIALS.md                   # This file
```
