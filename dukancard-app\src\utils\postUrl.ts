/**
 * Utility functions for post URL generation and deep linking in React Native
 */

import { BACKEND_CONFIG } from '../../src/config/publicKeys';

/**
 * Generate a complete URL for a single post page
 * @param postId - The ID of the post
 * @returns Complete URL for the post
 */
export function generatePostUrl(postId: string): string {
  return `${BACKEND_CONFIG.baseUrl}/post/${postId}`;
}

/**
 * Generate a deep link URL for the React Native app
 * @param postId - The ID of the post
 * @returns Deep link URL for the post
 */
export function generatePostDeepLink(postId: string): string {
  // This would be your app's deep link scheme
  // For example: dukancard://post/[postId]
  return `dukancard://post/${postId}`;
}

/**
 * Generate a relative path for navigation within the app
 * @param postId - The ID of the post
 * @returns Relative path for the post
 */
export function generatePostPath(postId: string): string {
  return `/post/${postId}`;
}

/**
 * Extract post ID from a post URL or deep link
 * @param url - The post URL or deep link
 * @returns Post ID if valid, null otherwise
 */
export function extractPostIdFromUrl(url: string): string | null {
  try {
    // Handle both HTTP URLs and deep links
    let urlObj: URL;
    
    if (url.startsWith('dukancard://')) {
      // Handle deep link
      urlObj = new URL(url);
    } else {
      // Handle HTTP URL
      urlObj = new URL(url);
    }
    
    const pathParts = urlObj.pathname.split('/');
    
    // Expected format: /post/[postId] or dukancard://post/[postId]
    if (pathParts.length >= 3 && pathParts[1] === 'post') {
      const postId = pathParts[2];
      return postId && postId.trim() !== '' ? postId : null;
    }
    
    return null;
  } catch (error) {
    console.error('Error extracting post ID from URL:', error);
    return null;
  }
}

/**
 * Validate if a string is a valid post ID format
 * @param postId - The post ID to validate
 * @returns True if valid, false otherwise
 */
export function isValidPostId(postId: string): boolean {
  if (!postId || typeof postId !== 'string') {
    return false;
  }
  
  // Basic validation - should be a non-empty string
  // You can add more specific validation based on your post ID format
  return postId.trim().length > 0;
}

/**
 * Generate sharing text for social media and messaging apps
 * @param postId - The ID of the post
 * @param authorName - Optional author name
 * @returns Formatted sharing text
 */
export function generateSharingText(postId: string, authorName?: string): string {
  const postUrl = generatePostUrl(postId);
  
  if (authorName) {
    return `Check out this post by ${authorName} on Dukancard: ${postUrl}`;
  }
  
  return `Check out this post on Dukancard: ${postUrl}`;
}

/**
 * Check if a URL is a valid post URL for this app
 * @param url - The URL to check
 * @returns True if it's a valid post URL, false otherwise
 */
export function isPostUrl(url: string): boolean {
  const postId = extractPostIdFromUrl(url);
  return postId !== null && isValidPostId(postId);
}
