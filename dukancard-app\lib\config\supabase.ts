/**
 * Centralized Supabase Configuration
 * Uses hardcoded public keys from src/config/publicKeys.ts
 */

import { SUPABASE_CONFIG as HARDCODED_CONFIG } from '../../src/config/publicKeys';

// Supabase Project Configuration - Using hardcoded values
export const SUPABASE_CONFIG = HARDCODED_CONFIG;

// Export individual values for convenience
export const SUPABASE_URL = SUPABASE_CONFIG.url;
export const SUPABASE_ANON_KEY = SUPABASE_CONFIG.anonKey;
// Service role key removed for security - use API routes instead

// Type definitions
export type SupabaseConfig = typeof SUPABASE_CONFIG;
