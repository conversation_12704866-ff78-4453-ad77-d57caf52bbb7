import { supabase } from '@/lib/supabase';
import { completeBusinessOnboarding as completeOnboarding } from '@/backend/supabase/services/business/businessOnboardingService';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Types for onboarding data - aligned with Next.js web version
export interface OnboardingFormData {
  // Step 1: Business Details
  businessName: string;
  email: string;

  // Step 2: Card Information
  memberName: string;
  title: string;
  phone: string;
  businessCategory: string;
  businessSlug: string;

  // Step 3: Address Information
  addressLine: string;
  pincode: string;
  city: string;
  state: string;
  locality: string;
  latitude?: number;
  longitude?: number;
  businessStatus: 'online' | 'offline';

  // Step 4: Plan Selection
  planId: string;
}

export interface OnboardingResult {
  success: boolean;
  error?: string;
  data?: any;
}

// Storage keys for onboarding data
const ONBOARDING_STORAGE_KEY = 'onboarding_data';

/**
 * Save onboarding data to AsyncStorage
 */
export async function saveOnboardingData(data: Partial<OnboardingFormData>): Promise<void> {
  try {
    const existingData = await getOnboardingData();
    const updatedData = { ...existingData, ...data };
    await AsyncStorage.setItem(ONBOARDING_STORAGE_KEY, JSON.stringify(updatedData));
  } catch (error) {
    console.error('Error saving onboarding data:', error);
  }
}

/**
 * Get onboarding data from AsyncStorage
 */
export async function getOnboardingData(): Promise<Partial<OnboardingFormData>> {
  try {
    const data = await AsyncStorage.getItem(ONBOARDING_STORAGE_KEY);
    return data ? JSON.parse(data) : {};
  } catch (error) {
    console.error('Error getting onboarding data:', error);
    return {};
  }
}

/**
 * Clear onboarding data from AsyncStorage
 */
export async function clearOnboardingData(): Promise<void> {
  try {
    await AsyncStorage.removeItem(ONBOARDING_STORAGE_KEY);
  } catch (error) {
    console.error('Error clearing onboarding data:', error);
  }
}

/**
 * Validate business slug availability - matches Next.js web version exactly
 */
export async function checkSlugAvailability(slug: string): Promise<{ available: boolean; error?: string }> {
  try {
    // Basic validation - must be at least 3 characters
    if (!slug || slug.length < 3) {
      return { available: false, error: 'Slug must be at least 3 characters.' };
    }

    // Format validation - exact same regex as Next.js web version
    if (!/^[a-z0-9]+(?:-[a-z0-9]+)*$/.test(slug)) {
      return {
        available: false,
        error: 'Invalid format (lowercase, numbers, hyphens only).',
      };
    }

    // Check for reserved words (same as Next.js web version)
    const reservedWords = [
      'admin', 'api', 'www', 'app', 'mail', 'ftp', 'localhost', 'test',
      'staging', 'dev', 'development', 'prod', 'production', 'beta',
      'alpha', 'demo', 'support', 'help', 'about', 'contact', 'privacy',
      'terms', 'legal', 'blog', 'news', 'login', 'register', 'signup',
      'signin', 'logout', 'dashboard', 'profile', 'settings', 'account',
      'billing', 'payment', 'checkout', 'cart', 'shop', 'store',
      'dukancard', 'dukan', 'card'
    ];

    if (reservedWords.includes(slug.toLowerCase())) {
      return { available: false, error: 'This URL is reserved and cannot be used.' };
    }

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return { available: false, error: 'Authentication required to check slug availability.' };
    }

    // Use regular client with public read access to check if slug exists (excluding current user)
    const { data: existingProfile, error } = await supabase
      .from('business_profiles')
      .select('id, business_slug')
      .ilike('business_slug', slug)
      .neq('id', user.id)
      .maybeSingle();

    if (error) {
      console.error('Error checking slug availability:', error);
      return { available: false, error: 'Database error checking slug.' };
    }

    const isAvailable = !existingProfile;
    return { available: isAvailable };
  } catch (error) {
    console.error('Error in checkSlugAvailability:', error);
    return { available: false, error: 'An unexpected error occurred.' };
  }
}

/**
 * Complete business onboarding by creating business profile and subscription
 */
export async function completeBusinessOnboarding(planId: string): Promise<OnboardingResult> {
  try {
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return { success: false, error: 'User not authenticated' };
    }

    // Get onboarding data from storage
    const onboardingData = await getOnboardingData();

    // Validate required fields
    const requiredFields = [
      'businessName', 'email', 'memberName', 'title', 'phone',
      'businessCategory', 'businessSlug', 'addressLine', 'pincode',
      'locality', 'city', 'state'
    ];
    
    for (const field of requiredFields) {
      if (!onboardingData[field as keyof OnboardingFormData]) {
        return { success: false, error: `Missing required field: ${field}` };
      }
    }

    // Check if business profile already exists
    const { data: existingProfile } = await supabase
      .from('business_profiles')
      .select('id, business_slug')
      .eq('id', user.id)
      .maybeSingle();

    // If profile exists and has business_slug, onboarding is already complete
    if (existingProfile && existingProfile.business_slug) {
      return { success: false, error: 'Business profile already exists and onboarding is complete' };
    }

    // Validate slug availability one more time
    const slugCheck = await checkSlugAvailability(onboardingData.businessSlug!);
    if (!slugCheck.available) {
      return { success: false, error: slugCheck.error || 'Business slug is not available' };
    }

    // React Native can use precise GPS coordinates, unlike browser-based Next.js
    let finalLatitude = onboardingData.latitude;
    let finalLongitude = onboardingData.longitude;

    // Create business profile and subscription using direct service
    // Map the profile data to the expected BusinessOnboardingData format
    const businessOnboardingData = {
      businessName: onboardingData.businessName!,
      email: onboardingData.email!,
      memberName: onboardingData.memberName!,
      title: onboardingData.title!,
      phone: onboardingData.phone!,
      businessCategory: onboardingData.businessCategory!,
      businessSlug: onboardingData.businessSlug!,
      addressLine: onboardingData.addressLine!,
      pincode: onboardingData.pincode!,
      locality: onboardingData.locality!,
      city: onboardingData.city!,
      state: onboardingData.state!,
      businessStatus: onboardingData.businessStatus!,
      latitude: finalLatitude,
      longitude: finalLongitude,
      planId,
      isTrial: planId !== "free", // Paid plans start as trial, free plan is active
    };

    const onboardingResult = await completeOnboarding(businessOnboardingData);

    if (!onboardingResult.success) {
      console.error('Business onboarding failed:', onboardingResult.error);
      return { success: false, error: onboardingResult.error || 'Failed to complete business onboarding' };
    }

    // Clear onboarding data after successful completion
    await clearOnboardingData();

    return { 
      success: true, 
      data: { 
        businessSlug: onboardingData.businessSlug,
        planId 
      } 
    };

  } catch (error) {
    console.error('Error completing business onboarding:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get existing business profile data for pre-filling forms
 */
export async function getExistingBusinessData(): Promise<Partial<OnboardingFormData> | null> {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return null;
    }

    const { data: profile, error } = await supabase
      .from('business_profiles')
      .select(`
        business_name,
        contact_email,
        member_name,
        title,
        phone,
        business_category,
        business_slug,
        address_line,
        pincode,
        city,
        state,
        locality,
        status
      `)
      .eq('id', user.id)
      .maybeSingle();

    if (error || !profile) {
      return null;
    }

    return {
      businessName: profile.business_name,
      email: profile.contact_email,
      memberName: profile.member_name,
      title: profile.title,
      phone: profile.phone,
      businessCategory: profile.business_category,
      businessSlug: profile.business_slug,
      addressLine: profile.address_line,
      pincode: profile.pincode,
      city: profile.city,
      state: profile.state,
      locality: profile.locality,
      businessStatus: profile.status === 'online' ? 'online' : 'offline',
    };
  } catch (error) {
    console.error('Error getting existing business data:', error);
    return null;
  }
}
