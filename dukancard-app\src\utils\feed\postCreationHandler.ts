import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';

/**
 * Post Creation Handler - Manages immediate post visibility after creation
 * 
 * Behavior:
 * 1. When user creates a post -> Show at top immediately (instant feedback)
 * 2. When user refreshes -> Apply normal algorithm (proper positioning)
 * 
 * This provides excellent UX while maintaining algorithmic integrity
 */

export interface PostCreationState {
  justCreatedPostId?: string;
  sessionId?: string;
  createdAt?: string;
}

export interface FeedWithCreationState {
  posts: UnifiedPost[];
  hasJustCreatedPost: boolean;
  justCreatedPost?: UnifiedPost;
}

/**
 * Handle feed display when user just created a post
 * Shows new post at top for immediate feedback
 */
export function handlePostCreationFeed(
  algorithmicPosts: UnifiedPost[],
  creationState: PostCreationState
): FeedWithCreationState {
  
  if (!creationState.justCreatedPostId) {
    // No recent post creation, return normal algorithmic feed
    return {
      posts: algorithmicPosts,
      hasJustCreatedPost: false
    };
  }

  // Find the just-created post in the algorithmic results
  const justCreatedPost = algorithmicPosts.find(
    post => post.id === creationState.justCreatedPostId
  );

  if (!justCreatedPost) {
    // Post not found in current page, return normal feed
    // (Post might be on a different page due to algorithm)
    return {
      posts: algorithmicPosts,
      hasJustCreatedPost: false
    };
  }

  // Remove the post from its algorithmic position
  const otherPosts = algorithmicPosts.filter(
    post => post.id !== creationState.justCreatedPostId
  );

  // Show just-created post at the top
  return {
    posts: [justCreatedPost, ...otherPosts],
    hasJustCreatedPost: true,
    justCreatedPost
  };
}

/**
 * Create post creation state after successful post creation
 */
export function createPostCreationState(
  postId: string,
  sessionId?: string
): PostCreationState {
  return {
    justCreatedPostId: postId,
    sessionId: sessionId || generateSessionId(),
    createdAt: new Date().toISOString()
  };
}

/**
 * Check if post creation state is still valid (within session)
 */
export function isPostCreationStateValid(
  creationState: PostCreationState,
  currentSessionId?: string
): boolean {
  if (!creationState.justCreatedPostId) return false;
  
  // Check if it's the same session
  if (creationState.sessionId && currentSessionId) {
    return creationState.sessionId === currentSessionId;
  }

  // Check if creation was recent (within last 5 minutes as fallback)
  if (creationState.createdAt) {
    const createdTime = new Date(creationState.createdAt).getTime();
    const now = new Date().getTime();
    const fiveMinutes = 5 * 60 * 1000;
    
    return (now - createdTime) < fiveMinutes;
  }

  return false;
}

/**
 * Clear post creation state (call on refresh or navigation)
 */
export function clearPostCreationState(): PostCreationState {
  return {};
}

/**
 * Generate a simple session ID for tracking
 */
function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Enhanced feed response that includes creation state information
 */
export interface EnhancedFeedResponse {
  success: boolean;
  message: string;
  error?: string;
  data?: {
    items: UnifiedPost[];
    totalCount: number;
    hasMore: boolean;
    hasJustCreatedPost: boolean;
    justCreatedPost?: UnifiedPost;
    creationState?: PostCreationState;
  };
}

/**
 * Process feed with post creation handling
 */
export function processFeedWithCreationHandling(
  algorithmicPosts: UnifiedPost[],
  totalCount: number,
  hasMore: boolean,
  creationState?: PostCreationState
): EnhancedFeedResponse {

  if (!creationState || !creationState.justCreatedPostId) {
    // No post creation state, return normal feed
    return {
      success: true,
      message: 'Posts fetched successfully',
      data: {
        items: algorithmicPosts,
        totalCount,
        hasMore,
        hasJustCreatedPost: false
      }
    };
  }

  // Handle post creation display
  const feedWithCreation = handlePostCreationFeed(algorithmicPosts, creationState);

  return {
    success: true,
    message: 'Posts fetched successfully',
    data: {
      items: feedWithCreation.posts,
      totalCount,
      hasMore,
      hasJustCreatedPost: feedWithCreation.hasJustCreatedPost,
      justCreatedPost: feedWithCreation.justCreatedPost,
      creationState
    }
  };
}

/**
 * Client-side helper to manage post creation state in localStorage/sessionStorage
 */
export const PostCreationStateManager = {
  
  /**
   * Save post creation state to session storage
   */
  save(state: PostCreationState): void {
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('post_creation_state', JSON.stringify(state));
    }
  },

  /**
   * Load post creation state from session storage
   */
  load(): PostCreationState {
    if (typeof window !== 'undefined') {
      const stored = sessionStorage.getItem('post_creation_state');
      if (stored) {
        try {
          return JSON.parse(stored);
        } catch (e) {
          console.warn('Failed to parse post creation state:', e);
        }
      }
    }
    return {};
  },

  /**
   * Clear post creation state from session storage
   */
  clear(): void {
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem('post_creation_state');
    }
  },

  /**
   * Check if current state is valid and clear if not
   */
  validateAndClean(): PostCreationState {
    const state = this.load();
    const currentSessionId = this.getCurrentSessionId();
    
    if (!isPostCreationStateValid(state, currentSessionId)) {
      this.clear();
      return {};
    }
    
    return state;
  },

  /**
   * Get or create current session ID
   */
  getCurrentSessionId(): string {
    if (typeof window !== 'undefined') {
      let sessionId = sessionStorage.getItem('current_session_id');
      if (!sessionId) {
        sessionId = generateSessionId();
        sessionStorage.setItem('current_session_id', sessionId);
      }
      return sessionId;
    }
    return generateSessionId();
  }
};

/**
 * Hook-like function for React components to manage post creation state
 */
export function usePostCreationState() {
  const load = () => PostCreationStateManager.validateAndClean();
  const save = (state: PostCreationState) => PostCreationStateManager.save(state);
  const clear = () => PostCreationStateManager.clear();
  
  return { load, save, clear };
}

/**
 * Utility to mark a post as just created (call after successful post creation)
 */
export function markPostAsJustCreated(postId: string): void {
  const state = createPostCreationState(
    postId, 
    PostCreationStateManager.getCurrentSessionId()
  );
  PostCreationStateManager.save(state);
}

/**
 * Utility to check if we should show the "just posted" indicator
 */
export function shouldShowJustPostedIndicator(
  post: UnifiedPost, 
  creationState?: PostCreationState
): boolean {
  if (!creationState || !creationState.justCreatedPostId) return false;
  return post.id === creationState.justCreatedPostId;
}
