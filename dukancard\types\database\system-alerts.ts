export interface SystemAlertsRow {
  id: string;
  alert_type: string;
  severity: string;
  message: string;
  entity_id: string | null;
  subscription_id: string | null;
  metadata: any | null;
  resolved: boolean | null;
  resolved_at: string | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface SystemAlertsInsert {
  id?: string;
  alert_type: string;
  severity: string;
  message: string;
  entity_id?: string | null;
  subscription_id?: string | null;
  metadata?: any | null;
  resolved?: boolean | null;
  resolved_at?: string | null;
  created_at?: string | null;
  updated_at?: string | null;
}

export interface SystemAlertsUpdate {
  id?: string;
  alert_type?: string;
  severity?: string;
  message?: string;
  entity_id?: string | null;
  subscription_id?: string | null;
  metadata?: any | null;
  resolved?: boolean | null;
  resolved_at?: string | null;
  created_at?: string | null;
  updated_at?: string | null;
}

export type SystemAlerts = SystemAlertsRow;
