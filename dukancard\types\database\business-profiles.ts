export interface BusinessProfilesRow {
  id: string;
  business_name: string;
  contact_email: string | null;
  has_active_subscription: boolean;
  trial_end_date: string | null;
  created_at: string;
  updated_at: string;
  logo_url: string | null;
  member_name: string | null;
  phone: string | null;
  instagram_url: string | null;
  whatsapp_number: string | null;
  status: string;
  title: string | null;
  address_line: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  locality: string | null;
  about_bio: string | null;
  facebook_url: string | null;
  average_rating: number | null;
  total_likes: number;
  total_subscriptions: number;
  theme_color: string | null;
  business_hours: any | null;
  delivery_info: string | null;
  total_visits: number;
  today_visits: number;
  yesterday_visits: number;
  visits_7_days: number;
  visits_30_days: number;
  business_category: string | null;
  business_slug: string | null;
  gallery: any | null;
  city_slug: string | null;
  state_slug: string | null;
  locality_slug: string | null;
  custom_branding: any | null;
  custom_ads: any | null;
  established_year: number | null;
  latitude: number | null;
  longitude: number | null;
  google_maps_url: string | null;
}

export interface BusinessProfilesInsert {
  id?: string;
  business_name: string;
  contact_email?: string | null;
  has_active_subscription?: boolean;
  trial_end_date?: string | null;
  created_at?: string;
  updated_at?: string;
  logo_url?: string | null;
  member_name?: string | null;
  phone?: string | null;
  instagram_url?: string | null;
  whatsapp_number?: string | null;
  status?: string;
  title?: string | null;
  address_line?: string | null;
  city?: string | null;
  state?: string | null;
  pincode?: string | null;
  locality?: string | null;
  about_bio?: string | null;
  facebook_url?: string | null;
  average_rating?: number | null;
  total_likes?: number;
  total_subscriptions?: number;
  theme_color?: string | null;
  business_hours?: any | null;
  delivery_info?: string | null;
  total_visits?: number;
  today_visits?: number;
  yesterday_visits?: number;
  visits_7_days?: number;
  visits_30_days?: number;
  business_category?: string | null;
  business_slug?: string | null;
  gallery?: any | null;
  city_slug?: string | null;
  state_slug?: string | null;
  locality_slug?: string | null;
  custom_branding?: any | null;
  custom_ads?: any | null;
  established_year?: number | null;
  latitude?: number | null;
  longitude?: number | null;
  google_maps_url?: string | null;
}

export interface BusinessProfilesUpdate {
  id?: string;
  business_name?: string;
  contact_email?: string | null;
  has_active_subscription?: boolean;
  trial_end_date?: string | null;
  created_at?: string;
  updated_at?: string;
  logo_url?: string | null;
  member_name?: string | null;
  phone?: string | null;
  instagram_url?: string | null;
  whatsapp_number?: string | null;
  status?: string;
  title?: string | null;
  address_line?: string | null;
  city?: string | null;
  state?: string | null;
  pincode?: string | null;
  locality?: string | null;
  about_bio?: string | null;
  facebook_url?: string | null;
  average_rating?: number | null;
  total_likes?: number;
  total_subscriptions?: number;
  theme_color?: string | null;
  business_hours?: any | null;
  delivery_info?: string | null;
  total_visits?: number;
  today_visits?: number;
  yesterday_visits?: number;
  visits_7_days?: number;
  visits_30_days?: number;
  business_category?: string | null;
  business_slug?: string | null;
  gallery?: any | null;
  city_slug?: string | null;
  state_slug?: string | null;
  locality_slug?: string | null;
  custom_branding?: any | null;
  custom_ads?: any | null;
  established_year?: number | null;
  latitude?: number | null;
  longitude?: number | null;
  google_maps_url?: string | null;
}

export type BusinessProfiles = BusinessProfilesRow;
