/**
 * Client-side image compression for React Native
 * Adapted from Next.js web version for mobile use
 */

import * as ImageManipulator from "expo-image-manipulator";

export interface CompressionOptions {
  targetSizeKB?: number;
  maxDimension?: number;
  quality?: number;
  format?: "webp" | "jpeg" | "png";
}

export interface CompressionResult {
  uri?: string;
  blob?: Blob;
  finalSizeKB: number;
  compressionRatio: number;
  dimensions: { width: number; height: number };
}

/**
 * Get file size from URI in React Native
 */
async function getFileSize(uri: string): Promise<number> {
  try {
    const response = await fetch(uri);
    const blob = await response.blob();
    return blob.size;
  } catch (error) {
    console.error("Error getting file size:", error);
    return 0;
  }
}

/**
 * Compress image using expo-image-manipulator
 */
/**
 * Sharp-like compression logic adapted for React Native using expo-image-manipulator
 * Mimics the progressive compression approach from Next.js
 * FIXED: Now preserves aspect ratio to prevent image squeezing
 */
export async function compressImageClientSide(
  imageUri: string,
  originalSize: number,
  options: CompressionOptions = {}
): Promise<CompressionResult> {
  const {
    format = "webp",
    targetSizeKB = 50, // Default to 50KB for avatars
    maxDimension = 400,
    quality: initialQuality = 0.7,
  } = options;

  try {
    // Convert format to expo-image-manipulator format
    let saveFormat: ImageManipulator.SaveFormat;
    switch (format) {
      case "webp":
        saveFormat = ImageManipulator.SaveFormat.WEBP;
        break;
      case "png":
        saveFormat = ImageManipulator.SaveFormat.PNG;
        break;
      case "jpeg":
      default:
        saveFormat = ImageManipulator.SaveFormat.JPEG;
        break;
    }

    // Get original image dimensions once at the beginning
    // Use the deprecated API for now to get dimensions, but we'll update the main manipulation
    // @ts-ignore - Using deprecated API until new API is available in this version
    const originalImage = await ImageManipulator.manipulateAsync(
      imageUri,
      [],
      {}
    );
    const originalWidth = originalImage.width || 1;
    const originalHeight = originalImage.height || 1;

    let quality = initialQuality;
    let currentMaxDimension = maxDimension;
    let attempts = 0;
    const maxAttempts = 5;
    let currentUri = imageUri;
    let currentSize = originalSize;
    let finalResult: any = null;

    // Progressive compression approach similar to Sharp logic in Next.js
    while (attempts < maxAttempts) {
      // Calculate scaling factor to maintain aspect ratio
      const scaleFactor = Math.min(
        currentMaxDimension / originalWidth,
        currentMaxDimension / originalHeight,
        1 // Never scale up
      );

      // Calculate new dimensions maintaining aspect ratio
      const newWidth = Math.round(originalWidth * scaleFactor);
      const newHeight = Math.round(originalHeight * scaleFactor);

      // Apply resize and compression with proper aspect ratio
      // Note: Using deprecated API until new API is properly available in this version
      // @ts-ignore - Using deprecated API until new API is available in this version
      const result = await ImageManipulator.manipulateAsync(
        imageUri, // Always start from original for best quality
        [
          {
            resize: { width: newWidth, height: newHeight },
          },
        ],
        {
          format: saveFormat,
          compress: quality,
        }
      );

      currentUri = result.uri;
      currentSize = await getFileSize(currentUri);
      const currentSizeKB = currentSize / 1024;
      finalResult = result;

      // Check if we've reached our target
      if (currentSizeKB <= targetSizeKB || quality <= 0.1) {
        const compressionRatio = originalSize / currentSize;
        return {
          uri: currentUri,
          finalSizeKB: Math.round(currentSizeKB * 100) / 100,
          compressionRatio: Math.round(compressionRatio * 100) / 100,
          dimensions: { width: result.width || 0, height: result.height || 0 },
        };
      }

      // Adjust parameters for next attempt (similar to Sharp logic)
      attempts++;

      if (attempts === 1) {
        // First retry: reduce quality significantly
        quality = Math.max(0.5, quality - 0.2);
      } else if (attempts === 2) {
        // Second retry: reduce both quality and dimension
        quality = Math.max(0.3, quality - 0.2);
        currentMaxDimension = Math.max(350, currentMaxDimension - 50);
      } else if (attempts === 3) {
        // Third retry: more aggressive reduction
        quality = Math.max(0.2, quality - 0.1);
        currentMaxDimension = Math.max(300, currentMaxDimension - 50);
      } else {
        // Final retry: very aggressive
        quality = 0.1;
        currentMaxDimension = 250;
      }
    }

    // If we still haven't reached target, return the last result
    const compressionRatio = originalSize / currentSize;
    return {
      uri: currentUri,
      finalSizeKB: Math.round((currentSize / 1024) * 100) / 100,
      compressionRatio: Math.round(compressionRatio * 100) / 100,
      dimensions: {
        width: finalResult?.width || 0,
        height: finalResult?.height || 0,
      },
    };
  } catch (error) {
    console.error("Image compression failed:", error);
    throw new Error("Failed to compress image");
  }
}

/**
 * Ultra-aggressive client-side compression for React Native
 */
export async function compressImageUltraAggressiveClient(
  imageUri: string,
  originalSize: number,
  options: CompressionOptions = {}
): Promise<CompressionResult> {
  const originalSizeMB = originalSize / (1024 * 1024);

  // Auto-determine settings based on file size
  let targetSizeKB = 100;
  let maxDimension = 800;
  let quality = 0.7;

  if (originalSizeMB <= 2) {
    quality = 0.7;
    maxDimension = 800;
    targetSizeKB = 90;
  } else if (originalSizeMB <= 5) {
    quality = 0.55;
    maxDimension = 700;
    targetSizeKB = 80;
  } else if (originalSizeMB <= 10) {
    quality = 0.45;
    maxDimension = 600;
    targetSizeKB = 70;
  } else {
    quality = 0.35;
    maxDimension = 550;
    targetSizeKB = 60;
  }

  return compressImageClientSide(imageUri, originalSize, {
    ...options,
    targetSizeKB: options.targetSizeKB || targetSizeKB,
    maxDimension: options.maxDimension || maxDimension,
    quality: options.quality || quality,
  });
}

/**
 * Moderate client-side compression for React Native
 * Default targets 50KB for avatars, can be overridden via options
 */
export async function compressImageModerateClient(
  imageUri: string,
  originalSize: number,
  options: CompressionOptions = {}
): Promise<CompressionResult> {
  return compressImageClientSide(imageUri, originalSize, {
    targetSizeKB: 50, // Default to 50KB for avatars
    maxDimension: 400, // Smaller default for avatars
    quality: 0.7, // More aggressive default quality
    ...options,
  });
}

/**
 * Ultra-aggressive compression for File objects (Next.js compatibility)
 */
export async function compressFileUltraAggressive(
  file: File,
  options: CompressionOptions = {}
): Promise<CompressionResult> {
  try {
    // Convert File to blob URL for processing
    const blobUrl = URL.createObjectURL(file);

    // Get original size
    const originalSize = file.size;

    // Use the existing compression function
    const result = await compressImageUltraAggressiveClient(
      blobUrl,
      originalSize,
      options
    );

    // Convert result URI back to blob for File compatibility
    if (!result.uri) {
      throw new Error("Compression result does not contain a valid URI");
    }
    const response = await fetch(result.uri);
    const blob = await response.blob();

    // Clean up blob URL
    URL.revokeObjectURL(blobUrl);

    return {
      ...result,
      blob,
      uri: undefined, // Remove URI since we're returning blob
    };
  } catch (error) {
    console.error("File compression failed:", error);
    throw new Error("Failed to compress file");
  }
}
