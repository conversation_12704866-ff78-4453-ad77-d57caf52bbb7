/**
 * Main Discovery Screen for React Native
 * Based on dukancard/app/(main)/discover/ModernDiscoverClient.tsx
 */

import React, { useRef, useCallback } from "react";
import {
  View,
  FlatList,
  Text,
  ActivityIndicator,
  TouchableOpacity,
} from "react-native";
import { SearchX } from "lucide-react-native";
import {
  DiscoverProvider, // Changed from DiscoveryProvider
  useDiscoverContext, // Changed from useDiscovery
} from "@/src/contexts/DiscoveryContext";
import { useTheme } from "@/src/hooks/useTheme";

// Import discovery components
import { CompactLocationPicker } from "@/src/components/discovery/CompactLocationPicker";
import { SearchSection } from "@/src/components/discovery/SearchSection";
import { ViewToggle } from "@/src/components/discovery/ViewToggle";
import { ErrorSection } from "@/src/components/discovery/ErrorComponents";
import { BusinessCard } from "@/src/components/discovery/BusinessCard";
import { ProductCard } from "@/src/components/shared/ui/ProductCard";
import { useDiscoveryNavigation } from "@/src/components/discovery/NavigationHandlers";
import { ErrorBoundary } from "@/src/components/ErrorBoundary";
import { createDiscoverScreenStyles } from "@/src/components/discovery/styles/DiscoverScreenStyles";

// Sort-related imports
import { getSortDisplayName } from "@/src/utils/sortMappings";
import SortBottomSheet, {
  SortBottomSheetRef,
} from "@/src/components/discovery/SortBottomSheet";

// Category picker imports
import { CategorySelector } from "@/src/components/discovery/CategorySelector";
import CategoryBottomSheetPicker, {
  CategoryBottomSheetPickerRef,
} from "@/src/components/pickers/CategoryBottomSheetPicker";

// Skeleton imports
import { BusinessCardSkeleton } from "@/src/components/discovery/DiscoverySkeletons";
import { ProductCardSkeleton } from "@/src/components/ui/ProductSkeleton";

// Utility imports
import { formatIndianNumberShort } from "@/lib/utils";

export default function DiscoverScreen() {
  return (
    <ErrorBoundary>
      <DiscoverProvider>
        <DiscoveryContent />
      </DiscoverProvider>
    </ErrorBoundary>
  );
}

/**
 * Main discovery content component
 * Separated to use discovery context hooks
 */
function DiscoveryContent() {
  const { colors } = useTheme();
  const styles = createDiscoverScreenStyles(colors);

  // Refs for bottom sheets
  const sortBottomSheetRef = useRef<SortBottomSheetRef>(null);
  const categoryPickerRef = useRef<CategoryBottomSheetPickerRef>(null);

  const {
    viewType,
    handleViewChange,
    isSearching,
    isSorting,
    isFilteringByCategory,
    searchTerm,
    handleSearch,
    handleClearSearch,
    searchError,
    // Data
    businesses,
    products,
    // Pagination
    isLoadingMore,
    hasMore,
    loadMore,
    totalCount,
    userLocation,
    updateLocation,
    isLocationLoading,
    refresh,
    // Sort-related context values
    productSortBy,
    sortBy,
    handleProductSortChange,
    handleBusinessSortChange,
    // Category-related context values
    selectedCategory,
    handleCategoryChange,
  } = useDiscoverContext();

  const { navigateToBusinessProfile } = useDiscoveryNavigation();

  // Handlers
  const handleSortPress = useCallback(() => {
    sortBottomSheetRef.current?.present();
  }, []);

  const handleCategoryPress = useCallback(() => {
    categoryPickerRef.current?.present();
  }, []);

  // Prepare data for FlatList - combine all items into single scrollable list
  const flatListData = React.useMemo(() => {
    const currentData = viewType === "cards" ? businesses : products;

    // For products, we need to handle 2-column layout by pairing items
    if (viewType === "products") {
      const pairedProducts = [];
      for (let i = 0; i < currentData.length; i += 2) {
        pairedProducts.push({
          type: "product-pair",
          left: currentData[i],
          right: currentData[i + 1] || null,
          id: `pair-${i}`,
        });
      }
      return pairedProducts;
    } else {
      // For business cards, each item is individual
      return currentData.map((item, index) => ({
        type: "business",
        data: item,
        id: item.id || `business-${index}`,
      }));
    }
  }, [viewType, businesses, products]);

  // Render header component
  const renderHeader = useCallback(
    () => (
      <View>
        {/* Location Selection */}
        <CompactLocationPicker
          location={userLocation || undefined}
          onLocationChange={updateLocation}
          onError={(error: string) => console.error("Location error:", error)}
          disabled={isLocationLoading}
          isLoading={isLocationLoading}
          colors={colors}
        />

        {/* View Toggle */}
        <ViewToggle
          viewType={viewType}
          onViewChange={handleViewChange}
          disabled={isSearching}
        />

        {/* Category Selector - Above search */}
        <CategorySelector
          selectedCategory={selectedCategory}
          onCategoryPress={handleCategoryPress}
          onCategoryClear={() => handleCategoryChange(null)}
          disabled={isSearching}
        />

        {/* Search Section */}
        <SearchSection
          searchTerm={searchTerm}
          onSearch={handleSearch}
          viewType={viewType}
          onSortPress={handleSortPress}
        />

        {/* Clear Search Button - Show when there's a search term */}
        {searchTerm.length > 0 && (
          <View style={styles.clearSearchContainer}>
            <TouchableOpacity
              onPress={handleClearSearch}
              style={styles.clearSearchButton}
            >
              <Text style={[styles.clearSearchText, { color: colors.primary }]}>
                Clear Search
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Error Display */}
        {searchError && <ErrorSection error={searchError} onRetry={refresh} />}

        {/* Results Header - Always show */}
        <ResultsHeader
          viewType={viewType}
          totalCount={totalCount}
          sortBy={sortBy}
          productSortBy={productSortBy}
          colors={colors}
          isLoading={
            isSearching ||
            isSorting ||
            isFilteringByCategory ||
            isLocationLoading
          }
        />
      </View>
    ),
    [
      userLocation,
      updateLocation,
      isLocationLoading,
      colors,
      viewType,
      handleViewChange,
      isSearching,
      isSorting,
      isFilteringByCategory,
      selectedCategory,
      handleCategoryPress,
      handleCategoryChange,
      searchTerm,
      handleSearch,
      handleClearSearch,
      handleSortPress,
      searchError,
      refresh,
      totalCount,
      sortBy,
      productSortBy,
      styles.clearSearchButton,
      styles.clearSearchContainer,
      styles.clearSearchText,
    ]
  );

  // Render individual items
  const renderItem = useCallback(
    ({ item }: { item: any }) => {
      // Validate item structure
      if (!item || !item.type) {
        return null;
      }

      if (item.type === "business") {
        // Validate business data
        if (!item.data) {
          return null;
        }
        return (
          <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
            <BusinessCard
              business={item.data}
              onPress={() => navigateToBusinessProfile(item.data)}
            />
          </View>
        );
      } else if (item.type === "product-pair") {
        // Validate product data
        if (!item.left) {
          return null;
        }
        return (
          <View
            style={{
              flexDirection: "row",
              paddingHorizontal: 16,
              marginBottom: 16,
              justifyContent: "space-between",
            }}
          >
            {/* Left product */}
            <View style={{ width: "48%" }}>
              <ProductCard
                product={{
                  id: item.left.id,
                  slug: item.left.slug,
                  business_id: item.left.business_id,
                  product_type: item.left.product_type,
                  name: item.left.name || "",
                  description: item.left.description,
                  base_price: item.left.base_price,
                  discounted_price: item.left.discounted_price || null,
                  image_url: item.left.image_url || null,
                  is_available: item.left.is_available,
                  created_at: item.left.created_at,
                  updated_at: item.left.updated_at,
                  images: null,
                  featured_image_index: null,
                }}
                businessLatitude={item.left.businessLatitude}
                businessLongitude={item.left.businessLongitude}
                showDistance={true}
                isClickable={true}
              />
            </View>

            {/* Right product (if exists) */}
            {item.right && (
              <View style={{ width: "48%" }}>
                <ProductCard
                  product={{
                    id: item.right.id,
                    slug: item.right.slug,
                    business_id: item.right.business_id,
                    product_type: item.right.product_type,
                    name: item.right.name || "",
                    description: item.right.description,
                    base_price: item.right.base_price,
                    discounted_price: item.right.discounted_price || null,
                    image_url: item.right.image_url || null,
                    is_available: item.right.is_available,
                    created_at: item.right.created_at,
                    updated_at: item.right.updated_at,
                    images: null,
                    featured_image_index: null,
                  }}
                  businessLatitude={item.right.businessLatitude}
                  businessLongitude={item.right.businessLongitude}
                  showDistance={true}
                  isClickable={true}
                />
              </View>
            )}
          </View>
        );
      }
      return null;
    },
    [navigateToBusinessProfile]
  );

  // Key extractor
  const keyExtractor = useCallback(
    (item: any) => item.id || `item-${Math.random()}`,
    []
  );

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (
      hasMore &&
      !isLoadingMore &&
      !isSearching &&
      !isSorting &&
      !isFilteringByCategory &&
      !isLocationLoading
    ) {
      loadMore();
    }
  }, [
    hasMore,
    isLoadingMore,
    isSearching,
    isSorting,
    isFilteringByCategory,
    isLocationLoading,
    loadMore,
  ]);

  // Render empty state
  const renderEmptyState = useCallback(() => {
    if (
      isSearching ||
      isSorting ||
      isFilteringByCategory ||
      isLocationLoading
    ) {
      return (
        <View style={{ paddingHorizontal: 16 }}>
          {viewType === "cards" ? (
            <View>
              {Array.from({ length: 3 }).map((_, index) => (
                <View key={index} style={{ marginBottom: 16 }}>
                  <BusinessCardSkeleton />
                </View>
              ))}
            </View>
          ) : (
            <View>
              {Array.from({ length: 3 }).map((_, index) => (
                <View
                  key={index}
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    marginBottom: 16,
                  }}
                >
                  <View style={{ width: "48%" }}>
                    <ProductCardSkeleton />
                  </View>
                  <View style={{ width: "48%" }}>
                    <ProductCardSkeleton />
                  </View>
                </View>
              ))}
            </View>
          )}
        </View>
      );
    }

    return <NoResultsFound viewType={viewType} colors={colors} />;
  }, [
    isSearching,
    isSorting,
    isFilteringByCategory,
    isLocationLoading,
    viewType,
    colors,
  ]);

  // Render footer
  const renderFooter = useCallback(() => {
    if (!isLoadingMore) return null;

    return (
      <View style={{ paddingHorizontal: 16, paddingVertical: 20 }}>
        {viewType === "cards" ? (
          <BusinessCardSkeleton />
        ) : (
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              marginBottom: 16,
            }}
          >
            <View style={{ width: "48%" }}>
              <ProductCardSkeleton />
            </View>
            <View style={{ width: "48%" }}>
              <ProductCardSkeleton />
            </View>
          </View>
        )}
      </View>
    );
  }, [isLoadingMore, viewType]);

  return (
    <View style={{ flex: 1 }}>
      <FlatList
        data={flatListData}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyState}
        ListFooterComponent={renderFooter}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        contentContainerStyle={[
          styles.contentContainer,
          {
            paddingBottom: 120, // Extra bottom padding for bottom navigation
          },
        ]}
      />

      {/* Category Bottom Sheet Picker */}
      <CategoryBottomSheetPicker
        ref={categoryPickerRef}
        selectedCategory={selectedCategory || undefined}
        onCategorySelect={handleCategoryChange}
      />

      {/* Sort Bottom Sheet */}
      <SortBottomSheet
        ref={sortBottomSheetRef}
        viewType={viewType}
        productSortBy={productSortBy}
        businessSortBy={sortBy}
        onProductSortSelect={handleProductSortChange}
        onBusinessSortSelect={handleBusinessSortChange}
      />
    </View>
  );
}

/**
 * Results Header Component - Always visible
 */
function ResultsHeader({
  viewType,
  totalCount,
  sortBy,
  productSortBy,
  colors,
  isLoading = false,
}: {
  viewType: "cards" | "products";
  totalCount: number;
  sortBy: string;
  productSortBy: string;
  colors: any;
  isLoading?: boolean;
}) {
  // Get sort display name with fallback
  const sortDisplayName = getSortDisplayName(
    viewType || "products",
    (productSortBy || "newest") as any,
    (sortBy || "created_desc") as any
  );

  // Ensure we have valid values
  const safeViewType = viewType || "products";
  const safeTotalCount = totalCount || 0;
  const safeSortDisplayName = sortDisplayName || "Newest First";

  return (
    <View
      style={{
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        paddingVertical: 16,
        paddingHorizontal: 16,
        borderBottomWidth: 1,
        borderBottomColor: colors.border,
        marginBottom: 16,
      }}
    >
      <View style={{ flexDirection: "row", alignItems: "center" }}>
        {isLoading ? (
          <ActivityIndicator
            size="small"
            color={colors.primary}
            style={{ marginRight: 8 }}
          />
        ) : null}
        <Text
          style={{
            fontSize: 16,
            fontWeight: "600",
            color: colors.textPrimary,
          }}
        >
          {isLoading
            ? "Loading..."
            : `${formatIndianNumberShort(safeTotalCount)} ${
                safeViewType === "products"
                  ? "Products & Services"
                  : "Businesses"
              }`}
        </Text>
      </View>
      <Text
        style={{
          fontSize: 14,
          color: colors.textSecondary,
        }}
      >
        Sorted by: {safeSortDisplayName}
      </Text>
    </View>
  );
}

/**
 * No Results Found Component - Centered in available area
 */
function NoResultsFound({
  viewType,
  colors,
}: {
  viewType: "cards" | "products";
  colors: any;
}) {
  const safeViewType = viewType || "products";

  return (
    <View
      style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        paddingVertical: 80,
        paddingHorizontal: 20,
        minHeight: 300, // Ensure minimum height for proper centering
      }}
    >
      <SearchX
        size={64}
        color={colors?.iconMuted || "#999"}
        style={{ marginBottom: 24 }}
      />
      <Text
        style={{
          color: colors?.textPrimary || "#000",
          fontSize: 18,
          fontWeight: "600",
          textAlign: "center",
          marginBottom: 8,
        }}
      >
        No {safeViewType === "products" ? "Products" : "Business Cards"} Found
      </Text>
      <Text
        style={{
          color: colors?.textSecondary || "#666",
          fontSize: 14,
          textAlign: "center",
          lineHeight: 20,
        }}
      >
        {safeViewType === "products"
          ? "Try adjusting your search criteria or explore different categories to find products."
          : "Try adjusting your search criteria or explore different categories to find businesses."}
      </Text>
    </View>
  );
}
