/**
 * LikeCard Component for React Native
 * Displays individual liked business with unlike functionality
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface BusinessProfileData {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  address_line: string | null;
}

interface LikeWithProfile {
  id: string;
  business_profiles: BusinessProfileData | null;
}

interface LikeCardProps {
  like: LikeWithProfile;
  onUnlike: (likeId: string) => Promise<void>;
}

export const LikeCard: React.FC<LikeCardProps> = ({
  like,
  onUnlike,
}) => {
  const [isUnliking, setIsUnliking] = useState(false);
  const business = like.business_profiles;

  if (!business) {
    return null;
  }

  const handleUnlike = () => {
    Alert.alert(
      'Unlike Business',
      `Are you sure you want to remove ${business.business_name} from your liked businesses?`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Unlike',
          style: 'destructive',
          onPress: async () => {
            setIsUnliking(true);
            try {
              await onUnlike(like.id);
            } catch (error) {
              console.error('Error unliking:', error);
            } finally {
              setIsUnliking(false);
            }
          },
        },
      ]
    );
  };

  const handleVisitBusiness = () => {
    // TODO: Navigate to business profile
  };

  const getBusinessInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatAddress = () => {
    const parts = [];
    if (business.city) parts.push(business.city);
    if (business.state) parts.push(business.state);
    if (business.pincode) parts.push(business.pincode);
    return parts.join(', ');
  };

  return (
    <View style={styles.card}>
      <TouchableOpacity
        style={styles.cardContent}
        onPress={handleVisitBusiness}
        activeOpacity={0.7}
      >
        {/* Business Logo/Avatar */}
        <View style={styles.logoContainer}>
          {business.logo_url ? (
            <Image source={{ uri: business.logo_url }} style={styles.logo} />
          ) : (
            <View style={styles.logoPlaceholder}>
              <Text style={styles.logoText}>
                {getBusinessInitials(business.business_name || 'B')}
              </Text>
            </View>
          )}
        </View>

        {/* Business Info */}
        <View style={styles.businessInfo}>
          <Text style={styles.businessName} numberOfLines={1}>
            {business.business_name}
          </Text>
          {formatAddress() && (
            <Text style={styles.businessAddress} numberOfLines={1}>
              {formatAddress()}
            </Text>
          )}
          <View style={styles.statusContainer}>
            <Ionicons name="heart" size={12} color="#ff4444" />
            <Text style={styles.statusText}>Liked</Text>
          </View>
        </View>

        {/* Visit Button */}
        <TouchableOpacity style={styles.visitButton} onPress={handleVisitBusiness}>
          <Ionicons name="arrow-forward" size={16} color="#D4AF37" />
        </TouchableOpacity>
      </TouchableOpacity>

      {/* Unlike Button */}
      <TouchableOpacity
        style={[styles.unlikeButton, isUnliking && styles.unlikeButtonDisabled]}
        onPress={handleUnlike}
        disabled={isUnliking}
      >
        {isUnliking ? (
          <ActivityIndicator size="small" color="#fff" />
        ) : (
          <>
            <Ionicons name="heart-dislike" size={16} color="#fff" />
            <Text style={styles.unlikeText}>Unlike</Text>
          </>
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    overflow: 'hidden',
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  logoContainer: {
    marginRight: 12,
  },
  logo: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  logoPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#D4AF37',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  businessInfo: {
    flex: 1,
    marginRight: 12,
  },
  businessName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  businessAddress: {
    fontSize: 14,
    color: '#666',
    marginBottom: 6,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 12,
    color: '#ff4444',
    fontWeight: '500',
    marginLeft: 4,
  },
  visitButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(212, 175, 55, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  unlikeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ff4444',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  unlikeButtonDisabled: {
    backgroundColor: '#ccc',
  },
  unlikeText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
});
