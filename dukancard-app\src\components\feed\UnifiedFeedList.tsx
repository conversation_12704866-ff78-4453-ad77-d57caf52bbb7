import React, {
  useState,
  useCallback,
  useRef,
  useEffect,
  useMemo,
} from "react";
import {
  View,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  Text,
  Alert,
} from "react-native";
import {
  UnifiedPost,
  getUnifiedFeedPostsWithAuthors,
} from "@/lib/actions/posts/unifiedFeed";
import { FeedFilterType } from "@/lib/types/posts";
import { FeedHeader } from "./FeedHeader";
import { PostCard } from "./PostCard";
import { PostSkeleton } from "./PostSkeleton";
import { CustomerPostCreator } from "./CustomerPostCreator";
import { BusinessPostCreator } from "./BusinessPostCreator";
import { useColorScheme } from "@/src/hooks/useColorScheme";
import { Colors } from "@/src/constants/Colors";

import PostOptionsBottomSheet, {
  PostOptionsBottomSheetRef,
} from "./PostOptionsBottomSheet";
import { sharePost } from "@/backend/supabase/services/posts/postInteractions";
import { deleteCustomerPost } from "@/lib/actions/customerPosts";
import { deleteBusinessPost } from "@/lib/actions/businessPosts";
import { createUnifiedFeedListStyles } from "@/styles/feed/_unified-feed-list-styles";
import { supabase } from "@/lib/supabase";
import { CustomerPostEditModal } from "./CustomerPostEditModal";
import { BusinessPostEditModal } from "./BusinessPostEditModal";

interface UnifiedFeedListProps {
  initialPosts: UnifiedPost[];
  initialHasMore: boolean;
  userName: string;
  userType: "customer" | "business";
  citySlug?: string;
  stateSlug?: string;
  localitySlug?: string;
  pincode?: string;
  onScroll?: (event: any) => void;
  scrollEventThrottle?: number;
}

export function UnifiedFeedList({
  initialPosts,
  initialHasMore,
  userName,
  userType,
  citySlug,
  stateSlug,
  localitySlug,
  pincode,
  onScroll,
  scrollEventThrottle = 16,
}: UnifiedFeedListProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === "dark";

  const [posts, setPosts] = useState<UnifiedPost[]>(initialPosts);
  const [hasMore, setHasMore] = useState<boolean>(initialHasMore);
  const [page, setPage] = useState<number>(1);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [filter, setFilter] = useState<FeedFilterType>("smart");
  const [error, setError] = useState<string | null>(null);

  // Bottom sheet state
  const [selectedPost, setSelectedPost] = useState<UnifiedPost | null>(null);
  const [selectedPostIsOwner, setSelectedPostIsOwner] =
    useState<boolean>(false);

  // Edit modal state
  const [showEditModal, setShowEditModal] = useState<boolean>(false);

  const flatListRef = useRef<FlatList>(null);
  const postOptionsRef = useRef<PostOptionsBottomSheetRef>(null);

  // Theme colors - use pure black/white for feed
  const backgroundColor = isDark ? "#000000" : "#FFFFFF";
  const textColor = isDark ? "#FFFFFF" : "#000000";
  const mutedTextColor = isDark ? "#A1A1AA" : "#71717A";
  const primaryColor = Colors[colorScheme ?? "light"].primary;

  // Get styles from external file
  const styles = createUnifiedFeedListStyles();

  // Load more posts for infinite scroll
  const loadMorePosts = useCallback(async () => {
    if (isLoading || !hasMore) return;

    setIsLoading(true);
    setError(null);

    try {
      const nextPage = page + 1;
      const result = await getUnifiedFeedPostsWithAuthors({
        filter,
        page: nextPage,
        limit: 10,
        city_slug: citySlug,
        state_slug: stateSlug,
        locality_slug: localitySlug,
        pincode: pincode,
      });

      if (result.success && result.data?.items) {
        setPosts((prev) => {
          // Create a Set of existing post IDs for fast lookup
          const existingIds = new Set(prev.map((post) => post.id));
          // Filter out any posts that already exist
          const newPosts = result.data!.items.filter(
            (post) => !existingIds.has(post.id)
          );
          return [...prev, ...newPosts];
        });
        setHasMore(result.data.hasMore || false);
        setPage(nextPage);
      } else {
        setError(result.error || "Failed to load more posts");
      }
    } catch (err) {
      console.error("Error loading more posts:", err);
      setError("Failed to load more posts");
    } finally {
      setIsLoading(false);
    }
  }, [
    isLoading,
    hasMore,
    page,
    filter,
    citySlug,
    stateSlug,
    localitySlug,
    pincode,
  ]);

  // Handle filter change
  const handleFilterChange = useCallback(
    async (newFilter: FeedFilterType) => {
      if (newFilter === filter) return;

      setFilter(newFilter);
      setError(null);

      setIsLoading(true);
      setPosts([]);

      try {
        const result = await getUnifiedFeedPostsWithAuthors({
          filter: newFilter,
          page: 1,
          limit: 10,
          city_slug: citySlug,
          state_slug: stateSlug,
          locality_slug: localitySlug,
          pincode: pincode,
        });

        if (result.success && result.data) {
          setPosts(result.data.items);
          setHasMore(result.data.hasMore);
          setPage(1);
        } else {
          setError(result.error || "Failed to load posts");
        }
      } catch (err) {
        console.error("Error changing filter:", err);
        setError("Failed to load posts");
      } finally {
        setIsLoading(false);
      }
    },
    [filter, citySlug, stateSlug, localitySlug, pincode]
  );

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    setError(null);

    try {
      const result = await getUnifiedFeedPostsWithAuthors({
        filter,
        page: 1,
        limit: 10,
        city_slug: citySlug,
        state_slug: stateSlug,
        locality_slug: localitySlug,
        pincode: pincode,
      });

      if (result.success && result.data?.items) {
        setPosts(result.data.items);
        setHasMore(result.data.hasMore || false);
        setPage(1);
      } else {
        setError(result.error || "Failed to refresh posts");
      }
    } catch (err) {
      console.error("Error refreshing posts:", err);
      setError("Failed to refresh posts");
    } finally {
      setIsRefreshing(false);
    }
  }, [filter, citySlug, stateSlug, localitySlug, pincode]);

  // Handle post creation success
  const handlePostCreated = useCallback(() => {
    handleRefresh();
  }, [handleRefresh]);

  // Handle post update
  const handlePostUpdate = useCallback(
    async (postId: string, newContent: string) => {
      const updatedPosts = posts.map((post) =>
        post.id === postId ? { ...post, content: newContent } : post
      );
      setPosts(updatedPosts);
    },
    [posts]
  );

  // Handle post updated from edit modal
  const handlePostDelete = useCallback(
    async (postId: string) => {
      const filteredPosts = posts.filter((post) => post.id !== postId);
      setPosts(filteredPosts);
    },
    [posts]
  );

  // Handle product update
  const handleProductsUpdate = useCallback(
    async (postId: string, newProductIds: string[]) => {
      const updatedPosts = posts.map((post) =>
        post.id === postId ? { ...post, product_ids: newProductIds } : post
      );
      setPosts(updatedPosts);
    },
    [posts]
  );

  // Handle more press from PostCard
  const handleMorePress = useCallback((post: UnifiedPost, isOwner: boolean) => {
    setSelectedPost(post);
    setSelectedPostIsOwner(isOwner);
    postOptionsRef.current?.present();
  }, []);

  // Handle share post
  const handleSharePost = useCallback(async () => {
    if (!selectedPost) return;

    const result = await sharePost(
      selectedPost.id,
      selectedPost.content,
      selectedPost.author_name || "Unknown",
      selectedPost.business_slug || undefined
    );

    if (!result.success && result.message !== "Share cancelled") {
      Alert.alert("Error", result.message);
    }

    postOptionsRef.current?.dismiss();
  }, [selectedPost]);

  // Handle edit post from bottom sheet
  const handleEditPostFromSheet = useCallback(() => {
    if (!selectedPost) return;
    setShowEditModal(true);
    postOptionsRef.current?.dismiss();
  }, [selectedPost]);

  // Handle post updated from edit modal
  const handlePostUpdated = useCallback(
    (
      postId: string,
      newContent: string,
      newImageUrl?: string | null,
      newProductIds?: string[]
    ) => {
      setShowEditModal(false);

      // Update the post in the list
      const updatedPosts = posts.map((post) => {
        if (post.id === postId) {
          return {
            ...post,
            content: newContent,
            image_url: newImageUrl || post.image_url,
            product_ids: newProductIds || post.product_ids,
          };
        }
        return post;
      });

      setPosts(updatedPosts);
    },
    [posts]
  );

  // Handle delete post from bottom sheet
  const handleDeletePostFromSheet = useCallback(async () => {
    if (!selectedPost) return;

    try {
      let result;
      if (selectedPost.post_source === "customer") {
        result = await deleteCustomerPost(selectedPost.id);
      } else {
        result = await deleteBusinessPost(selectedPost.id);
      }

      if (result.success) {
        handlePostDelete(selectedPost.id);
        Alert.alert("Success", "Post deleted successfully");
      } else {
        Alert.alert("Error", result.error || "Failed to delete post");
      }
    } catch (error) {
      console.error("Error deleting post:", error);
      Alert.alert("Error", "Failed to delete post");
    }

    postOptionsRef.current?.dismiss();
  }, [selectedPost, handlePostDelete]);

  // Get empty state message based on filter
  const getEmptyStateMessage = useCallback(() => {
    switch (filter) {
      case "smart":
        return "No posts available in your smart feed. Try subscribing to businesses or check other filters.";
      case "subscribed":
        return "Subscribe to businesses to see their posts here.";
      case "locality":
        return "No posts from businesses in your locality yet.";
      case "pincode":
        return "No posts from businesses in your pincode yet.";
      case "city":
        return "No posts from businesses in your city yet.";
      case "state":
        return "No posts from businesses in your state yet.";
      case "all":
        return "No posts available at the moment.";
      default:
        return "No posts available at the moment.";
    }
  }, [filter]);

  // Render post item
  const renderPost = useCallback(
    ({ item, index }: { item: UnifiedPost; index: number }) => (
      <PostCard
        post={item}
        index={index}
        onPostUpdate={handlePostUpdate}
        onPostDelete={handlePostDelete}
        onProductsUpdate={handleProductsUpdate}
        onMorePress={handleMorePress}
      />
    ),
    [handlePostUpdate, handlePostDelete, handleProductsUpdate, handleMorePress]
  );

  // Render loading footer
  const renderFooter = useCallback(() => {
    if (!isLoading || !hasMore) return null;

    return (
      <View style={styles.loadingFooter}>
        <ActivityIndicator size="small" color={primaryColor} />
        <Text style={[styles.loadingText, { color: mutedTextColor }]}>
          Loading more posts...
        </Text>
      </View>
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoading, hasMore, primaryColor, mutedTextColor]);

  // Render empty state
  const renderEmpty = useCallback(() => {
    // Show empty state when not loading and no posts
    if (!isLoading && posts.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyTitle, { color: textColor }]}>
            No posts found
          </Text>
          <Text style={[styles.emptySubtitle, { color: mutedTextColor }]}>
            {getEmptyStateMessage()}
          </Text>
        </View>
      );
    }

    return null;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [posts.length, getEmptyStateMessage, textColor, mutedTextColor]);

  // Show error if there's one
  if (error && posts.length === 0) {
    return (
      <View style={[styles.errorContainer, { backgroundColor }]}>
        <Text style={[styles.errorText, { color: primaryColor }]}>
          ⚠️ {error}
        </Text>
        <Text style={[styles.errorSubtext, { color: mutedTextColor }]}>
          Pull down to refresh
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor }]}>
      <FlatList
        ref={flatListRef}
        data={posts}
        renderItem={renderPost}
        keyExtractor={(item) => item.id}
        onEndReached={loadMorePosts}
        onEndReachedThreshold={0.5}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[primaryColor]}
            tintColor={primaryColor}
          />
        }
        ListHeaderComponent={
          <View>
            <FeedHeader
              activeFilter={filter}
              onFilterChange={handleFilterChange}
              isLoading={isLoading}
            />
            {userType === "customer" && (
              <CustomerPostCreator
                customerName={userName}
                onPostCreated={handlePostCreated}
              />
            )}
            {userType === "business" && (
              <BusinessPostCreator
                businessName={userName}
                onPostCreated={handlePostCreated}
              />
            )}
          </View>
        }
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={
          posts.length === 0 ? styles.emptyContentContainer : undefined
        }
        onScroll={onScroll}
        scrollEventThrottle={scrollEventThrottle}
      />

      {/* Post Options Bottom Sheet - Rendered outside FlatList */}
      {selectedPost && (
        <PostOptionsBottomSheet
          ref={postOptionsRef}
          postSource={selectedPost.post_source}
          isOwner={selectedPostIsOwner}
          onEditPost={handleEditPostFromSheet}
          onEditProducts={undefined} // Products can be edited directly in edit post modal
          onDeletePost={handleDeletePostFromSheet}
          onSharePost={handleSharePost}
        />
      )}

      {/* Edit Modals */}
      {selectedPost && selectedPost.post_source === "customer" ? (
        <CustomerPostEditModal
          visible={showEditModal}
          onClose={() => setShowEditModal(false)}
          postId={selectedPost.id}
          initialContent={selectedPost.content}
          initialImageUrl={selectedPost.image_url}
          customerName={selectedPost.author_name || undefined}
          onPostUpdated={handlePostUpdated}
        />
      ) : selectedPost && selectedPost.post_source === "business" ? (
        <BusinessPostEditModal
          visible={showEditModal}
          onClose={() => setShowEditModal(false)}
          postId={selectedPost.id}
          initialContent={selectedPost.content}
          initialImageUrl={selectedPost.image_url}
          initialProductIds={selectedPost.product_ids || []}
          businessName={selectedPost.author_name || undefined}
          onPostUpdated={handlePostUpdated}
        />
      ) : null}
    </View>
  );
}
