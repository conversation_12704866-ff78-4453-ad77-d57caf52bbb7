import { useEffect, useState } from "react";
import { View, Text, Modal, TouchableOpacity } from "react-native";
import { AlertCircle, Mail, Phone, MapPin, CheckCircle } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";

interface ProfileRequirementDialogProps {
  hasCompleteAddress?: boolean;
}

export default function ProfileRequirementDialog({
  hasCompleteAddress = false,
}: ProfileRequirementDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [missingFields, setMissingFields] = useState<string[]>([]);
  const theme = useTheme();
  const { colors, isDark } = theme;

  useEffect(() => {
    // Check if address is missing and show dialog
    if (!hasCompleteAddress) {
      setMissingFields(["address"]);
      setIsOpen(true);
    }
  }, [hasCompleteAddress]);

  const getFieldInfo = (field: string) => {
    switch (field) {
      case "email":
        return {
          icon: <Mail size={20} color="#2563eb" />,
          label: "Email Address",
          description: "Required for account notifications and password reset",
          color: "#2563eb",
          bgColor: isDark ? '#1e3a8a' : '#dbeafe',
        };
      case "phone":
        return {
          icon: <Phone size={20} color="#16a34a" />,
          label: "Mobile Number",
          description: "Required for account access and verification",
          color: "#16a34a",
          bgColor: isDark ? '#14532d' : '#dcfce7',
        };
      case "address":
        return {
          icon: <MapPin size={20} color="#9333ea" />,
          label: "Address Information",
          description: "Required for location-based services",
          color: "#9333ea",
          bgColor: isDark ? '#581c87' : '#f3e8ff',
        };
      default:
        return {
          icon: <AlertCircle size={20} color="#6b7280" />,
          label: field,
          description: "Required information",
          color: "#6b7280",
          bgColor: isDark ? '#374151' : '#f3f4f6',
        };
    }
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  if (missingFields.length === 0) {
    return null;
  }

  return (
    <Modal
      visible={isOpen}
      transparent={true}
      animationType="fade"
      onRequestClose={handleClose}
    >
      <View style={{
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
        padding: theme.spacing.lg,
      }}>
        <View style={{
          backgroundColor: isDark ? '#000' : '#fff',
          borderRadius: 12,
          padding: 24,
          width: '100%',
          maxWidth: 400,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 8,
          elevation: 8,
        }}>
          {/* Header */}
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 12, marginBottom: 16 }}>
            <View style={{
              padding: 8,
              borderRadius: 20,
              backgroundColor: isDark ? '#92400e' : '#fef3c7',
            }}>
              <AlertCircle size={20} color={isDark ? '#f59e0b' : '#d97706'} />
            </View>
            <Text style={{
              fontSize: 18,
              fontWeight: '600',
              color: isDark ? '#fff' : '#000',
            }}>
              Complete Your Profile
            </Text>
          </View>

          <Text style={{
            fontSize: 14,
            color: isDark ? '#9ca3af' : '#6b7280',
            marginBottom: 20,
          }}>
            Please add the following required information to continue using the dashboard.
          </Text>

          {/* Missing Fields */}
          <View style={{ gap: 12, marginBottom: 24 }}>
            {missingFields.map((field) => {
              const fieldInfo = getFieldInfo(field);
              return (
                <View
                  key={field}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'flex-start',
                    gap: 12,
                    padding: 12,
                    borderRadius: 8,
                    backgroundColor: fieldInfo.bgColor,
                    borderWidth: 1,
                    borderColor: `${fieldInfo.color}20`,
                  }}
                >
                  <View style={{
                    padding: 6,
                    borderRadius: 6,
                    backgroundColor: `${fieldInfo.color}20`,
                  }}>
                    {fieldInfo.icon}
                  </View>
                  <View style={{ flex: 1 }}>
                    <Text style={{
                      fontSize: 14,
                      fontWeight: '500',
                      color: isDark ? '#fff' : '#000',
                      marginBottom: 4,
                    }}>
                      {fieldInfo.label}
                    </Text>
                    <Text style={{
                      fontSize: 12,
                      color: isDark ? '#9ca3af' : '#6b7280',
                    }}>
                      {fieldInfo.description}
                    </Text>
                  </View>
                </View>
              );
            })}
          </View>

          {/* Button */}
          <TouchableOpacity
            onPress={handleClose}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              paddingVertical: 12,
              paddingHorizontal: 16,
              borderRadius: 8,
              backgroundColor: '#D4AF37',
              marginBottom: 8,
            }}
          >
            <CheckCircle size={16} color="#fff" style={{ marginRight: 8 }} />
            <Text style={{
              color: '#fff',
              fontSize: 14,
              fontWeight: '500',
            }}>
              Got it, let me update my profile
            </Text>
          </TouchableOpacity>

          <Text style={{
            fontSize: 12,
            textAlign: 'center',
            color: isDark ? '#9ca3af' : '#6b7280',
          }}>
            You can update these details in the forms below
          </Text>
        </View>
      </View>
    </Modal>
  );
}
