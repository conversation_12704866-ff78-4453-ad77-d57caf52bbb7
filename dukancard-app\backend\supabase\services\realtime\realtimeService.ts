import { supabase } from '@/lib/supabase';
import { RealtimeChannel } from '@supabase/supabase-js';
import { useEffect, useRef } from 'react';
import { AppState } from 'react-native';

export type RealtimeEventType = 'INSERT' | 'UPDATE' | 'DELETE';

export interface RealtimeSubscription {
  id: string;
  channel: RealtimeChannel;
  unsubscribe: () => void;
}

export interface RealtimeEvent<T = any> {
  eventType: RealtimeEventType;
  table: string;
  schema: string;
  new: T | null;
  old: T | null;
  errors: string[] | null;
}

export type RealtimeCallback<T = any> = (event: RealtimeEvent<T>) => void;

class RealtimeService {
  private subscriptions: Map<string, RealtimeSubscription> = new Map();
  private isConnected: boolean = false;

  /**
   * Subscribe to real-time changes on a specific table
   */
  subscribeToTable<T extends Record<string, any> = any>(
    table: string,
    callback: RealtimeCallback<T>,
    options?: {
      event?: RealtimeEventType | '*';
      schema?: string;
      filter?: string;
    }
  ): RealtimeSubscription {
    const subscriptionId = `${table}_${Date.now()}_${Math.random()}`;
    const schema = options?.schema || 'public';
    const event = options?.event || '*';



    const channel = supabase
      .channel(`realtime:${subscriptionId}`)
      .on(
        'postgres_changes' as any,
        {
          event,
          schema,
          table,
          filter: options?.filter,
        },
        (payload: any) => {


          const realtimeEvent: RealtimeEvent<T> = {
            eventType: payload.eventType as RealtimeEventType,
            table: payload.table,
            schema: payload.schema,
            new: (payload.new as T) || null,
            old: (payload.old as T) || null,
            errors: payload.errors || null,
          };

          callback(realtimeEvent);
        }
      )
      .subscribe((status: string) => {

        this.isConnected = status === 'SUBSCRIBED';
      });

    const subscription: RealtimeSubscription = {
      id: subscriptionId,
      channel,
      unsubscribe: () => this.unsubscribe(subscriptionId),
    };

    this.subscriptions.set(subscriptionId, subscription);
    return subscription;
  }

  /**
   * Subscribe to user-specific notifications
   */
  subscribeToUserNotifications(
    userId: string,
    callback: RealtimeCallback
  ): RealtimeSubscription {
    return this.subscribeToTable(
      'notifications',
      callback,
      {
        filter: `user_id=eq.${userId}`,
        event: 'INSERT',
      }
    );
  }

  /**
   * Subscribe to business profile updates
   */
  subscribeToBusinessUpdates(
    businessId: string,
    callback: RealtimeCallback
  ): RealtimeSubscription {
    return this.subscribeToTable(
      'business_profiles',
      callback,
      {
        filter: `id=eq.${businessId}`,
        event: 'UPDATE',
      }
    );
  }

  /**
   * Subscribe to feed updates (new posts, likes, etc.)
   */
  subscribeToFeedUpdates(
    callback: RealtimeCallback
  ): RealtimeSubscription {
    return this.subscribeToTable(
      'posts',
      callback,
      {
        event: '*',
      }
    );
  }

  /**
   * Subscribe to post interactions (likes, comments)
   * DISABLED: Real-time subscriptions removed from feed as per user request
   */
  /*
  subscribeToPostInteractions(
    postId: string,
    callback: RealtimeCallback
  ): RealtimeSubscription {
    return this.subscribeToTable(
      'post_likes',
      callback,
      {
        filter: `post_id=eq.${postId}`,
        event: '*',
      }
    );
  }
  */

  /**
   * Subscribe to customer metrics updates (reviews, subscriptions, likes)
   */
  subscribeToCustomerReviews(
    userId: string,
    callback: RealtimeCallback
  ): RealtimeSubscription {
    return this.subscribeToTable(
      'reviews',
      callback,
      {
        filter: `user_id=eq.${userId}`,
        event: '*',
      }
    );
  }

  subscribeToCustomerSubscriptions(
    userId: string,
    callback: RealtimeCallback
  ): RealtimeSubscription {
    return this.subscribeToTable(
      'subscriptions',
      callback,
      {
        filter: `user_id=eq.${userId}`,
        event: '*',
      }
    );
  }

  subscribeToCustomerLikes(
    userId: string,
    callback: RealtimeCallback
  ): RealtimeSubscription {
    return this.subscribeToTable(
      'likes',
      callback,
      {
        filter: `user_id=eq.${userId}`,
        event: '*',
      }
    );
  }

  /**
   * Subscribe to business likes for real-time updates
   */
  subscribeToBusinessLikes(
    businessId: string,
    callback: RealtimeCallback
  ): RealtimeSubscription {
    return this.subscribeToTable(
      'likes',
      callback,
      {
        filter: `business_profile_id=eq.${businessId}`,
        event: '*',
      }
    );
  }

  /**
   * Subscribe to business subscriptions for real-time updates
   */
  subscribeToBusinessSubscriptions(
    businessId: string,
    callback: RealtimeCallback
  ): RealtimeSubscription {
    return this.subscribeToTable(
      'subscriptions',
      callback,
      {
        filter: `business_profile_id=eq.${businessId}`,
        event: '*',
      }
    );
  }

  /**
   * Subscribe to business reviews for real-time updates
   */
  subscribeToBusinessReviews(
    businessId: string,
    callback: RealtimeCallback
  ): RealtimeSubscription {
    return this.subscribeToTable(
      'reviews',
      callback,
      {
        filter: `business_profile_id=eq.${businessId}`,
        event: '*',
      }
    );
  }

  /**
   * Subscribe to business activities for real-time updates
   */
  subscribeToBusinessActivities(
    businessId: string,
    callback: RealtimeCallback,
    event: RealtimeEventType | '*' = 'INSERT'
  ): RealtimeSubscription {
    return this.subscribeToTable(
      'business_activities',
      callback,
      {
        filter: `business_profile_id=eq.${businessId}`,
        event,
      }
    );
  }

  /**
   * Unsubscribe from a specific subscription
   */
  unsubscribe(subscriptionId: string): void {
    const subscription = this.subscriptions.get(subscriptionId);
    if (subscription) {

      subscription.channel.unsubscribe();
      this.subscriptions.delete(subscriptionId);
    }
  }

  /**
   * Unsubscribe from all subscriptions
   */
  unsubscribeAll(): void {

    this.subscriptions.forEach((subscription) => {
      subscription.channel.unsubscribe();
    });
    this.subscriptions.clear();
    this.isConnected = false;
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  /**
   * Get active subscriptions count
   */
  getActiveSubscriptionsCount(): number {
    return this.subscriptions.size;
  }

  /**
   * Reconnect all subscriptions (useful for app state changes)
   */
  reconnect(): void {
    console.log('Reconnecting real-time subscriptions');
    // Supabase handles reconnection automatically, but we can force it if needed
    this.subscriptions.forEach((subscription) => {
      subscription.channel.subscribe();
    });
  }

  /**
   * Force cleanup all subscriptions and reset connection state
   * Useful for app state changes, logout, or critical cleanup scenarios
   */
  forceCleanup(): void {

    this.subscriptions.forEach((subscription) => {
      try {
        subscription.channel.unsubscribe();
      } catch (error) {

      }
    });
    this.subscriptions.clear();
    this.isConnected = false;
  }
}

// Export singleton instance
export const realtimeService = new RealtimeService();

/**
 * React hook for managing real-time subscriptions
 */

export function useRealtimeSubscription<T extends Record<string, any> = any>(
  table: string,
  callback: RealtimeCallback<T>,
  options?: {
    event?: RealtimeEventType | '*';
    schema?: string;
    filter?: string;
    enabled?: boolean;
  }
) {
  const subscriptionRef = useRef<RealtimeSubscription | null>(null);
  const enabled = options?.enabled !== false;
  const event = options?.event;
  const schema = options?.schema;
  const filter = options?.filter;

  useEffect(() => {
    if (!enabled) {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
        subscriptionRef.current = null;
      }
      return;
    }

    // Subscribe to real-time changes
    subscriptionRef.current = realtimeService.subscribeToTable(
      table,
      callback,
      { event, schema, filter }
    );

    // Cleanup on unmount
    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
        subscriptionRef.current = null;
      }
    };
  }, [table, callback, enabled, event, schema, filter]);

  return subscriptionRef.current;
}

/**
 * Hook for user notifications
 */
export function useUserNotifications(
  userId: string | null,
  callback: RealtimeCallback,
  enabled: boolean = true
) {
  const subscriptionRef = useRef<RealtimeSubscription | null>(null);

  useEffect(() => {
    if (!enabled || !userId) {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
        subscriptionRef.current = null;
      }
      return;
    }

    subscriptionRef.current = realtimeService.subscribeToUserNotifications(
      userId,
      callback
    );

    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
        subscriptionRef.current = null;
      }
    };
  }, [userId, enabled, callback]);

  return subscriptionRef.current;
}

/**
 * Hook for customer metrics real-time updates
 */
export function useCustomerMetricsSubscriptions(
  userId: string | null,
  onMetricsUpdate: () => void,
  enabled: boolean = true
) {
  const reviewsSubscriptionRef = useRef<RealtimeSubscription | null>(null);
  const subscriptionsSubscriptionRef = useRef<RealtimeSubscription | null>(null);
  const likesSubscriptionRef = useRef<RealtimeSubscription | null>(null);

  useEffect(() => {
    if (!enabled || !userId) {
      // Cleanup all subscriptions
      [reviewsSubscriptionRef, subscriptionsSubscriptionRef, likesSubscriptionRef].forEach(ref => {
        if (ref.current) {
          ref.current.unsubscribe();
          ref.current = null;
        }
      });
      return;
    }

    // Subscribe to reviews changes
    reviewsSubscriptionRef.current = realtimeService.subscribeToCustomerReviews(
      userId,
      () => onMetricsUpdate()
    );

    // Subscribe to subscriptions changes
    subscriptionsSubscriptionRef.current = realtimeService.subscribeToCustomerSubscriptions(
      userId,
      () => onMetricsUpdate()
    );

    // Subscribe to likes changes
    likesSubscriptionRef.current = realtimeService.subscribeToCustomerLikes(
      userId,
      () => onMetricsUpdate()
    );

    return () => {
      // Cleanup all subscriptions
      [reviewsSubscriptionRef, subscriptionsSubscriptionRef, likesSubscriptionRef].forEach(ref => {
        if (ref.current) {
          ref.current.unsubscribe();
          ref.current = null;
        }
      });
    };
  }, [userId, enabled, onMetricsUpdate]);

  return {
    reviewsSubscription: reviewsSubscriptionRef.current,
    subscriptionsSubscription: subscriptionsSubscriptionRef.current,
    likesSubscription: likesSubscriptionRef.current,
  };
}

/**
 * Hook for post interactions real-time updates
 * DISABLED: Real-time subscriptions removed from feed as per user request
 */
/*
export function usePostInteractionsSubscription(
  postId: string | null,
  onInteractionUpdate: (event: RealtimeEvent) => void,
  enabled: boolean = true
) {
  const subscriptionRef = useRef<RealtimeSubscription | null>(null);

  useEffect(() => {
    if (!enabled || !postId) {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
        subscriptionRef.current = null;
      }
      return;
    }

    // Subscribe to post interactions (likes, comments)
    subscriptionRef.current = realtimeService.subscribeToPostInteractions(
      postId,
      onInteractionUpdate
    );

    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
        subscriptionRef.current = null;
      }
    };
  }, [postId, enabled, onInteractionUpdate]);

  return subscriptionRef.current;
}
*/

/**
 * Hook for business social interactions real-time updates
 */
export function useBusinessSocialInteractions(
  businessId: string | null,
  onLikesUpdate: () => void,
  onSubscriptionsUpdate: () => void,
  onReviewsUpdate: () => void,
  enabled: boolean = true
) {
  const likesSubscriptionRef = useRef<RealtimeSubscription | null>(null);
  const subscriptionsSubscriptionRef = useRef<RealtimeSubscription | null>(null);
  const reviewsSubscriptionRef = useRef<RealtimeSubscription | null>(null);

  useEffect(() => {
    if (!enabled || !businessId) {
      // Cleanup all subscriptions
      [likesSubscriptionRef, subscriptionsSubscriptionRef, reviewsSubscriptionRef].forEach(ref => {
        if (ref.current) {
          ref.current.unsubscribe();
          ref.current = null;
        }
      });
      return;
    }

    // Subscribe to business likes changes
    likesSubscriptionRef.current = realtimeService.subscribeToBusinessLikes(
      businessId,
      () => onLikesUpdate()
    );

    // Subscribe to business subscriptions changes
    subscriptionsSubscriptionRef.current = realtimeService.subscribeToBusinessSubscriptions(
      businessId,
      () => onSubscriptionsUpdate()
    );

    // Subscribe to business reviews changes
    reviewsSubscriptionRef.current = realtimeService.subscribeToBusinessReviews(
      businessId,
      () => onReviewsUpdate()
    );

    return () => {
      // Cleanup all subscriptions
      [likesSubscriptionRef, subscriptionsSubscriptionRef, reviewsSubscriptionRef].forEach(ref => {
        if (ref.current) {
          ref.current.unsubscribe();
          ref.current = null;
        }
      });
    };
  }, [businessId, enabled, onLikesUpdate, onSubscriptionsUpdate, onReviewsUpdate]);

  return {
    likesSubscription: likesSubscriptionRef.current,
    subscriptionsSubscription: subscriptionsSubscriptionRef.current,
    reviewsSubscription: reviewsSubscriptionRef.current,
  };
}

/**
 * Global app-level real-time subscription management hook
 * Handles app state changes, network connectivity, and global cleanup
 */
export function useGlobalRealtimeCleanup() {
  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'background') {
        console.log('App going to background - cleaning up all real-time subscriptions');
        realtimeService.unsubscribeAll();
      } else if (nextAppState === 'active') {
        console.log('App returning to foreground - real-time subscriptions will be reestablished by components');
        // Individual components will reestablish their subscriptions when they become focused
      }
    };

    // Listen for app state changes
    const appStateSubscription = AppState.addEventListener('change', handleAppStateChange);

    // Global cleanup on unmount (app termination)
    return () => {

      appStateSubscription?.remove();
      realtimeService.forceCleanup();
    };
  }, []);

  // Return cleanup functions for manual use
  return {
    cleanupAll: () => realtimeService.forceCleanup(),
    getActiveCount: () => realtimeService.getActiveSubscriptionsCount(),
    getConnectionStatus: () => realtimeService.getConnectionStatus(),
  };
}
