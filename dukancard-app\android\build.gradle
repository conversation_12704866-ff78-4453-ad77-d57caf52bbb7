// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
  repositories {
    google()
    mavenCentral()
  }
  dependencies {
    classpath('com.android.tools.build:gradle')
    classpath('com.facebook.react:react-native-gradle-plugin')
    classpath('org.jetbrains.kotlin:kotlin-gradle-plugin')
  }
}

def reactNativeAndroidDir = new File(
  providers.exec {
    workingDir(rootDir)
    commandLine("node", "--print", "require.resolve('react-native/package.json')")
  }.standardOutput.asText.get().trim(),
  "../android"
)

allprojects {
  repositories {
    maven {
      // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
      url(reactNativeAndroidDir)
    }

    google()
    mavenCentral()
    maven { url 'https://www.jitpack.io' }
  }
}


// Set NDK version globally for all modules
def ndkDir = new File(System.getenv("ANDROID_HOME") ?: System.getenv("ANDROID_SDK_ROOT") ?: "", "ndk")
if (ndkDir.exists()) {
    def validNdks = ndkDir.listFiles()?.findAll {
        it.isDirectory() && new File(it, "source.properties").exists()
    }?.sort { it.name }
    if (validNdks && !validNdks.isEmpty()) {
        ext.ndkVersion = validNdks.last().name
        println "Global NDK version set to: ${ext.ndkVersion}"
    }
}

apply plugin: "expo-root-project"
apply plugin: "com.facebook.react.rootproject"
