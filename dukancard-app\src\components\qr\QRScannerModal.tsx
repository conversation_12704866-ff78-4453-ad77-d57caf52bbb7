import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { X, Flashlight, FlashlightOff, WifiOff } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import { Camera } from 'expo-camera';
import { useRouter } from 'expo-router';
import QRScanner from './QRScanner';
import { QRScanService } from '@/backend/supabase/services/common/qrScanService';
import { useToast } from '@/src/components/ui/Toast';
import { validateQRCodeForUser } from '@/src/utils/qrCodeUtils';
import { createQRScannerModalStyles } from '@/styles/components/QRScannerModal-styles';
import { useNetworkStatus } from '@/src/utils/networkStatus';

interface QRScannerModalProps {
  visible: boolean;
  onClose: () => void;
  onScanSuccess?: (businessSlug: string) => void;
  allowOfflineBusiness?: boolean; // For business dashboard usage
}

export default function QRScannerModal({
  visible,
  onClose,
  onScanSuccess,
  allowOfflineBusiness = false
}: QRScannerModalProps) {
  const [isFlashlightOn, setIsFlashlightOn] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingMessage, setProcessingMessage] = useState('');
  const [offlineQueuedScans, setOfflineQueuedScans] = useState<string[]>([]);
  const [scanProgress, setScanProgress] = useState(0);
  const isMountedRef = useRef(true);
  const router = useRouter();
  const styles = createQRScannerModalStyles();
  const networkStatus = useNetworkStatus();
  const toast = useToast();

  // Safe state setters that check if component is still mounted
  const safeSetIsProcessing = (value: boolean) => {
    if (isMountedRef.current) setIsProcessing(value);
  };

  const safeSetProcessingMessage = (value: string) => {
    if (isMountedRef.current) setProcessingMessage(value);
  };

  const safeSetScanProgress = (value: number) => {
    if (isMountedRef.current) setScanProgress(value);
  };

  const safeSetOfflineQueuedScans = (value: string[] | ((prev: string[]) => string[])) => {
    if (isMountedRef.current) setOfflineQueuedScans(value);
  };

  // Define processQueuedScans with useCallback to avoid dependency issues
  const processQueuedScans = useCallback(async () => {
    if (offlineQueuedScans.length === 0 || isProcessing) return;

    safeSetIsProcessing(true);
    safeSetProcessingMessage('Processing queued QR scans...');
    safeSetScanProgress(0);

        toast.info('Processing queued QR scans...');

    try {
      for (let i = 0; i < offlineQueuedScans.length; i++) {
        const qrData = offlineQueuedScans[i];
        const progress = ((i + 1) / offlineQueuedScans.length) * 100;

        safeSetProcessingMessage(`Processing scan ${i + 1} of ${offlineQueuedScans.length}...`);
        safeSetScanProgress(progress);

        try {
          const scanResult = allowOfflineBusiness
            ? await QRScanService.processQRScanAllowOffline(qrData)
            : await QRScanService.processQRScan(qrData);

          if (scanResult.success && scanResult.businessSlug) {
            safeSetProcessingMessage('Success! Opening business profile...');
            safeSetScanProgress(100);

            toast.success('QR scan processed successfully!');

            // Clear the queue before navigation
            safeSetOfflineQueuedScans([]);

            if (onScanSuccess) {
              onScanSuccess(scanResult.businessSlug);
            } else {
              router.push(`/business/${scanResult.businessSlug}`);
            }

            // Reset processing state
            setTimeout(() => {
              safeSetIsProcessing(false);
              safeSetProcessingMessage('');
              safeSetScanProgress(0);
            }, 500);

            return; // Exit after first successful scan
          }
        } catch (error) {
          console.error('Error processing queued scan:', error);
          // Continue to next scan
        }
      }

      // If we get here, no scans were successful
            toast.error('Failed to process queued QR scans. Please try scanning again.');
    } catch (error) {
      console.error('Error in processQueuedScans:', error);
      toast.error('Error processing queued scans.');
    } finally {
      // Clear the queue and reset state
      safeSetOfflineQueuedScans([]);
      safeSetIsProcessing(false);
      safeSetProcessingMessage('');
      safeSetScanProgress(0);
    }
  }, [offlineQueuedScans, isProcessing, allowOfflineBusiness, onScanSuccess, router, toast]);

  // Handle network status changes and process queued scans
  useEffect(() => {
    if (networkStatus.isConnected && offlineQueuedScans.length > 0) {
      // Process queued scans when coming back online
      processQueuedScans();
    }
  }, [networkStatus.isConnected, offlineQueuedScans.length, processQueuedScans]);

  // Cleanup effect when modal is closed
  useEffect(() => {
    if (!visible) {
      // Reset all states when modal is closed
      setIsFlashlightOn(false);
      setIsProcessing(false);
      setProcessingMessage('');
      setScanProgress(0);
      // Keep offline queued scans for when modal reopens
    }
  }, [visible]);

  // Cleanup effect on unmount
  useEffect(() => {
    return () => {
      // Mark component as unmounted to prevent state updates
      isMountedRef.current = false;
    };
  }, []);

  // Early return if not visible to avoid unnecessary rendering
  if (!visible) {
    return null;
  }



  const handleScanSuccess = async (businessSlug: string) => {
    if (isProcessing) return;

    safeSetIsProcessing(true);
    safeSetScanProgress(0);
    const qrData = `https://dukancard.in/${businessSlug}`;

    try {
      // Check if we're offline
      if (!networkStatus.isConnected) {
        setProcessingMessage('Offline mode detected...');
        setScanProgress(50);
        // Handle offline scenario
        handleOfflineQRScan(qrData);
        return;
      }

      // Step 1: Validating QR code
      safeSetProcessingMessage('Validating QR code...');
      safeSetScanProgress(25);

      // Small delay to show progress
      await new Promise(resolve => setTimeout(resolve, 300));

      // Step 2: Fetching business data
      safeSetProcessingMessage('Loading business information...');
      safeSetScanProgress(50);

      // Use the comprehensive QR scan service
      const scanResult = allowOfflineBusiness
        ? await QRScanService.processQRScanAllowOffline(qrData)
        : await QRScanService.processQRScan(qrData);

      safeSetScanProgress(75);

      if (!scanResult.success) {
        safeSetScanProgress(100);
        safeSetProcessingMessage('Error occurred');

        const errorMessage = QRScanService.getErrorMessage(scanResult.error || 'Business not found');
        const recoveryAction = QRScanService.getErrorRecoveryAction(scanResult.error || 'Business not found');

        // Show error with recovery suggestion
        toast.error(`${errorMessage}
${recoveryAction.message}`);

        // Handle specific error types
        if (recoveryAction.action === 'network') {
          // Queue for retry when network is available
          safeSetOfflineQueuedScans(prev => [...prev, qrData]);
          toast.info('QR scan queued for when connection is restored.');
        }

        safeSetIsProcessing(false);
        safeSetScanProgress(0);
        safeSetProcessingMessage('');
        return;
      }

      // Step 3: Success - preparing navigation
      safeSetProcessingMessage('Success! Opening business profile...');
      safeSetScanProgress(100);

      // Small delay to show completion
      await new Promise(resolve => setTimeout(resolve, 500));

      // Success - close modal and navigate or call callback
      onClose();

      if (onScanSuccess) {
        onScanSuccess(businessSlug);
      } else {
        // Default navigation to business card view
        router.push(`/business/${businessSlug}`);
      }

      toast.success('QR code scanned successfully!');
    } catch (error) {
      console.error('QR Scanner: Error processing scanned QR:', error);

      safeSetScanProgress(100);
      safeSetProcessingMessage('Error occurred');

      // Handle network errors specifically
      if (error instanceof Error && (error.message.includes('network') || error.message.includes('fetch'))) {
        handleOfflineQRScan(qrData);
      } else {
        toast.error('Failed to process QR code. Please try again.');
      }
    } finally {
      // Reset states after a delay
      setTimeout(() => {
        safeSetIsProcessing(false);
        safeSetScanProgress(0);
        safeSetProcessingMessage('');
      }, 1000);
    }
  };

  const handleOfflineQRScan = (qrData: string) => {
    // Validate QR code format offline
    const validation = validateQRCodeForUser(qrData);

    if (!validation.isValid) {
      toast.error('Invalid QR code format.');
      safeSetIsProcessing(false);
      return;
    }

    // Queue the scan for when network is available
    safeSetOfflineQueuedScans(prev => [...prev, qrData]);

    toast.info(
      'You are offline. QR scan has been queued and will be processed when connection is restored.'
    );

    // Close modal but don't navigate yet
    onClose();
    safeSetIsProcessing(false);
  };

  const handleScanError = (error: string) => {
    console.error('QR scan error:', error);
    // Error is already shown by the QRScanner component
  };

  const toggleFlashlight = () => {
    setIsFlashlightOn(!isFlashlightOn);
  };

  const handleGalleryPress = async () => {
    if (isProcessing) return;

    try {
      // Check current permission status first
      const currentPermission = await ImagePicker.getMediaLibraryPermissionsAsync();

      let finalStatus = currentPermission.status;

      // If permission is not granted, request it
      if (finalStatus !== 'granted') {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        finalStatus = status;
      }

      // Handle different permission states
      if (finalStatus === 'denied') {
        Alert.alert(
          'Gallery Permission Required',
          'Gallery access is needed to select QR code images. Please enable gallery permissions in your device settings.',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Open Settings',
              onPress: () => {
                // Note: Linking.openSettings() opens app settings on iOS
                // On Android, it opens the general settings
                import('expo-linking').then(({ default: Linking }) => {
                  Linking.openSettings();
                });
              }
            }
          ]
        );
        return;
      }

      if (finalStatus !== 'granted') {
        toast.error('Gallery permission is required to select QR code images');
        return;
      }

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: false,
        quality: 1,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const imageUri = result.assets[0].uri;

        try {
          // Use Expo Camera's scanFromURLAsync to detect QR codes from the image
          const scanResults = await Camera.scanFromURLAsync(imageUri, ['qr']);

          if (scanResults && scanResults.length > 0) {
            const qrData = scanResults[0].data;

            // Process the QR code data using the same logic as camera scanning
            const validation = validateQRCodeForUser(qrData);

            if (!validation.isValid) {
              toast.error(validation.error || 'Invalid QR code');
              return;
            }

            const businessSlug = validation.businessSlug!;
            toast.success('QR code detected successfully!');

            // Handle the successful scan
            await handleScanSuccess(businessSlug);
          } else {
            toast.error('No QR code found in the selected image');
          }
        } catch (error) {
          toast.error('Failed to scan QR code from image');
        }
      }
    } catch (error) {
      console.error('QR Scanner: Error selecting image from gallery:', error);
      toast.error('Failed to select image from gallery');
    }
  };

  const handleClose = () => {
    if (isProcessing) {
      Alert.alert(
        'Processing QR Code',
        'A QR code is currently being processed. Do you want to cancel?',
        [
          { text: 'Wait', style: 'cancel' },
          {
            text: 'Cancel',
            style: 'destructive',
            onPress: () => {
              // Force close and cleanup
              safeSetIsProcessing(false);
              safeSetProcessingMessage('');
              safeSetScanProgress(0);
              onClose();
            }
          }
        ]
      );
      return;
    }

    // Clean up states before closing
    setIsFlashlightOn(false);
    setProcessingMessage('');
    setScanProgress(0);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={handleClose}
    >
      <StatusBar style="light" backgroundColor="#000" />
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={handleClose}
            disabled={isProcessing}
          >
            <X color="#fff" size={24} />
          </TouchableOpacity>

          <View style={styles.headerCenter}>
            <Text style={styles.headerTitle}>Scan QR Code</Text>
            {!networkStatus.isConnected && (
              <View style={styles.offlineIndicator}>
                <WifiOff color="#EF4444" size={16} />
                <Text style={styles.offlineText}>Offline</Text>
              </View>
            )}
          </View>

          <TouchableOpacity
            style={styles.flashlightButton}
            onPress={toggleFlashlight}
            disabled={isProcessing}
          >
            {isFlashlightOn ? (
              <FlashlightOff color="#D4AF37" size={24} />
            ) : (
              <Flashlight color="#fff" size={24} />
            )}
          </TouchableOpacity>
        </View>

        {/* Scanner */}
        <View style={styles.scannerContainer}>
          <QRScanner
            onScanSuccess={handleScanSuccess}
            onScanError={handleScanError}
            onClose={onClose}
            enableTorch={isFlashlightOn}
          />
        </View>

        {/* Footer with instructions */}
        <View style={styles.footer}>
          <TouchableOpacity
            style={styles.galleryButton}
            onPress={handleGalleryPress}
            disabled={isProcessing}
          >
            <Text style={styles.galleryButtonText}>
              Select QR from Gallery
            </Text>
          </TouchableOpacity>

          <Text style={styles.footerText}>
            Scan any Dukancard business QR code to view their profile
          </Text>
        </View>

        {/* Processing Overlay */}
        {isProcessing && (
          <View style={styles.processingOverlay}>
            <View style={styles.processingContainer}>
              <ActivityIndicator size="large" color="#D4AF37" />
              <Text style={styles.processingText}>
                {processingMessage || 'Processing QR code...'}
              </Text>
              {scanProgress > 0 && (
                <View style={styles.progressContainer}>
                  <View style={styles.progressBar}>
                    <View
                      style={[
                        styles.progressFill,
                        { width: `${scanProgress}%` }
                      ]}
                    />
                  </View>
                  <Text style={styles.progressText}>{scanProgress}%</Text>
                </View>
              )}
            </View>
          </View>
        )}
      </SafeAreaView>
    </Modal>
  );
}


