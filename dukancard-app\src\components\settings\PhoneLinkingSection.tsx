import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Input } from '@/src/components/ui/Input';
import { Button } from '@/src/components/ui/Button';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { Phone, AlertCircle, CheckCircle } from 'lucide-react-native';
import { linkCustomerPhone, verifyPhoneOTP } from '@/backend/supabase/services/common/settingsService';

interface PhoneLinkingSectionProps {
  currentEmail?: string | null;
  currentPhone?: string | null;
  registrationType: 'google' | 'email' | 'phone';
  onPhoneUpdated?: () => void;
}

interface FormData {
  phone: string;
  otp: string;
}

interface FormErrors {
  phone?: string;
  otp?: string;
}

type Step = 'phone' | 'otp';

export function PhoneLinkingSection({
  currentPhone,
  registrationType,
  onPhoneUpdated,
}: PhoneLinkingSectionProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [step, setStep] = useState<Step>('phone');
  const [formData, setFormData] = useState<FormData>({
    phone: '',
    otp: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<string>('');
  const [phoneForOTP, setPhoneForOTP] = useState<string>('');

  const isPhoneUser = registrationType === 'phone';

  const validatePhone = (phone: string): string | undefined => {
    if (!phone.trim()) {
      return 'Phone number is required';
    }
    // Remove all non-digits
    const cleanPhone = phone.replace(/\D/g, '');
    if (cleanPhone.length !== 10) {
      return 'Phone number must be 10 digits';
    }
    if (!/^[6-9]/.test(cleanPhone)) {
      return 'Phone number must start with 6, 7, 8, or 9';
    }
    return undefined;
  };

  const validateOTP = (otp: string): string | undefined => {
    if (!otp.trim()) {
      return 'Verification code is required';
    }
    if (otp.trim().length !== 6) {
      return 'Verification code must be 6 digits';
    }
    if (!/^\d{6}$/.test(otp.trim())) {
      return 'Verification code must contain only numbers';
    }
    return undefined;
  };

  const formatPhoneNumber = (phone: string): string => {
    // Remove all non-digits
    const cleanPhone = phone.replace(/\D/g, '');
    // Limit to 10 digits
    const limitedPhone = cleanPhone.slice(0, 10);
    // Format as XXX XXX XXXX
    if (limitedPhone.length >= 6) {
      return `${limitedPhone.slice(0, 3)} ${limitedPhone.slice(3, 6)} ${limitedPhone.slice(6)}`;
    } else if (limitedPhone.length >= 3) {
      return `${limitedPhone.slice(0, 3)} ${limitedPhone.slice(3)}`;
    }
    return limitedPhone;
  };

  const handlePhoneSubmit = async () => {
    const phoneError = validatePhone(formData.phone);
    if (phoneError) {
      setErrors({ phone: phoneError });
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      const cleanPhone = formData.phone.replace(/\D/g, '');
      const result = await linkCustomerPhone(cleanPhone);

      if (result.success) {
        if (result.requiresOTP) {
          setPhoneForOTP(cleanPhone);
          setStep('otp');
          setMessage("We've sent a 6-digit verification code to your phone number.");
          Alert.alert('Success', 'Verification code sent to your phone!');
        } else {
          setMessage('Phone number has been linked to your account.');
          Alert.alert('Success', 'Phone linked successfully!');
          onPhoneUpdated?.();
        }
      } else {
        setErrors({ phone: result.error || 'Failed to link phone number' });
        Alert.alert('Error', result.error || 'Failed to link phone number');
      }
    } catch (error) {
      console.error('Phone linking error:', error);
      setErrors({ phone: 'An unexpected error occurred' });
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleOTPSubmit = async () => {
    const otpError = validateOTP(formData.otp);
    if (otpError) {
      setErrors({ otp: otpError });
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      const result = await verifyPhoneOTP(phoneForOTP, formData.otp.trim());

      if (result.success) {
        setMessage('Phone number has been linked to your account.');
        setStep('phone');
        setPhoneForOTP('');
        setFormData({ phone: '', otp: '' });
        Alert.alert('Success', 'Phone linked successfully!');
        onPhoneUpdated?.();
      } else {
        setErrors({ otp: result.error || 'Failed to verify code' });
        Alert.alert('Error', result.error || 'Failed to verify code');
      }
    } catch (error) {
      console.error('OTP verification error:', error);
      setErrors({ otp: 'An unexpected error occurred' });
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePhoneChange = (text: string) => {
    const formatted = formatPhoneNumber(text);
    setFormData(prev => ({ ...prev, phone: formatted }));
    // Clear error when user starts typing
    if (errors.phone) {
      setErrors(prev => ({ ...prev, phone: undefined }));
    }
  };

  const handleOTPChange = (text: string) => {
    // Only allow digits and limit to 6 characters
    const numericText = text.replace(/[^0-9]/g, '').slice(0, 6);
    setFormData(prev => ({ ...prev, otp: numericText }));
    // Clear error when user starts typing
    if (errors.otp) {
      setErrors(prev => ({ ...prev, otp: undefined }));
    }
  };

  const getTitle = () => {
    if (isPhoneUser) return "Update Phone Number";
    return "Link Phone Number";
  };

  const getDescription = () => {
    if (currentPhone) {
      return "Your current phone number linked to this account.";
    }
    return "No phone number is currently linked to your account.";
  };

  const formatDisplayPhone = (phone: string | null | undefined): string => {
    if (!phone) return 'Not linked';
    // Remove country code if present
    const cleanPhone = phone.replace(/^\+91/, '').replace(/^91/, '');
    return formatPhoneNumber(cleanPhone);
  };

  return (
    <View style={[styles.container, { backgroundColor: isDark ? '#1a1a1a' : '#fff' }]}>
      <View style={styles.header}>
        <View style={[styles.iconContainer, { backgroundColor: isDark ? '#********' : '#D1FAE5' }]}>
          <Phone size={20} color="#10B981" />
        </View>
        <View style={styles.headerContent}>
          <Text style={[styles.title, { color: isDark ? '#fff' : '#000' }]}>
            {getTitle()}
          </Text>
          <Text style={[styles.description, { color: isDark ? '#999' : '#666' }]}>
            {getDescription()}
          </Text>
        </View>
      </View>

      {message ? (
        <View style={[styles.messageContainer, { backgroundColor: isDark ? '#********' : '#D1FAE5' }]}>
          <View style={styles.messageContent}>
            <CheckCircle size={16} color="#10B981" />
            <Text style={[styles.messageText, { color: isDark ? '#34D399' : '#059669' }]}>
              {message}
            </Text>
          </View>
        </View>
      ) : null}

      <View style={styles.content}>
        {currentPhone && step === 'phone' ? (
          // Show current phone number
          <View style={styles.currentPhoneContainer}>
            <Text style={[styles.label, { color: isDark ? '#fff' : '#000' }]}>
              Current Phone Number
            </Text>
            <View style={[styles.readOnlyField, { backgroundColor: isDark ? '#2a2a2a' : '#f5f5f5' }]}>
              <Text style={[styles.readOnlyText, { color: isDark ? '#999' : '#666' }]}>
                +91 {formatDisplayPhone(currentPhone)}
              </Text>
            </View>
          </View>
        ) : null}

        {step === 'phone' ? (
          // Phone step
          <View style={styles.formContainer}>
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: isDark ? '#fff' : '#000' }]}>
                {isPhoneUser ? 'New Phone Number' : 'Phone Number'}
              </Text>
              <View style={styles.phoneInputContainer}>
                <View style={[styles.countryCode, { backgroundColor: isDark ? '#2a2a2a' : '#f5f5f5' }]}>
                  <Text style={[styles.countryCodeText, { color: isDark ? '#fff' : '#000' }]}>+91</Text>
                </View>
                <Input
                  value={formData.phone}
                  onChangeText={handlePhoneChange}
                  placeholder="XXX XXX XXXX"
                  error={errors.phone}
                  keyboardType="numeric"
                  editable={!isLoading}
                  containerStyle={styles.phoneInput}
                />
              </View>
              <Text style={[styles.helperText, { color: isDark ? '#999' : '#666' }]}>
                {isPhoneUser
                  ? "We'll send verification codes to both your current and new phone numbers."
                  : "We'll send a verification code to this phone number."
                }
              </Text>
            </View>

            <Button
              title={isLoading ? 'Sending...' : isPhoneUser ? 'Update Phone' : 'Link Phone'}
              onPress={handlePhoneSubmit}
              disabled={isLoading || !formData.phone.trim()}
              variant="primary"
              icon={
                isLoading ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Phone size={20} color="#fff" />
                )
              }
            />
          </View>
        ) : (
          // OTP step
          <View style={styles.formContainer}>
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: isDark ? '#fff' : '#000' }]}>
                Verification Code
              </Text>
              <Input
                value={formData.otp}
                onChangeText={handleOTPChange}
                placeholder="Enter 6-digit code"
                error={errors.otp}
                leftIcon={<AlertCircle size={20} color={isDark ? '#999' : '#666'} />}
                keyboardType="numeric"
                maxLength={6}
                editable={!isLoading}
              />
              <Text style={[styles.helperText, { color: isDark ? '#999' : '#666' }]}>
                Enter the 6-digit code sent to +91 {formatPhoneNumber(phoneForOTP)}
              </Text>
            </View>

            <View style={styles.buttonGroup}>
              <Button
                title="Back"
                onPress={() => {
                  setStep('phone');
                  setFormData(prev => ({ ...prev, otp: '' }));
                  setErrors({});
                }}
                disabled={isLoading}
                variant="secondary"
                style={styles.backButton}
              />
              <Button
                title={isLoading ? 'Verifying...' : 'Verify Code'}
                onPress={handleOTPSubmit}
                disabled={isLoading || formData.otp.length !== 6}
                variant="primary"
                style={styles.verifyButton}
                icon={
                  isLoading ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <CheckCircle size={20} color="#fff" />
                  )
                }
              />
            </View>
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
  },
  messageContainer: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#10B981',
  },
  messageContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  messageText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  content: {
    gap: 16,
  },
  currentPhoneContainer: {
    gap: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
  },
  readOnlyField: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e5e5',
  },
  readOnlyText: {
    fontSize: 14,
  },
  helperText: {
    fontSize: 12,
    lineHeight: 16,
  },
  formContainer: {
    gap: 20,
  },
  inputGroup: {
    gap: 8,
  },
  phoneInputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 8,
  },
  countryCode: {
    paddingHorizontal: 12,
    paddingVertical: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e5e5',
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 60,
  },
  countryCodeText: {
    fontSize: 16,
    fontWeight: '500',
  },
  phoneInput: {
    flex: 1,
  },
  buttonGroup: {
    flexDirection: 'row',
    gap: 12,
  },
  backButton: {
    flex: 1,
  },
  verifyButton: {
    flex: 2,
  },
});
