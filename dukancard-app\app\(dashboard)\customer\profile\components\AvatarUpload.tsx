import React, { useState, useRef } from "react";
import { View, Text, TouchableOpacity, Image, Alert } from "react-native";
import { Camera, Loader2, User } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import ImagePickerBottomSheet, { ImagePickerBottomSheetRef } from "@/src/components/pickers/ImagePickerBottomSheet";
import { openCameraForAvatar, openGalleryForAvatar, uploadAvatarImage } from "@/backend/supabase/services/storage/avatarUploadService";
import { responsiveFontSize } from '@/lib/theme/colors';

interface AvatarUploadProps {
  initialAvatarUrl?: string;
  userName?: string | null;
  onUpdateAvatar: (_url: string) => void;
}

export default function AvatarUpload({
  initialAvatarUrl,
  userName,
  onUpdateAvatar,
}: AvatarUploadProps) {
  const [localPreviewUrl, setLocalPreviewUrl] = useState<string | undefined>(initialAvatarUrl || undefined);
  const [isUploading, setIsUploading] = useState(false);
  const theme = useTheme();
  const imagePickerRef = useRef<ImagePickerBottomSheetRef>(null);

  const themeColor = "#D4AF37";

  // Generate initials from name
  const getInitials = (name?: string | null) => {
    if (!name) return "U";

    const parts = name.split(/\s+/);
    if (parts.length === 1) {
      return name.substring(0, 2).toUpperCase();
    }

    return (
      parts[0].charAt(0) + parts[parts.length - 1].charAt(0)
    ).toUpperCase();
  };

  const initials = getInitials(userName);

  const handleAvatarUpload = () => {
    imagePickerRef.current?.present();
  };

  const handleCameraSelection = async () => {
    try {
      setIsUploading(true);
      const result = await openCameraForAvatar();
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const imageUri = result.assets[0].uri;

        // Upload the image and get the URL
        const uploadResult = await uploadAvatarImage(imageUri);

        if (uploadResult.success && uploadResult.url) {
          setLocalPreviewUrl(uploadResult.url);
          onUpdateAvatar(uploadResult.url);
          Alert.alert('Success', 'Avatar updated successfully!');
        } else {
          Alert.alert('Error', uploadResult.error || 'Failed to upload avatar. Please try again.');
        }
      }
    } catch (error) {
      console.error('Error uploading avatar:', error);
      Alert.alert('Error', 'Failed to upload avatar. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleGallerySelection = async () => {
    try {
      setIsUploading(true);
      const result = await openGalleryForAvatar();
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const imageUri = result.assets[0].uri;

        // Upload the image and get the URL
        const uploadResult = await uploadAvatarImage(imageUri);

        if (uploadResult.success && uploadResult.url) {
          setLocalPreviewUrl(uploadResult.url);
          onUpdateAvatar(uploadResult.url);
          Alert.alert('Success', 'Avatar updated successfully!');
        } else {
          Alert.alert('Error', uploadResult.error || 'Failed to upload avatar. Please try again.');
        }
      }
    } catch (error) {
      console.error('Error uploading avatar:', error);
      Alert.alert('Error', 'Failed to upload avatar. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <View style={{ alignItems: 'center', gap: theme.spacing.md }}>
      <View style={{ position: 'relative' }}>
        {/* Avatar Container */}
        <View style={{
          width: responsiveFontSize(96),
          height: responsiveFontSize(96),
          borderRadius: responsiveFontSize(48),
          borderWidth: 2,
          borderColor: theme.colors.background,
          shadowColor: theme.shadows.md.shadowColor,
            shadowOffset: theme.shadows.md.shadowOffset,
            shadowOpacity: theme.shadows.md.shadowOpacity,
            shadowRadius: theme.shadows.md.shadowRadius,
            elevation: theme.shadows.md.elevation,
          overflow: 'hidden',
          backgroundColor: theme.colors.primary + '20',
        }}>
          {localPreviewUrl ? (
            <Image
              source={{ uri: localPreviewUrl }}
              style={{ width: '100%', height: '100%' }}
              resizeMode="cover"
            />
          ) : (
            <View style={{
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: theme.colors.primary + '20',
            }}>
              {userName ? (
                <Text style={{
                  fontSize: theme.typography.fontSize.xxl,
                  fontWeight: 'bold',
                  color: theme.colors.primary,
                }}>
                  {initials}
                </Text>
              ) : (
                <User size={responsiveFontSize(32)} color={theme.colors.primary} />
              )}
            </View>
          )}
        </View>

        {/* Camera Button */}
        <TouchableOpacity
          onPress={handleAvatarUpload}
          disabled={isUploading}
          style={{
            position: 'absolute',
            bottom: 0,
            right: 0,
            width: responsiveFontSize(32),
            height: responsiveFontSize(32),
            borderRadius: responsiveFontSize(16),
            backgroundColor: theme.colors.primary,
            justifyContent: 'center',
            alignItems: 'center',
            shadowColor: theme.shadows.md.shadowColor,
            shadowOffset: theme.shadows.md.shadowOffset,
            shadowOpacity: theme.shadows.md.shadowOpacity,
            shadowRadius: theme.shadows.md.shadowRadius,
            elevation: theme.shadows.md.elevation,
            opacity: isUploading ? 0.7 : 1,
          }}
        >
          <Camera size={responsiveFontSize(16)} color={theme.colors.primaryForeground} />
        </TouchableOpacity>
      </View>

      {/* Loading State */}
      {isUploading && (
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Loader2 size={responsiveFontSize(16)} color={theme.colors.textSecondary} style={{ marginRight: theme.spacing.xs }} />
          <Text style={{
            fontSize: theme.typography.fontSize.sm,
            color: theme.colors.textSecondary,
          }}>
            Uploading...
          </Text>
        </View>
      )}

      {/* Image Picker Bottom Sheet */}
      <ImagePickerBottomSheet
        ref={imagePickerRef}
        onCameraPress={handleCameraSelection}
        onGalleryPress={handleGallerySelection}
        title="Update Profile Picture"
        cameraLabel="Take Photo"
        galleryLabel="Choose from Gallery"
      />
    </View>
  );
}
