import { supabase } from '@/lib/supabase';

export interface BusinessValidationResult {
  isValid: boolean;
  exists: boolean;
  isOnline: boolean;
  businessData?: {
    id: string;
    business_name: string;
    business_slug: string;
    status: string;
    logo_url?: string;
    member_name?: string;
    city?: string;
    state?: string;
  };
  error?: string;
}

/**
 * Validates if a business slug exists and is accessible
 * @param businessSlug - The business slug to validate
 * @returns Validation result with business data if valid
 */
export async function validateBusinessSlug(businessSlug: string): Promise<BusinessValidationResult> {
  if (!businessSlug || typeof businessSlug !== 'string') {
    return {
      isValid: false,
      exists: false,
      isOnline: false,
      error: 'Invalid business slug'
    };
  }

  const cleanSlug = businessSlug.trim().toLowerCase();

  if (!cleanSlug) {
    return {
      isValid: false,
      exists: false,
      isOnline: false,
      error: 'Empty business slug'
    };
  }

  try {
    // Query the business profile using regular client with public read access
    const { data: businessProfile, error } = await supabase
      .from('business_profiles')
      .select(`
        id,
        business_name,
        business_slug,
        status,
        logo_url,
        member_name,
        city,
        state
      `)
      .eq('business_slug', cleanSlug)
      .maybeSingle();

    if (error) {
      console.error('Error validating business slug:', error);
      return {
        isValid: false,
        exists: false,
        isOnline: false,
        error: 'Failed to validate business'
      };
    }

    if (!businessProfile) {
      return {
        isValid: false,
        exists: false,
        isOnline: false,
        error: 'Business not found'
      };
    }

    const isOnline = businessProfile.status === 'online';

    return {
      isValid: isOnline,
      exists: true,
      isOnline,
      businessData: businessProfile,
      error: isOnline ? undefined : 'Business is currently offline'
    };
  } catch (error) {
    console.error('Exception validating business slug:', error);
    return {
      isValid: false,
      exists: false,
      isOnline: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Checks if a business exists (regardless of online status)
 * @param businessSlug - The business slug to check
 * @returns True if business exists, false otherwise
 */
export async function businessExists(businessSlug: string): Promise<boolean> {
  const result = await validateBusinessSlug(businessSlug);
  return result.exists;
}

/**
 * Checks if a business is online and accessible
 * @param businessSlug - The business slug to check
 * @returns True if business is online, false otherwise
 */
export async function isBusinessOnline(businessSlug: string): Promise<boolean> {
  const result = await validateBusinessSlug(businessSlug);
  return result.isOnline;
}

/**
 * Gets basic business information for a slug
 * @param businessSlug - The business slug
 * @returns Business data if found and online, null otherwise
 */
export async function getBusinessInfo(businessSlug: string): Promise<BusinessValidationResult['businessData'] | null> {
  const result = await validateBusinessSlug(businessSlug);
  return result.isValid ? result.businessData! : null;
}

/**
 * Validates business slug with user-friendly error messages
 * @param businessSlug - The business slug to validate
 * @returns User-friendly validation result
 */
export async function validateBusinessSlugForUser(businessSlug: string): Promise<BusinessValidationResult> {
  const result = await validateBusinessSlug(businessSlug);
  
  if (!result.isValid && result.error) {
    // Convert technical errors to user-friendly messages
    const userFriendlyErrors: Record<string, string> = {
      'Invalid business slug': 'Invalid business URL.',
      'Empty business slug': 'Business URL is empty.',
      'Business not found': 'This business was not found. The QR code may be outdated or invalid.',
      'Business is currently offline': 'This business is currently not available. Please try again later.',
      'Failed to validate business': 'Unable to verify business. Please check your internet connection.',
      'An unexpected error occurred': 'Something went wrong. Please try again.'
    };

    const userFriendlyError = userFriendlyErrors[result.error] || result.error;
    
    return {
      ...result,
      error: userFriendlyError
    };
  }

  return result;
}
