// Expo configuration for Dukancard React Native app
// Public keys and configuration are hardcoded in src/config/publicKeys.ts

import versionConfig from "./version.json";

export default {
  expo: {
    name: "Dukancard",
    slug: "dukancard-app",
    version: versionConfig.version,
    orientation: "portrait",
    icon: "./assets/images/icon.png",
    scheme: "dukancardapp",
    userInterfaceStyle: "automatic",
    newArchEnabled: true,
    linking: {
      prefixes: ["dukancardapp://", "https://dukancard.in"],
      config: {
        screens: {
          "(dashboard)": {
            screens: {
              customer: "dashboard/customer",
              business: "dashboard/business",
            },
          },
          "post/[postId]": "post/:postId",
          "business/[businessSlug]": "business/:businessSlug",
        },
      },
    },
    ios: {
      bundleIdentifier: "com.dukancardapp.dukancard",
      buildNumber: versionConfig.buildNumber, // Increment this for each App Store release
      supportsTablet: true,
      infoPlist: {
        NSLocationWhenInUseUsageDescription:
          "This app uses location to automatically fill in your pincode during onboarding, making the setup process faster and more convenient.",
        NSLocationAlwaysAndWhenInUseUsageDescription:
          "This app uses location to automatically fill in your pincode during onboarding, making the setup process faster and more convenient.",
        NSCameraUsageDescription:
          "This app uses camera to take profile pictures and upload images.",
        NSPhotoLibraryUsageDescription:
          "This app uses photo library to select profile pictures and upload images.",
        NSMicrophoneUsageDescription:
          "This app uses microphone for camera functionality.",
        NSFaceIDUsageDescription:
          "This app uses Face ID for secure authentication and profile access.",
      },
    },
    android: {
      package: "com.dukancardapp.dukancard",
      versionCode: versionConfig.versionCode, // Increment this for each Play Store release
      icon: "./assets/images/icon.png", // Fallback for older Android devices
      adaptiveIcon: {
        foregroundImage: "./assets/images/adaptive-icon.png",
        backgroundColor: "#000000",
      },
      edgeToEdgeEnabled: true,
      softwareKeyboardLayoutMode: "pan",
      permissions: [
        "ACCESS_COARSE_LOCATION",
        "ACCESS_FINE_LOCATION",
        "CAMERA",
        "READ_EXTERNAL_STORAGE",
        "WRITE_EXTERNAL_STORAGE",
        "READ_MEDIA_IMAGES",
        "USE_BIOMETRIC",
        "USE_FINGERPRINT",
      ],
    },
    web: {
      bundler: "metro",
      output: "static",
      favicon: "./assets/images/favicon.png",
    },
    plugins: [
      "expo-dev-client",
      "expo-router",
      [
        "expo-splash-screen",
        {
          backgroundColor: "#ffffff",
          resizeMode: "contain",
          dark: {
            backgroundColor: "#000000",
          },
          // Hide default splash screen immediately since we use custom splash
          hideAsync: true,
          // Prevent native splash from showing at all
          preventAutoHideAsync: false,
        },
      ],
      [
        "expo-location",
        {
          locationAlwaysAndWhenInUsePermission:
            "This app uses location to automatically fill in your pincode during onboarding, making the setup process faster and more convenient.",
          locationWhenInUsePermission:
            "This app uses location to automatically fill in your pincode during onboarding, making the setup process faster and more convenient.",
        },
      ],
      [
        "@react-native-google-signin/google-signin",
        {
          iosUrlScheme:
            "com.googleusercontent.apps.110991972471-9g5hbuj78s88b5fi0m97i4k0823v7430",
        },
      ],
      [
        "expo-image-picker",
        {
          photosPermission:
            "This app uses photo library to select profile pictures and upload images.",
          cameraPermission:
            "This app uses camera to take profile pictures and upload images.",
        },
      ],
      [
        "expo-media-library",
        {
          photosPermission:
            "This app uses photo library to select and save profile pictures.",
          savePhotosPermission:
            "This app saves profile pictures to your photo library.",
        },
      ],
      [
        "expo-camera",
        {
          cameraPermission:
            "This app uses camera to scan QR codes for business discovery.",
        },
      ],
      "@maplibre/maplibre-react-native",
    ],
    experiments: {
      typedRoutes: true,
    },
    extra: {
      // Public keys are now hardcoded in src/config/publicKeys.ts
      // This eliminates environment variable issues and simplifies configuration
      // All configuration is now handled in the publicKeys.ts file
    },
  },
};
