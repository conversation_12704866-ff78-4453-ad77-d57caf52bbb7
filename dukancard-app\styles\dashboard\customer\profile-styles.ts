import { StyleSheet } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { responsiveFontSize } from "@/lib/theme/colors";

export const createCustomerProfileStyles = (
  theme: ReturnType<typeof useTheme>
) => {
  return StyleSheet.create({
    container: {
      flex: 1,
    },
    errorContainer: {
      marginHorizontal: theme.spacing.md,
      marginVertical: theme.spacing.lg,
    },
    // Profile content matching skeleton layout
    profileContent: {
      padding: theme.spacing.lg,
      paddingBottom: 120, // Extra bottom padding to account for bottom navigation (80px) + safe area
    },
    // Profile header - matching skeleton profileHeader
    profileHeader: {
      alignItems: "center",
      marginBottom: theme.spacing.xl,
    },
    largeAvatar: {
      width: responsiveFontSize(100),
      height: responsiveFontSize(100),
      borderRadius: responsiveFontSize(50),
      borderWidth: 2,
      borderColor: theme.colors.primary, // Golden border to match brand
      justifyContent: "center",
      alignItems: "center",
      marginBottom: theme.spacing.md,
      overflow: "hidden",
      shadowColor: theme.colors.primary,
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
      elevation: 4,
    },
    avatarImage: {
      width: responsiveFontSize(96), // Slightly smaller to account for border
      height: responsiveFontSize(96),
      borderRadius: responsiveFontSize(48),
    },
    profileName: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: "600",
      marginBottom: theme.spacing.xs,
    },
    // Profile stats - matching skeleton profileStats
    profileStats: {
      flexDirection: "row",
      justifyContent: "space-around",
      marginBottom: theme.spacing.xl,
      paddingVertical: theme.spacing.md,
      borderRadius: theme.borderRadius.lg,
    },
    profileStatItem: {
      alignItems: "center",
    },
    statValue: {
      fontSize: theme.typography.fontSize.xxl,
      fontWeight: "bold",
      marginBottom: theme.spacing.xs,
    },
    statLabel: {
      fontSize: theme.typography.fontSize.sm,
    },
    // Profile menu - matching skeleton profileMenu
    profileMenu: {
      gap: theme.spacing.md,
    },
    profileMenuItem: {
      flexDirection: "row",
      alignItems: "center",
      paddingVertical: theme.spacing.sm,
    },
    menuItemText: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "500",
      flex: 1,
      marginLeft: theme.spacing.md,
    },
    // Theme section styles
    themeSection: {
      marginTop: theme.spacing.xl,
      padding: theme.spacing.md,
      borderRadius: theme.borderRadius.lg,
    },
    themeSectionHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: theme.spacing.sm,
    },
    themeSectionTitle: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      marginLeft: theme.spacing.sm,
    },
    // Logout button styles
    logoutButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: theme.spacing.sm,
      paddingHorizontal: theme.spacing.md,
      borderRadius: theme.borderRadius.md,
      marginTop: theme.spacing.xl,
      gap: theme.spacing.xs,
      borderWidth: 1,
      borderColor: theme.colors.error,
    },
    logoutButtonText: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
    },
  });
};
