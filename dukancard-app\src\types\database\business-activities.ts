import { UserProfile } from ".";

export interface BusinessActivitiesRow {
  id: string;
  business_profile_id: string;
  user_id: string;
  activity_type: string;
  rating_value: number | null;
  created_at: string;
  is_read: boolean;
  post_id: string | null;
  post_type: string | null;
  user_profile: UserProfile;
}

export interface BusinessActivitiesInsert {
  id?: string;
  business_profile_id: string;
  user_id: string;
  activity_type: string;
  rating_value?: number | null;
  created_at?: string;
  is_read?: boolean;
  post_id?: string | null;
  post_type?: string | null;
}

export interface BusinessActivitiesUpdate {
  id?: string;
  business_profile_id?: string;
  user_id?: string;
  activity_type?: string;
  rating_value?: number | null;
  created_at?: string;
  is_read?: boolean;
  post_id?: string | null;
  post_type?: string | null;
}

export type BusinessActivities = BusinessActivitiesRow;
