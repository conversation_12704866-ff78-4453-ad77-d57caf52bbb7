import { useState, useEffect, useCallback } from 'react';
import { 
  checkLocationPermission, 
  requestLocationPermission,
} from '@/backend/supabase/services/location/locationService';
import * as Location from 'expo-location';

export interface UseLocationPermissionReturn {
  permission: Location.PermissionResponse | null;
  isLoading: boolean;
  requestPermission: () => Promise<Location.PermissionResponse>;
  checkPermission: () => Promise<void>;
}

export function useLocationPermission(): UseLocationPermissionReturn {
  const [permission, setPermission] = useState<Location.PermissionResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const checkPermission = useCallback(async () => {
    setIsLoading(true);
    try {
      const result = await checkLocationPermission();
      setPermission(result);
    } catch (error) {
      console.error('Error checking location permission:', error);
      setPermission(null); // Set to null or a default error state if needed
    } finally {
      setIsLoading(false);
    }
  }, []);

  const requestPermission = useCallback(async (): Promise<Location.PermissionResponse> => {
    setIsLoading(true);
    try {
      const result = await requestLocationPermission();
      setPermission(result);
      return result;
    } catch (error) {
      console.error('Error requesting location permission:', error);
      setPermission(null); // Set to null or a default error state if needed
      throw error; // Re-throw the error to be handled by the caller
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Check permission on mount
  useEffect(() => {
    checkPermission();
  }, [checkPermission]);

  return {
    permission,
    isLoading,
    requestPermission,
    checkPermission,
  };
}
