import { useTheme } from '@/src/hooks/useTheme';
import React from 'react';
import {
    Keyboard,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    TouchableWithoutFeedback,
    View,
    ViewStyle
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { createScreenContainerStyles } from '@/styles/layout/ScreenContainer-styles';

interface ScreenContainerProps {
  children: React.ReactNode;
  scrollable?: boolean;
  showKeyboardAvoidingView?: boolean;
  showStatusBar?: boolean;
  statusBarStyle?: 'auto' | 'inverted' | 'light' | 'dark';
  backgroundColor?: string;
  contentContainerStyle?: ViewStyle;
  scrollContentContainerStyle?: ViewStyle;
  style?: ViewStyle;
  keyboardVerticalOffset?: number;
  includeSafeArea?: boolean;
  safeAreaEdges?: ('top' | 'right' | 'bottom' | 'left')[];
}

/**
 * ScreenContainer - Universal layout component for all screens
 * Provides consistent keyboard handling, safe area, and scrolling behavior
 *
 * Features:
 * - Automatic keyboard avoidance with proper content adjustment
 * - ScrollView with optimized keyboard handling
 * - Safe area handling
 * - Status bar management
 * - Theme-aware styling
 *
 * Usage:
 * <ScreenContainer scrollable showKeyboardAvoidingView>
 *   <YourScreenContent />
 * </ScreenContainer>
 */
export function ScreenContainer({
  children,
  scrollable = true,
  showKeyboardAvoidingView = true,
  showStatusBar = true,
  statusBarStyle,
  backgroundColor,
  contentContainerStyle,
  scrollContentContainerStyle,
  style,
  keyboardVerticalOffset,
  includeSafeArea = true,
  safeAreaEdges = ['top', 'left', 'right'],
}: ScreenContainerProps) {
  const theme = useTheme();
  const styles = createScreenContainerStyles({ theme, backgroundColor });
  const insets = useSafeAreaInsets();

  // Determine status bar style based on theme if not provided
  const finalStatusBarStyle = statusBarStyle || (theme.isDark ? 'light' : 'dark');

  const innerContent = scrollable ? (
    <ScrollView
      style={styles.scrollView}
      contentContainerStyle={[styles.scrollContentContainer, scrollContentContainerStyle]}
      showsVerticalScrollIndicator={false}
      keyboardShouldPersistTaps="handled"
    >
      <View style={[styles.contentContainer, contentContainerStyle]}>
        {children}
      </View>
    </ScrollView>
  ) : (
    <View style={[styles.inner, contentContainerStyle, style]}>
      {children}
    </View>
  );

  const mainContent = showKeyboardAvoidingView ? (
    <KeyboardAvoidingView
      style={styles.keyboardAvoidingView}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={keyboardVerticalOffset || (Platform.OS === 'ios' ? 0 : 20)}
      enabled={true}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={styles.inner}>
          {innerContent}
        </View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  ) : (
    innerContent
  );

  // Calculate safe area padding based on edges
  const safeAreaStyle = includeSafeArea ? {
    paddingTop: safeAreaEdges.includes('top') ? insets.top : 0,
    paddingRight: safeAreaEdges.includes('right') ? insets.right : 0,
    paddingBottom: safeAreaEdges.includes('bottom') ? insets.bottom : 0,
    paddingLeft: safeAreaEdges.includes('left') ? insets.left : 0,
  } : {};

  return (
    <View style={[styles.safeArea, safeAreaStyle]}>
      {showStatusBar && (
        <StatusBar
          style={finalStatusBarStyle}
          backgroundColor={backgroundColor || theme.colors.background}
          translucent={false}
        />
      )}
      {mainContent}
    </View>
  );
}

/**
 * AuthScreenContainer - Pre-configured ScreenContainer for authentication screens
 */
export function AuthScreenContainer(props: Omit<ScreenContainerProps, 'showKeyboardAvoidingView'>) {
  return (
    <ScreenContainer
      {...props}
      showKeyboardAvoidingView={true}
    />
  );
}

/**
 * OnboardingScreenContainer - Pre-configured ScreenContainer for onboarding screens
 */
export function OnboardingScreenContainer(props: Omit<ScreenContainerProps, 'showKeyboardAvoidingView'>) {
  return (
    <ScreenContainer
      {...props}
      showKeyboardAvoidingView={true}
    />
  );
}

/**
 * DashboardScreenContainer - Pre-configured ScreenContainer for dashboard screens
 * Excludes safe area handling since dashboard layouts manage their own safe areas
 */
export function DashboardScreenContainer(props: ScreenContainerProps) {
  return (
    <ScreenContainer
      {...props}
      showKeyboardAvoidingView={props.showKeyboardAvoidingView ?? false}
      includeSafeArea={props.includeSafeArea ?? false}
      safeAreaEdges={props.safeAreaEdges ?? []}
    />
  );
}

/**
 * FormScreenContainer - Pre-configured ScreenContainer for form-heavy screens
 * Includes bottom safe area since these screens typically don't have bottom navigation
 */
export function FormScreenContainer(props: Omit<ScreenContainerProps, 'showKeyboardAvoidingView' | 'scrollable'>) {
  return (
    <ScreenContainer
      {...props}
      scrollable={true}
      showKeyboardAvoidingView={true}
      safeAreaEdges={props.safeAreaEdges ?? ['top', 'left', 'right', 'bottom']}
    />
  );
}
