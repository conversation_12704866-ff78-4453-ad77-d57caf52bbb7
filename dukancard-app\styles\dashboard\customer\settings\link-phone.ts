import { StyleSheet } from 'react-native';
import { useTheme } from '@/src/hooks/useTheme';
import { responsiveFontSize } from '@/lib/theme/colors';

export const createLinkPhoneSectionStyles = (theme: ReturnType<typeof useTheme>) => {
  return StyleSheet.create({
    container: {
      backgroundColor: theme.colors.card,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      marginBottom: theme.spacing.md,
    },
    header: {
      padding: theme.spacing.lg,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.sm,
      marginBottom: theme.spacing.sm,
    },
    iconContainer: {
      padding: theme.spacing.sm,
      borderRadius: 8,
      backgroundColor: '#10B981' + '20', // Green with 20% opacity
    },
    title: {
      fontSize: responsiveFontSize(18),
      fontWeight: '600',
      color: theme.colors.foreground,
    },
    description: {
      fontSize: responsiveFontSize(14),
      color: theme.colors.mutedForeground,
      lineHeight: responsiveFontSize(20),
    },
    content: {
      padding: theme.spacing.lg,
    },
    section: {
      gap: theme.spacing.md,
    },
    label: {
      fontSize: responsiveFontSize(14),
      fontWeight: '500',
      color: theme.colors.foreground,
      marginBottom: theme.spacing.xs,
    },
    readOnlyContainer: {
      backgroundColor: theme.colors.muted,
      borderRadius: 8,
      padding: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    readOnlyText: {
      fontSize: responsiveFontSize(14),
      color: theme.colors.mutedForeground,
    },
    helperText: {
      fontSize: responsiveFontSize(12),
      color: theme.colors.mutedForeground,
      lineHeight: responsiveFontSize(16),
    },
    emptyState: {
      alignItems: 'center',
      padding: theme.spacing.xl,
      backgroundColor: theme.colors.muted,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    emptyIconContainer: {
      padding: theme.spacing.md,
      borderRadius: responsiveFontSize(50),
      backgroundColor: theme.colors.border,
      marginBottom: theme.spacing.md,
    },
    emptyTitle: {
      fontSize: responsiveFontSize(18),
      fontWeight: '600',
      color: theme.colors.foreground,
      marginBottom: theme.spacing.sm,
    },
    emptyDescription: {
      fontSize: responsiveFontSize(14),
      color: theme.colors.mutedForeground,
      textAlign: 'center',
      lineHeight: responsiveFontSize(20),
      maxWidth: responsiveFontSize(280),
    },
  });
};
