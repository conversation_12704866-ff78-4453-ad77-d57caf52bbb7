import { BusinessSortBy, DiscoverSearchResult } from "./types";
import { fetchBusinessesBySearch } from "./businessActions";
import { fetchAllProducts, fetchProductsByBusinessIds } from "./productActions";
import { searchDiscoverData } from "./locationActions";

// Combined search function that handles both business and product searches
export async function searchDiscoverCombined(params: {
  businessName?: string | null;
  productName?: string | null;
  pincode?: string | null;
  city?: string | null;
  locality?: string | null;
  category?: string | null;
  viewType: "cards" | "products";
  page?: number;
  limit?: number;
  businessSort?: BusinessSortBy | string;
  productSort?: BusinessSortBy | string;
  productType?: "physical" | "service" | null;
  userLocation?: { latitude: number; longitude: number };
}): Promise<{
  data?: DiscoverSearchResult;
  error?: string;
}> {
  const {
    businessName,
    productName,
    pincode,
    city,
    locality,
    category,
    viewType,
    page = 1,
    limit = viewType === "products" ? 20 : 5,
    businessSort = "created_desc",
    productSort = "created_desc",
    productType = null,
  } = params;

  // Authentication is handled by auth guards in React Native
  const isAuthenticated = true;

  try {
    // Case 1: Search by product name (only for products view)
    if (
      viewType === "products" &&
      productName &&
      productName.trim().length > 0
    ) {
      // Fetch products by name
      const productsResult = await fetchAllProducts({
        page,
        limit,
        sortBy: productSort as BusinessSortBy,
        productType,
        pincode,
        locality,
        city,
        productName,
        category,
      });

      if (productsResult.error) {
        return { error: productsResult.error };
      }

      return {
        data: {
          products: productsResult.data?.products || [],
          isAuthenticated,
          totalCount: productsResult.data?.totalCount || 0,
          hasMore: productsResult.data?.hasMore || false,
          nextPage: productsResult.data?.nextPage || null,
        },
      };
    }
    // Case 2: Search by business name
    else if (businessName && businessName.trim().length > 0) {
      // First, search for businesses by name
      const businessResult = await fetchBusinessesBySearch({
        businessName,
        pincode,
        locality,
        city,
        page,
        limit,
        sortBy: businessSort as BusinessSortBy,
        category,
      });

      if (businessResult.error) {
        return { error: businessResult.error };
      }

      if (viewType === "cards") {
        // Return businesses directly
        return {
          data: {
            businesses: businessResult.data?.businesses || [],
            isAuthenticated,
            totalCount: businessResult.data?.totalCount || 0,
            hasMore: businessResult.data?.hasMore || false,
            nextPage: businessResult.data?.nextPage || null,
          },
        };
      } else {
        // viewType === "products"
        // Get business IDs from the search results
        const businessIds = businessResult.data?.businesses.map(
          (business) => business.id
        );

        if (!businessIds || businessIds.length === 0) {
          return {
            data: {
              products: [],
              isAuthenticated,
              totalCount: 0,
              hasMore: false,
              nextPage: null,
            },
          };
        }

        // Fetch products for these businesses
        const productsResult = await fetchProductsByBusinessIds({
          businessIds: businessIds as string[],
          page,
          limit,
          sortBy: productSort as BusinessSortBy,
          productType,
        });

        if (productsResult.error) {
          return { error: productsResult.error };
        }

        return {
          data: {
            products: productsResult.data?.products || [],
            isAuthenticated,
            totalCount: productsResult.data?.totalCount || 0,
            hasMore: productsResult.data?.hasMore || false,
            nextPage: productsResult.data?.nextPage || null,
          },
        };
      }
    }
    // Case 3: Search by location (pincode or city)
    else if (pincode || city) {
      return await searchDiscoverData({
        pincode: pincode || undefined,
        city: city || undefined,
        locality,
        viewType,
        page,
        limit,
        sortBy: (viewType === "products"
          ? productSort
          : businessSort) as BusinessSortBy,
        productType: viewType === "products" ? productType : null,
        category,
      });
    }
    // Case 4: No search criteria - show all businesses or products
    else {
      if (viewType === "cards") {
        // Fetch all businesses
        const businessResult = await fetchBusinessesBySearch({
          page,
          limit,
          sortBy: businessSort as BusinessSortBy,
          category,
          city: city || undefined,
        });

        if (businessResult.error) {
          return { error: businessResult.error };
        }

        return {
          data: {
            businesses: businessResult.data?.businesses || [],
            isAuthenticated,
            totalCount: businessResult.data?.totalCount || 0,
            hasMore: businessResult.data?.hasMore || false,
            nextPage: businessResult.data?.nextPage || null,
          },
        };
      } else {
        // viewType === "products"
        // Fetch all products

        const productsResult = await fetchAllProducts({
          page,
          limit,
          sortBy: productSort as BusinessSortBy,
          productType,
          pincode: pincode || undefined,
          locality,
          city: city || undefined,
          category,
        });

        if (productsResult.error) {
          return { error: productsResult.error };
        }

        return {
          data: {
            products: productsResult.data?.products || [],
            isAuthenticated,
            totalCount: productsResult.data?.totalCount || 0,
            hasMore: productsResult.data?.hasMore || false,
            nextPage: productsResult.data?.nextPage || null,
          },
        };
      }
    }
  } catch (e) {
    console.error("Search Discover Combined Exception:", e);
    return { error: "An unexpected error occurred during the search." };
  }
}
