{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "jsx": "react-native", "paths": {"@/*": ["./*"], "@/backend/*": ["./backend/*"], "@/src/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/screens/*": ["./src/screens/*"], "@/hooks/*": ["./src/hooks/*"], "@/contexts/*": ["./src/contexts/*"], "@/utils/*": ["./src/utils/*"], "@/types/*": ["./src/types/*"], "@/config/*": ["./src/config/*"], "@/constants/*": ["./src/constants/*"], "@/assets/*": ["./assets/*"], "@/lib/*": ["./lib/*"], "@/styles/*": ["./styles/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts", "nativewind-env.d.ts"], "exclude": ["<PERSON><PERSON>card/**/*", "node_modules", ".expo"]}