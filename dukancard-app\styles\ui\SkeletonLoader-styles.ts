import { StyleSheet, StatusBar } from "react-native";

const statusBarHeight = StatusBar.currentHeight || 0;

export const createSkeletonLoaderStyles = (isDark: boolean = false) => {
  const skeletonBaseColor = isDark ? "#1F2937" : "#E5E7EB";
  const backgroundColor = isDark ? "#0A0A0A" : "#F8FAFC"; // Match PublicCardViewStyles
  const cardBackgroundColor = isDark ? "#0A0A0A" : "#F8FAFC"; // Match PublicCardViewStyles
  const statsBackgroundColor = isDark ? "#2a2a2a" : "#F9FAFB";

  return StyleSheet.create({
    skeleton: {
      backgroundColor: skeletonBaseColor,
    },
    businessCard: {
      backgroundColor: cardBackgroundColor,
      margin: 16,
      padding: 16,
      borderRadius: 12,
      elevation: 2,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
    },
    businessCardHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 16,
    },
    businessCardInfo: {
      flex: 1,
      marginLeft: 12,
    },
    businessCardStats: {
      flexDirection: "row",
      justifyContent: "space-around",
      marginBottom: 16,
      paddingVertical: 12,
      backgroundColor: statsBackgroundColor,
      borderRadius: 8,
    },
    statItem: {
      alignItems: "center",
    },
    businessCardContent: {
      marginBottom: 16,
    },
    businessCardActions: {
      flexDirection: "row",
      justifyContent: "space-between",
    },
    profile: {
      backgroundColor: backgroundColor,
      padding: 20,
    },
    profileHeader: {
      alignItems: "center",
      marginBottom: 24,
    },
    profileStats: {
      flexDirection: "row",
      justifyContent: "space-around",
      marginBottom: 32,
      paddingVertical: 16,
      backgroundColor: statsBackgroundColor,
      borderRadius: 12,
    },
    profileStatItem: {
      alignItems: "center",
    },
    profileMenu: {
      gap: 16,
    },
    profileMenuItem: {
      flexDirection: "row",
      alignItems: "center",
      paddingVertical: 12,
    },
    searchResults: {
      padding: 16,
      gap: 12,
    },
    searchResultItem: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: cardBackgroundColor,
      padding: 12,
      borderRadius: 8,
      elevation: 1,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 1,
    },
    searchResultInfo: {
      flex: 1,
      marginLeft: 12,
    },

    // Enhanced Product Skeleton Styles - Matches exact ProductCard design
    productCard: {
      borderRadius: 8,
      borderWidth: 1,
      borderColor: isDark ? "#374151" : "#E5E7EB",
      overflow: "hidden",
      marginBottom: 8,
      padding: 2, // Reduced padding to match Next.js design
      backgroundColor: isDark ? "#1F2937" : "#FFFFFF",
    },
    productImageContainer: {
      width: "100%",
      height: 140, // Fixed height to match ProductCard
      borderRadius: 8,
      overflow: "hidden",
      position: "relative",
    },
    productBadgeContainer: {
      position: "absolute",
      top: 8,
      right: 8, // Positioned on right like real discount badge
      zIndex: 1,
    },
    productCardContent: {
      padding: 6, // Reduced padding to match Next.js compact design
    },
    productCardPriceRow: {
      paddingTop: 4,
    },
    productPriceContainer: {
      flexDirection: "row",
      alignItems: "baseline",
      gap: 6,
      flexWrap: "wrap",
    },
    productGrid: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between",
      paddingHorizontal: 20,
    },
    productGridItem: {
      width: "48%",
      marginBottom: 16,
    },
    productRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      width: "100%",
    },

    // Review Skeleton Styles - Matches exact ReviewCard design
    reviewCard: {
      backgroundColor: "#fff",
      borderRadius: 12,
      marginBottom: 16,
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 3.84,
      elevation: 5,
      overflow: "hidden",
    },
    reviewHeader: {
      flexDirection: "row",
      alignItems: "center",
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: "#f0f0f0",
    },
    reviewerInfo: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1,
    },
    reviewerDetails: {
      flex: 1,
      marginLeft: 12,
    },
    reviewStars: {
      flexDirection: "row",
      marginBottom: 12,
    },
    reviewTextContainer: {
      padding: 16,
    },
    reviewActions: {
      flexDirection: "row",
      justifyContent: "space-between",
      gap: 16,
    },
    reviewList: {
      paddingHorizontal: 20,
    },

    // Loading More Container
    loadMoreContainer: {
      paddingVertical: 16,
      paddingHorizontal: 20,
    },

    // Single Product Skeleton Styles
    singleProduct: {
      flex: 1,
      backgroundColor: backgroundColor,
    },
    singleProductHeader: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: isDark ? "#333333" : "#E5E7EB",
    },
    singleProductScrollContent: {
      flex: 1,
    },
    singleProductInfoCard: {
      margin: 16,
    },
    singleProductPriceRow: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 12,
      gap: 8,
    },
    singleProductDescription: {
      marginTop: 0,
    },
    singleProductVariants: {
      marginHorizontal: 16,
      marginBottom: 16,
    },
    singleProductVariantOptions: {
      flexDirection: "row",
      gap: 8,
    },
    singleProductBusinessCard: {
      margin: 16,
      marginTop: 0,
      padding: 16,
      borderRadius: 12,
      backgroundColor: cardBackgroundColor,
    },
    singleProductBusinessHeader: {
      flexDirection: "row",
      alignItems: "center",
    },
    singleProductBusinessInfo: {
      flex: 1,
      marginLeft: 12,
    },
    singleProductContactButtons: {
      flexDirection: "row",
      justifyContent: "space-between",
      gap: 12,
      marginHorizontal: 16,
      marginBottom: 16,
    },
    singleProductShareButton: {
      marginHorizontal: 16,
      marginBottom: 16,
    },
    singleProductAdSection: {
      marginHorizontal: 16,
      marginBottom: 16,
    },
    singleProductRecommendations: {
      marginHorizontal: 16,
      marginBottom: 16,
    },
    singleProductRecommendationGrid: {
      flexDirection: "row",
      justifyContent: "space-between",
      gap: 12,
    },
    singleProductRelated: {
      marginTop: 16,
    },
    singleProductRelatedGrid: {
      flexDirection: "row",
      justifyContent: "space-between",
      gap: 12,
    },

    // Public Card Navigation Skeleton Styles
    publicCardNavigationContainer: {
      flex: 1,
      backgroundColor: cardBackgroundColor,
    },
    publicCardNavigationHeader: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: isDark ? "#374151" : "#E5E7EB",
      backgroundColor: cardBackgroundColor,
    },

    // Public Card Tab Skeleton Styles
    publicCardAbout: {
      padding: 20,
    },
    publicCardAboutSection: {
      marginBottom: 24,
    },
    publicCardAboutRow: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 8,
    },
    publicCardProducts: {
      padding: 20,
    },
    publicCardProductsHeader: {
      marginBottom: 20,
    },
    publicCardProductsFilters: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginTop: 12,
    },
    publicCardProductsGrid: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between",
    },
    publicCardProductItem: {
      width: "48%",
      marginBottom: 16,
    },
    publicCardGallery: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between",
      padding: 20,
    },
    publicCardReviews: {
      padding: 20,
    },
    publicCardReviewsStats: {
      marginBottom: 20,
      alignItems: "center",
    },

    // Enhanced Public Card Skeleton Styles
    publicCardContainer: {
      flex: 1,
      backgroundColor: backgroundColor,
      marginTop: -statusBarHeight, // Start from top of screen to cover notch
      paddingTop: statusBarHeight, // Add padding to account for negative margin
    },
    publicCardHeader: {
      backgroundColor: "#D4AF37", // Gold theme color to match actual header
      padding: 24,
      paddingTop: statusBarHeight + 40, // Cover notch area like actual header
      paddingBottom: 32,
      alignItems: "stretch",
      borderBottomLeftRadius: 0,
      borderBottomRightRadius: 0,
    },
    publicCardHeaderTop: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: 20,
      paddingTop: 20, // Match actual header
      marginBottom: 16,
    },
    publicCardHeaderBackButton: {
      position: "absolute",
      top: 10,
      left: 20,
      zIndex: 1,
    },
    publicCardHeaderLogoContainer: {
      width: 104,
      height: 104,
      justifyContent: "center",
      alignItems: "center",
    },
    publicCardHeaderQRContainer: {
      width: 104,
      height: 104,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "rgba(255, 255, 255, 0.9)",
      borderRadius: 12,
      padding: 12,
    },
    publicCardHeaderBusinessInfo: {
      paddingHorizontal: 20,
      alignItems: "center",
    },

    // Public Card Stats Skeleton Styles
    publicCardStats: {
      padding: 20,
      backgroundColor: backgroundColor,
    },
    publicCardStatsRow: {
      flexDirection: "row",
      justifyContent: "space-around",
      marginBottom: 20,
      paddingVertical: 16,
      backgroundColor: isDark ? "#1A1A1A" : "#F9FAFB",
      borderRadius: 12,
      borderWidth: 2,
      borderColor: "#D4AF37",
    },
    publicCardStatItem: {
      alignItems: "center",
    },
    publicCardActionsRow: {
      flexDirection: "row",
      justifyContent: "space-around",
      marginBottom: 16,
    },
    publicCardContactRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 16,
    },
    publicCardSocialRow: {
      flexDirection: "row",
      justifyContent: "space-around",
    },

    // Ad Section and Tab Navigation Styles
    publicCardAdSection: {
      backgroundColor: backgroundColor,
      marginBottom: 20,
    },
    publicCardTabNav: {
      flexDirection: "row",
      justifyContent: "space-around",
      paddingHorizontal: 20,
      paddingVertical: 16,
      backgroundColor: backgroundColor,
      borderBottomWidth: 1,
      borderBottomColor: isDark ? "#374151" : "#E5E7EB",
    },
    publicCardHeaderContent: {
      alignItems: "center",
    },
    publicCardQRSection: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      width: "100%",
      marginTop: 16,
    },
    publicCardQRInfo: {
      flex: 1,
      marginLeft: 16,
    },
    publicCardContact: {
      paddingHorizontal: 20,
      paddingVertical: 16,
      backgroundColor: cardBackgroundColor,
    },
    publicCardSocial: {
      paddingHorizontal: 20,
      paddingVertical: 16,
      backgroundColor: cardBackgroundColor,
    },
  });
};
