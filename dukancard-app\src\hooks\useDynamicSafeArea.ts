import { useEffect, useRef } from "react";
import { AppState, AppStateStatus } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

/**
 * Custom hook to handle dynamic safe area changes when app resumes from background.
 * This helps prevent layout shifts when returning from other apps.
 */
export function useDynamicSafeArea() {
  const insets = useSafeAreaInsets();
  const appState = useRef(AppState.currentState);
  const previousInsets = useRef(insets);

  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      // When app becomes active again, check if safe area insets have changed
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === "active"
      ) {
        // Force a small delay to allow the system to settle
        setTimeout(() => {
          // The insets will be automatically updated by the SafeAreaProvider
          // This timeout just ensures any layout calculations are complete
        }, 100);
      }
      appState.current = nextAppState;
    };

    const subscription = AppState.addEventListener(
      "change",
      handleAppStateChange
    );

    return () => {
      subscription?.remove();
    };
  }, []);

  // Track inset changes
  useEffect(() => {
    const hasChanged =
      previousInsets.current.top !== insets.top ||
      previousInsets.current.bottom !== insets.bottom ||
      previousInsets.current.left !== insets.left ||
      previousInsets.current.right !== insets.right;

    if (hasChanged) {
      previousInsets.current = insets;
    }
  }, [insets]);

  return insets;
}
