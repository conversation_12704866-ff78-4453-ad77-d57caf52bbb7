import React, { Component, ReactNode } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { AlertTriangle, RefreshCw, Home } from "lucide-react-native";

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  showDetails?: boolean;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

export class ErrorBoundary extends Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log error in development only
    if (__DEV__) {
      // Error logged to development tools
    }

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <View style={styles.container}>
          <ScrollView
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
            <View style={styles.errorContainer}>
              {/* Error Icon */}
              <View style={styles.iconContainer}>
                <AlertTriangle color="#EF4444" size={64} />
              </View>

              {/* Error Title */}
              <Text style={styles.title}>Something went wrong</Text>

              {/* Error Message */}
              <Text style={styles.message}>
                We&apos;re sorry, but something unexpected happened. Please try
                again.
              </Text>

              {/* Error Details (Development only) */}
              {__DEV__ && this.props.showDetails && this.state.error && (
                <View style={styles.detailsContainer}>
                  <Text style={styles.detailsTitle}>
                    Error Details (Development)
                  </Text>
                  <View style={styles.errorDetails}>
                    <Text style={styles.errorText}>
                      {this.state.error.name}: {this.state.error.message}
                    </Text>
                    {this.state.error.stack && (
                      <Text style={styles.stackTrace}>
                        {this.state.error.stack}
                      </Text>
                    )}
                  </View>
                </View>
              )}

              {/* Action Buttons */}
              <View style={styles.actionsContainer}>
                <TouchableOpacity
                  style={[styles.button, styles.retryButton]}
                  onPress={this.handleRetry}
                  activeOpacity={0.8}
                >
                  <RefreshCw color="#FFFFFF" size={20} />
                  <Text style={styles.retryButtonText}>Try Again</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.button, styles.homeButton]}
                  onPress={() => {
                    // Navigate to home - implementation needed
                  }}
                  activeOpacity={0.8}
                >
                  <Home color="#6B7280" size={20} />
                  <Text style={styles.homeButtonText}>Go Home</Text>
                </TouchableOpacity>
              </View>

              {/* Help Text */}
              <Text style={styles.helpText}>
                If this problem persists, please contact support.
              </Text>
            </View>
          </ScrollView>
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F9FAFB",
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: "center",
    paddingHorizontal: 20,
    paddingVertical: 40,
  },
  errorContainer: {
    alignItems: "center",
    maxWidth: 400,
    alignSelf: "center",
  },
  iconContainer: {
    marginBottom: 24,
    padding: 16,
    backgroundColor: "#FEF2F2",
    borderRadius: 50,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1F2937",
    textAlign: "center",
    marginBottom: 12,
  },
  message: {
    fontSize: 16,
    color: "#6B7280",
    textAlign: "center",
    lineHeight: 24,
    marginBottom: 32,
  },
  detailsContainer: {
    width: "100%",
    marginBottom: 32,
    padding: 16,
    backgroundColor: "#FEF2F2",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#FECACA",
  },
  detailsTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: "#DC2626",
    marginBottom: 8,
  },
  errorDetails: {
    backgroundColor: "#FFFFFF",
    padding: 12,
    borderRadius: 6,
  },
  errorText: {
    fontSize: 12,
    color: "#DC2626",
    fontFamily: "monospace",
    marginBottom: 8,
  },
  stackTrace: {
    fontSize: 10,
    color: "#7F1D1D",
    fontFamily: "monospace",
    lineHeight: 14,
  },
  actionsContainer: {
    width: "100%",
    gap: 12,
    marginBottom: 24,
  },
  button: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    gap: 8,
  },
  retryButton: {
    backgroundColor: "#D4AF37",
  },
  retryButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  homeButton: {
    backgroundColor: "#FFFFFF",
    borderWidth: 1,
    borderColor: "#D1D5DB",
  },
  homeButtonText: {
    color: "#6B7280",
    fontSize: 16,
    fontWeight: "600",
  },
  helpText: {
    fontSize: 14,
    color: "#9CA3AF",
    textAlign: "center",
    lineHeight: 20,
  },
});
