import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

interface LoadingOverlayProps {
  textColor: string;
  backgroundColor: string;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  textColor,
  backgroundColor,
}) => {
  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <View style={styles.loadingContainer}>
        <Text style={[styles.loadingText, { color: textColor }]}>
          Loading profile...
        </Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: 18,
    fontWeight: "bold",
  },
});

export default LoadingOverlay;
