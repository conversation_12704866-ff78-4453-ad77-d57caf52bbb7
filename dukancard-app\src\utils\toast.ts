export type ToastType = 'success' | 'error' | 'warning' | 'info';

// Global toast instance - will be set by the ToastProvider
let globalToastInstance: any = null;

export const setGlobalToastInstance = (instance: any) => {
  globalToastInstance = instance;
};

/**
 * Toast utility that uses the proper Toast component
 * Falls back to console logging if toast provider is not available
 */
export class Toast {
  static show(message: string, type: ToastType = 'info') {
    if (globalToastInstance) {
      switch (type) {
        case 'success':
          globalToastInstance.success(message);
          break;
        case 'error':
          globalToastInstance.error(message);
          break;
        case 'warning':
          globalToastInstance.warning(message);
          break;
        case 'info':
        default:
          globalToastInstance.info(message);
          break;
      }
    } else {
      // Fallback to console logging if toast provider is not available
    }
  }

  static success(message: string) {
    this.show(message, 'success');
  }

  static error(message: string) {
    this.show(message, 'error');
  }

  static warning(message: string) {
    this.show(message, 'warning');
  }

  static info(message: string) {
    this.show(message, 'info');
  }
}
