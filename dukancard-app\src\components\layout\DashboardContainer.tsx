import { useTheme } from '@/src/hooks/useTheme';
import React from 'react';
import {
    KeyboardAvoidingView,
    Platform,
    Pressable,
    ScrollView,
    StyleSheet,
    Text,
    View,
    ViewStyle
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface DashboardContainerProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  headerActions?: React.ReactNode;
  scrollable?: boolean;
  showHeader?: boolean;
  showKeyboardAvoidingView?: boolean;
  contentContainerStyle?: ViewStyle;
  style?: ViewStyle;
}

/**
 * DashboardContainer - Layout component for dashboard screens
 * Provides consistent header, navigation, and content layout
 */
export function DashboardContainer({
  children,
  title,
  subtitle,
  headerActions,
  scrollable = true,
  showHeader = true,
  showKeyboardAvoidingView = false,
  contentContainerStyle,
  style,
}: DashboardContainerProps) {
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  const styles = StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    scrollView: {
      flex: 1,
    },
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.lg,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      backgroundColor: theme.colors.card,
    },
    headerContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    headerText: {
      flex: 1,
    },
    title: {
      fontSize: theme.typography.fontSize.xxl,
      fontWeight: theme.typography.fontWeight.bold,
      color: theme.colors.textPrimary,
      marginBottom: theme.spacing.xs,
    },
    subtitle: {
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.textSecondary,
      lineHeight: theme.typography.lineHeight.normal * theme.typography.fontSize.base,
    },
    headerActions: {
      marginLeft: theme.spacing.md,
    },
    content: {
      flex: 1,
    },
    scrollContainer: {
      flexGrow: 1,
    },
    contentContainer: {
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.lg,
    },
    nonScrollableContent: {
      flex: 1,
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.lg,
    },
  });

  const content = (
    <View style={styles.content}>
      {scrollable ? (
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={[styles.contentContainer, contentContainerStyle]}>
            {children}
          </View>
        </ScrollView>
      ) : (
        <View style={[styles.nonScrollableContent, contentContainerStyle]}>
          {children}
        </View>
      )}
    </View>
  );

  const mainContent = (
    <View style={[styles.container, style]}>
      {/* Header */}
      {showHeader && (
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <View style={styles.headerText}>
              {title && <Text style={styles.title}>{title}</Text>}
              {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
            </View>
            {headerActions && (
              <View style={styles.headerActions}>
                {headerActions}
              </View>
            )}
          </View>
        </View>
      )}

      {/* Main content */}
      {content}
    </View>
  );

  // Note: DashboardContainer should not include safe area since dashboard layouts handle it
  const safeAreaStyle = {
    paddingTop: insets.top,
    paddingBottom: insets.bottom,
    paddingLeft: insets.left,
    paddingRight: insets.right,
  };

  if (showKeyboardAvoidingView) {
    return (
      <View style={[styles.safeArea, safeAreaStyle]}>
        <KeyboardAvoidingView
          style={styles.keyboardAvoidingView}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
        >
          {mainContent}
        </KeyboardAvoidingView>
      </View>
    );
  }

  return (
    <View style={[styles.safeArea, safeAreaStyle]}>
      {mainContent}
    </View>
  );
}

/**
 * DashboardCard - Card component for dashboard content sections
 */
interface DashboardCardProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  actions?: React.ReactNode;
  style?: ViewStyle;
  onPress?: () => void;
}

export function DashboardCard({ 
  children, 
  title, 
  subtitle, 
  actions,
  style,
  onPress,
}: DashboardCardProps) {
  const theme = useTheme();
  
  const styles = StyleSheet.create({
    card: {
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.lg,
      marginVertical: theme.spacing.sm,
      ...theme.shadows.sm,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    pressableCard: {
      opacity: 1,
    },
    pressedCard: {
      opacity: 0.95,
      transform: [{ scale: 0.98 }],
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: theme.spacing.md,
    },
    headerText: {
      flex: 1,
    },
    title: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: theme.typography.fontWeight.semibold,
      color: theme.colors.textPrimary,
      marginBottom: theme.spacing.xs,
    },
    subtitle: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      lineHeight: theme.typography.lineHeight.normal * theme.typography.fontSize.sm,
    },
    actions: {
      marginLeft: theme.spacing.md,
    },
  });

  const cardContent = (
    <>
      {(title || subtitle || actions) && (
        <View style={styles.header}>
          <View style={styles.headerText}>
            {title && <Text style={styles.title}>{title}</Text>}
            {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
          </View>
          {actions && (
            <View style={styles.actions}>
              {actions}
            </View>
          )}
        </View>
      )}
      {children}
    </>
  );

  if (onPress) {
    return (
      <Pressable
        style={({ pressed }) => [
          styles.card,
          styles.pressableCard,
          pressed && styles.pressedCard,
          style,
        ]}
        onPress={onPress}
      >
        {cardContent}
      </Pressable>
    );
  }

  return (
    <View style={[styles.card, style]}>
      {cardContent}
    </View>
  );
}

/**
 * DashboardSection - Section component for grouping related dashboard content
 */
interface DashboardSectionProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  actions?: React.ReactNode;
  style?: ViewStyle;
}

export function DashboardSection({ 
  children, 
  title, 
  subtitle, 
  actions,
  style,
}: DashboardSectionProps) {
  const theme = useTheme();
  
  const styles = StyleSheet.create({
    section: {
      marginVertical: theme.spacing.md,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: theme.spacing.md,
      paddingHorizontal: theme.spacing.sm,
    },
    headerText: {
      flex: 1,
    },
    title: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: theme.typography.fontWeight.bold,
      color: theme.colors.textPrimary,
      marginBottom: theme.spacing.xs,
    },
    subtitle: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      lineHeight: theme.typography.lineHeight.normal * theme.typography.fontSize.sm,
    },
    actions: {
      marginLeft: theme.spacing.md,
    },
  });

  return (
    <View style={[styles.section, style]}>
      {(title || subtitle || actions) && (
        <View style={styles.header}>
          <View style={styles.headerText}>
            {title && <Text style={styles.title}>{title}</Text>}
            {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
          </View>
          {actions && (
            <View style={styles.actions}>
              {actions}
            </View>
          )}
        </View>
      )}
      {children}
    </View>
  );
}
