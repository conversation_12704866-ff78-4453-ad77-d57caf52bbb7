import { supabase } from '@/lib/supabase';
import { useCallback, useState } from 'react';

export interface PincodeDetails {
  city: string;
  state: string;
  localities: string[];
}

export interface UsePincodeDetailsOptions {
  onPincodeChange?: (details: PincodeDetails | null) => void;
}

export interface UsePincodeDetailsReturn {
  isPincodeLoading: boolean;
  availableLocalities: string[];
  handlePincodeChange: (pincode: string) => Promise<void>;
}

/**
 * Get pincode details from Supabase - exact same logic as Next.js version
 */
async function getPincodeDetails(pincode: string): Promise<{
  data?: {
    city: string;
    state: string;
    localities: string[];
  };
  city?: string;
  state?: string;
  localities?: string[];
  error?: string;
}> {
  if (!pincode || !/^\d{6}$/.test(pincode)) {
    return { error: "Invalid Pincode format." };
  }

  try {
    // First get city and state from pincodes table
    const { data: pincodeData, error: pincodeError } = await supabase
      .from("pincodes")
      .select("OfficeName, DivisionName, StateName")
      .eq("Pincode", pincode) // Updated column name to match database
      .order("OfficeName");

    if (pincodeError) {
      console.error("Pincode Fetch Error:", pincodeError);
      return { error: "Database error fetching pincode details." };
    }

    if (!pincodeData || pincodeData.length === 0) {
      return { error: "Pincode not found." };
    }

    // State names are already in title case format in the database
    const state = pincodeData[0].StateName;

    // Use DivisionName as the city (already cleaned)
    const city = pincodeData[0].DivisionName;

    // Get unique localities from post office names
    const localities = [
      ...new Set(pincodeData.map((item: any) => item.OfficeName)),
    ] as string[];

    return {
      data: { city, state, localities },
      city,
      state,
      localities
    };
  } catch (e) {
    console.error("Pincode Lookup Exception:", e);
    return { error: "An unexpected error occurred during pincode lookup." };
  }
}

export function usePincodeDetails(options: UsePincodeDetailsOptions = {}): UsePincodeDetailsReturn {
  const [isPincodeLoading, setIsPincodeLoading] = useState(false);
  const [availableLocalities, setAvailableLocalities] = useState<string[]>([]);

  const handlePincodeChange = useCallback(async (pincode: string) => {
    if (pincode.length !== 6) {
      setAvailableLocalities([]);
      options.onPincodeChange?.(null);
      return;
    }

    setIsPincodeLoading(true);
    setAvailableLocalities([]);

    try {
      const result = await getPincodeDetails(pincode);

      if (result.error) {
        console.error('Pincode error:', result.error);
        setAvailableLocalities([]);
        options.onPincodeChange?.(null);
      } else if (result.city && result.state && result.localities) {
        setAvailableLocalities(result.localities);
        options.onPincodeChange?.({
          city: result.city,
          state: result.state,
          localities: result.localities
        });
      } else {
        // Handle unknown pincode
        setAvailableLocalities([]);
        options.onPincodeChange?.(null);
      }
    } catch (error) {
      console.error('Error fetching pincode details:', error);
      setAvailableLocalities([]);
      options.onPincodeChange?.(null);
    } finally {
      setIsPincodeLoading(false);
    }
  }, [options]);

  return {
    isPincodeLoading,
    availableLocalities,
    handlePincodeChange,
  };
}
