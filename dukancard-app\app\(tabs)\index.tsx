import { Image } from 'expo-image';
import { Alert, Platform, TouchableOpacity, StyleSheet } from 'react-native';

import { HelloWave } from '@/src/components/HelloWave';
import ParallaxScrollView from '@/src/components/ParallaxScrollView';
import { ThemedText } from '@/src/components/ThemedText';
import { ThemedView } from '@/src/components/ThemedView';
import { useAuth } from '@/src/contexts/AuthContext';
import { getUserDisplayName } from '@/lib/supabase';
import { useTheme } from '@/src/hooks/useTheme';
import { responsiveFontSize } from '@/lib/theme/colors';

export default function HomeScreen() {
  const { user, signOut } = useAuth();
  const theme = useTheme();

  const styles = StyleSheet.create({
    headerImageContainer: {
      height: responsiveFontSize(178),
      width: responsiveFontSize(290),
      position: 'absolute',
      bottom: 0,
      left: 0,
      backgroundColor: theme.colors.secondary,
      borderRadius: theme.borderRadius.md,
      alignItems: 'center',
      justifyContent: 'center',
    },
    headerTitle: {
      color: theme.colors.primary,
    },
    welcomeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.xs,
    },
    stepContainer: {
      gap: theme.spacing.xs,
      marginBottom: theme.spacing.xs,
    },
    authContainer: {
      gap: theme.spacing.xs,
      marginBottom: theme.spacing.xl,
    },
    signOutButton: {
      backgroundColor: theme.colors.error,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      marginTop: theme.spacing.md,
    },
    signOutText: {
      color: theme.colors.errorForeground,
      textAlign: 'center',
      fontWeight: '600',
    },
  });

  const handleSignOut = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            const { error } = await signOut();
            if (error) {
              Alert.alert('Error', 'Failed to sign out. Please try again.');
            }
          },
        },
      ]
    );
  };
  return (
    <ParallaxScrollView
      headerBackgroundColor={{ light: '#A1CEDC', dark: '#1D3D47' }}
      headerImage={
        <ThemedView style={styles.headerImageContainer}>
          <ThemedText type="title" style={styles.headerTitle}>Dukancard</ThemedText>
        </ThemedView>
      }>
      <ThemedView style={styles.welcomeContainer}>
        <ThemedText type="title">Welcome, {getUserDisplayName(user)}!</ThemedText>
        <HelloWave />
      </ThemedView>
      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Step 1: Try it</ThemedText>
        <ThemedText>
          Edit <ThemedText type="defaultSemiBold">app/(tabs)/index.tsx</ThemedText> to see changes.
          Press{' '}
          <ThemedText type="defaultSemiBold">
            {Platform.select({
              ios: 'cmd + d',
              android: 'cmd + m',
              web: 'F12',
            })}
          </ThemedText>{' '}
          to open developer tools.
        </ThemedText>
      </ThemedView>
      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Step 2: Explore</ThemedText>
        <ThemedText>
          {`Tap the Explore tab to learn more about what's included in this starter app.`}
        </ThemedText>
      </ThemedView>
      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Step 3: Get a fresh start</ThemedText>
        <ThemedText>
          {`When you're ready, run `}
          <ThemedText type="defaultSemiBold">npm run reset-project</ThemedText> to get a fresh{' '}
          <ThemedText type="defaultSemiBold">app</ThemedText> directory. This will move the current{' '}
          <ThemedText type="defaultSemiBold">app</ThemedText> to{' '}
          <ThemedText type="defaultSemiBold">app-example</ThemedText>.
        </ThemedText>
      </ThemedView>
      <ThemedView style={styles.authContainer}>
        <ThemedText type="subtitle">Authentication</ThemedText>
        <ThemedText>
          You are logged in as: <ThemedText type="defaultSemiBold">{user?.email}</ThemedText>
        </ThemedText>
        <TouchableOpacity
          onPress={handleSignOut}
          style={styles.signOutButton}
        >
          <ThemedText style={styles.signOutText}>
            Sign Out
          </ThemedText>
        </TouchableOpacity>
      </ThemedView>
    </ParallaxScrollView>
  );
}

