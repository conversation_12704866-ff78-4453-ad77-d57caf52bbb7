# 🔐 Google OAuth Setup for Dukancard App (2025 Guide)

## 📋 Prerequisites
- Production keystore generated ✅
- SHA-1 and SHA-256 fingerprints extracted ✅
- React Native Google Sign-In library configured ✅

## 🆕 2025 Updates
Based on the latest React Native and Expo documentation:
- Uses `@react-native-google-signin/google-signin` with Expo config plugins
- No Firebase required - direct Google Cloud Console integration
- Proper Android App Bundle (AAB) generation for Play Store
- Environment-based configuration for dev/prod builds

## 🚀 Step-by-Step Google OAuth Setup

### Step 1: Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "Select a project" → "New Project"
3. **Project Name**: `Dukancard App`
4. **Project ID**: `dukancard-app-[random-id]` (will be auto-generated)
5. Click "Create"

### Step 2: Enable Google Sign-In API
1. In your new project, go to "APIs & Services" → "Library"
2. Search for "Google Sign-In API" or "Google+ API"
3. Click on "Google Sign-In API"
4. Click "Enable"

### Step 3: Configure OAuth Consent Screen
1. Go to "APIs & Services" → "OAuth consent screen" 
2. Choose "External" (for public app)
3. Fill in the required information:
   - **App name**: `Dukancard`
   - **User support email**: [Your email]
   - **App logo**: Upload your app icon (optional)
   - **App domain**: `https://dukancard.in`
   - **Developer contact information**: [Your email]
4. Click "Save and Continue"
5. **Scopes**: Add these scopes:
   - `../auth/userinfo.email`
   - `../auth/userinfo.profile`
   - `openid`
6. Click "Save and Continue"
7. **Test users**: Add your email for testing
8. Click "Save and Continue"

### Step 4: Create OAuth 2.0 Client IDs

#### For Android (Production):
1. Go to "APIs & Services" → "Credentials"
2. Click "Create Credentials" → "OAuth 2.0 Client IDs"
3. **Application type**: `Android`
4. **Name**: `Dukancard Android Production`
5. **Package name**: `com.dukancardapp.dukancard`
6. **SHA-1 certificate fingerprint**: `67:AA:47:E7:6B:0C:FA:4A:53:4A:82:2C:70:DA:6F:46:1D:81:7F:8D`
7. Click "Create"
8. **Save the Client ID** (will be used in app.config.js)

#### For Web (Backend):
1. Click "Create Credentials" → "OAuth 2.0 Client IDs"
2. **Application type**: `Web application`
3. **Name**: `Dukancard Web Client`
4. **Authorized JavaScript origins**: 
   - `https://dukancard.in`
   - `http://localhost:3000` (for development)
5. **Authorized redirect URIs**:
   - `https://dukancard.in/auth/callback`
6. Click "Create"
7. **Save the Client ID** (will be used in app.config.js)

## 📝 Generated Credentials ✅ CONFIGURED

### iOS Client:
```
Client ID: 110991972471-9g5hbuj78s88b5fi0m97i4k0823v7430.apps.googleusercontent.com
Bundle ID: com.dukancardapp.dukancard
```

### Android Development Client:
```
Client ID: 110991972471-jskkg6qg8g33mk1qrv9el5u0bj0f8cql.apps.googleusercontent.com
SHA-1: 5E:8F:16:06:2E:A3:CD:2C:4A:0D:54:78:76:BA:A6:F3:8C:AB:F6:25
```

### Android Production Client:
```
Client ID: 110991972471-bauq8cmll9nlrdl6ok7svvt5rgfuhno7.apps.googleusercontent.com
SHA-1: 67:AA:47:E7:6B:0C:FA:4A:53:4A:82:2C:70:DA:6F:46:1D:81:7F:8D
```

### Web Client:
```
Client ID: 110991972471-ek9ra016ca12ucaobil2s8oid86k6to4.apps.googleusercontent.com
```

## 🔧 App Configuration ✅ UPDATED

### Expo Config Plugin (iOS URL Scheme):
```javascript
plugins: [
  [
    "@react-native-google-signin/google-signin",
    {
      iosUrlScheme: "com.googleusercontent.apps.110991972471-9g5hbuj78s88b5fi0m97i4k0823v7430",
    },
  ],
],
```

### Environment Variables:
```javascript
extra: {
  googleIosClientId: process.env.EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID,
  googleWebClientId: process.env.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID,
  googleAndroidClientId: process.env.EXPO_PUBLIC_GOOGLE_ANDROID_CLIENT_ID,
  // ... other config
},
```

### Environment Files:
- **Development**: `.env` - Uses development Android client ID
- **Production**: `.env.production` - Uses production Android client ID
- **iOS**: Uses same iOS client ID for both dev/prod environments

## 🧪 Testing OAuth Setup

### Development Testing:
1. Use existing development credentials for local testing
2. Production credentials will only work with signed APK

### Production Testing (React Native):
1. Build production AAB: `npx react-native build-android --mode=release`
2. Alternative APK: `cd android && ./gradlew assembleRelease`
3. Test APK: `npm run android -- --mode="release"`
4. AAB location: `android/app/build/outputs/bundle/release/app-release.aab`
5. Test Google Sign-In functionality with production credentials

## ⚠️ Important Notes

1. **Development vs Production**: 
   - Development uses debug keystore SHA-1
   - Production uses upload keystore SHA-1
   - Both need separate OAuth client IDs

2. **SHA-1 Fingerprints**:
   - Debug SHA-1: `[Extract from debug keystore if needed]`
   - Production SHA-1: `67:AA:47:E7:6B:0C:FA:4A:53:4A:82:2C:70:DA:6F:46:1D:81:7F:8D`

3. **Client ID Usage**:
   - Android Client ID: Used by React Native Google Sign-In
   - Web Client ID: Used by Supabase for server-side verification

4. **Security**:
   - Never commit client secrets to git
   - Use environment variables for sensitive data
   - Web client secret should only be used on server-side

## 📋 Checklist

- [ ] Create Google Cloud Project
- [ ] Enable Google Sign-In API
- [ ] Configure OAuth consent screen
- [ ] Create Android OAuth client ID
- [ ] Create Web OAuth client ID
- [ ] Update app.config.js with new credentials
- [ ] Test with development build
- [ ] Test with production signed APK
- [ ] Verify Google Sign-In works in production

## � 2025 Best Practices

### For React Native with Expo:
1. **Use Environment Variables**: Store client IDs in `.env` files for better security
2. **Separate Dev/Prod Configs**: Use different Android client IDs for development and production
3. **Config Plugins**: Use `@react-native-google-signin/google-signin` with Expo config plugins
4. **No Firebase Required**: You can use Google Cloud Console directly without Firebase
5. **SHA-1 Management**: Keep track of both debug and production SHA-1 fingerprints

### Build Process (Official React Native):
1. **AAB for Play Store**: Use `npx react-native build-android --mode=release`
2. **APK for Testing**: Use `npm run android -- --mode="release"`
3. **Keystore Management**: Store keystore in `android/app/` directory
4. **Gradle Variables**: Use `gradle.properties` for secure credential storage

### Security Considerations:
- ✅ Client IDs are safe to expose (they're public by design)
- ✅ Service admin keys should never be in client-side code
- ✅ Use environment variables for different build configurations
- ✅ Production builds should use production client IDs
- ✅ Keep keystore file private and backed up securely

## 🔗 Useful Links (2025)

- [React Native Publishing to Google Play Store](https://reactnative.dev/docs/signed-apk-android) - Official React Native docs
- [Expo Google Authentication Guide](https://docs.expo.dev/guides/google-authentication/) - Updated April 2025
- [React Native Google Sign-In Expo Setup](https://react-native-google-signin.github.io/docs/setting-up/expo)
- [Google Cloud Console](https://console.cloud.google.com/)
- [Supabase Auth with Google](https://supabase.com/docs/guides/auth/social-login/auth-google)
