import { supabase } from '@/lib/supabase';

export class CustomerPostService {
  static async getCustomerProfileById(userId: string) {
    return await supabase
      .from('customer_profiles')
      .select('id, name, avatar_url, city_slug, state_slug, locality_slug, pincode')
      .eq('id', userId)
      .single();
  }

  static async insertCustomerPost(postData: any) {
    return await supabase
      .from('customer_posts')
      .insert(postData)
      .select()
      .single();
  }

  static async getCustomerPostByIdAndUserId(postId: string, userId: string) {
    return await supabase
      .from('customer_posts')
      .select('id, customer_id, created_at, image_url')
      .eq('id', postId)
      .eq('customer_id', userId)
      .single();
  }

  static async updateCustomerPost(postId: string, updateData: any) {
    return await supabase
      .from('customer_posts')
      .update(updateData)
      .eq('id', postId)
      .select()
      .single();
  }

  static async deleteCustomerPost(postId: string) {
    return await supabase
      .from('customer_posts')
      .delete()
      .eq('id', postId);
  }

  static async getCustomerPosts(userId: string, limit: number, offset: number) {
    return await supabase
      .from('customer_posts')
      .select(`
        *,
        customer_profiles!inner(
          id,
          name,
          avatar_url
        )
      `)
      .eq('customer_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);
  }
}
