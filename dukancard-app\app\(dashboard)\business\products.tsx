import { ScrollView, View } from 'react-native';
import { ThemedText } from '@/src/components/ThemedText';
import { ThemedView } from '@/src/components/ThemedView';
import { Button } from '@/src/components/ui/Button';
import { createProductsStyles } from '@/styles/dashboard/business/products-styles';

export default function BusinessProductsScreen() {
  const styles = createProductsStyles();

  return (
    <ThemedView style={styles.container}>
      <ScrollView contentContainerStyle={styles.content}>
        <View style={styles.header}>
          <ThemedText type="title">Products & Services</ThemedText>
          <ThemedText style={styles.subtitle}>
            Manage your offerings
          </ThemedText>
        </View>

        <Button
          title="Add New Product"
          style={styles.addButton}
          onPress={() => {}}
        />

        <View style={styles.productsContainer}>
          <View style={styles.productCard}>
            <ThemedText type="defaultSemiBold" style={styles.productName}>
              Premium Service Package
            </ThemedText>
            <ThemedText style={styles.productPrice}>₹2,999</ThemedText>
            <ThemedText style={styles.productDescription}>
              Complete business solution with premium features
            </ThemedText>
          </View>

          <View style={styles.productCard}>
            <ThemedText type="defaultSemiBold" style={styles.productName}>
              Basic Consultation
            </ThemedText>
            <ThemedText style={styles.productPrice}>₹999</ThemedText>
            <ThemedText style={styles.productDescription}>
              One-hour consultation session
            </ThemedText>
          </View>

          <View style={styles.productCard}>
            <ThemedText type="defaultSemiBold" style={styles.productName}>
              Digital Marketing Package
            </ThemedText>
            <ThemedText style={styles.productPrice}>₹4,999</ThemedText>
            <ThemedText style={styles.productDescription}>
              Complete digital marketing solution
            </ThemedText>
          </View>
        </View>

        <View style={styles.emptyState}>
          <ThemedText style={styles.emptyText}>
            🛍️ Product management features will be implemented here
          </ThemedText>
        </View>
      </ScrollView>
    </ThemedView>
  );
}


