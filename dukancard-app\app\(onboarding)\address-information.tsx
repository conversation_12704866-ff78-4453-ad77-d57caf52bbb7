import LocalityBottomSheetPicker, { LocalityBottomSheetPickerRef } from '@/src/components/pickers/LocalityBottomSheetPicker';
import { OnboardingContainer } from '@/src/components/layout/OnboardingContainer';
import { LocationPicker } from '@/src/components/ui/LocationPicker';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { usePincodeDetails } from '@/src/hooks/usePincodeDetails';
import { useTheme } from '@/src/hooks/useTheme';

import { router, useLocalSearchParams } from 'expo-router';
import { Building2, Home, MapPin, Loader2 } from 'lucide-react-native';
import React, { useEffect, useRef, useState } from 'react';
import {
  Alert,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { addressInformationSchema } from '@/src/utils/validationSchemas';
import { getOnboardingData, saveOnboardingData } from '@/backend/supabase/services/common/onboardingService';
import { FormField } from '@/src/components/forms/FormField';
import { FormPicker } from '@/src/components/forms/FormPicker';
import { Input } from '@/src/components/ui/Input';
import { useLocationPermission } from '@/src/hooks/useLocationPermission';

// Create a form-specific type that matches the Yup schema
interface FormData {
  addressLine: string;
  pincode: string;
  locality: string;
  city: string;
  state: string;
  businessStatus: 'online' | 'offline';
  latitude: number;
  longitude: number;
}

// Partial type for initial form data (before GPS coordinates are set)
type InitialFormData = Omit<FormData, 'latitude' | 'longitude'> & {
  latitude?: number;
  longitude?: number;
};

export default function AddressInformationScreen() {
  const colorScheme = useColorScheme();
  const theme = useTheme();
  const params = useLocalSearchParams<{
    businessName?: string;
    businessCategory?: string;
    memberName?: string;
    establishedYear?: string;
    title?: string;
    phone?: string;
    businessSlug?: string;
    redirect?: string;
    message?: string;
  }>();

  const [initialFormData, setInitialFormData] = useState<InitialFormData>({
    addressLine: '',
    pincode: '',
    locality: '',
    city: '',
    state: '',
    businessStatus: 'online',
    latitude: undefined,
    longitude: undefined,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [gpsDetectedLocality, setGpsDetectedLocality] = useState<string | null>(null);
  const [hasGpsCoordinates, setHasGpsCoordinates] = useState(false);

  // Location permission hook
  const { permission: locationPermission } = useLocationPermission();

  // React Hook Form setup
  const {
    control,
    handleSubmit,
    formState: { isValid, errors },
    reset: resetForm,
    watch,
    setValue,
    trigger,
  } = useForm<FormData>({
    resolver: yupResolver(addressInformationSchema) as any,
    defaultValues: {
      addressLine: '',
      pincode: '',
      locality: '',
      city: '',
      state: '',
      businessStatus: 'online', // Explicitly set default value here
    } as Partial<FormData>, // Use partial since latitude/longitude will be set later
    mode: 'onChange', // Validate on change for better UX, consistent with other screens
    reValidateMode: 'onChange', // Ensure validation runs properly
  });





  // Refs for bottom sheet pickers
  const localityPickerRef = useRef<LocalityBottomSheetPickerRef>(null);

  const isDark = colorScheme === 'dark';

  // Load existing onboarding data on component mount
  useEffect(() => {
    const loadExistingData = async () => {
      try {
        const existingData = await getOnboardingData();
        if (existingData) {
          setInitialFormData(prev => ({
            ...prev,
            addressLine: existingData.addressLine || prev.addressLine,
            pincode: existingData.pincode || prev.pincode,
            locality: existingData.locality || prev.locality,
            city: existingData.city || prev.city,
            state: existingData.state || prev.state,
            latitude: existingData.latitude || prev.latitude,
            longitude: existingData.longitude || prev.longitude,
          }));

          // Check if GPS coordinates are already available
          if (existingData.latitude && existingData.longitude) {
            setHasGpsCoordinates(true);
          }
        }
      } catch (error) {
        console.error('Error loading existing onboarding data:', error);
      }
    };

    loadExistingData();
  }, []);

  // Reset form when initial data changes
  useEffect(() => {
    resetForm({
      ...initialFormData,
      businessStatus: initialFormData.businessStatus || 'online', // Ensure businessStatus is always set
    });
  }, [initialFormData, resetForm]);





  // Use pincode details hook
  const {
    isPincodeLoading,
    availableLocalities,
    handlePincodeChange: fetchPincodeDetails
  } = usePincodeDetails({
    onPincodeChange: (details) => {
      if (details && details.city && details.state) {
        setValue('city', details.city);
        setValue('state', details.state);
        setValue('locality', ''); // Reset locality when pincode changes
        setInitialFormData(prev => ({
          ...prev,
          pincode: watch('pincode'), // Preserve the current pincode
          addressLine: watch('addressLine'), // Preserve the current address line
          city: details.city,
          state: details.state,
          locality: '', // Reset locality when pincode changes
          latitude: watch('latitude'), // Preserve the GPS coordinates
          longitude: watch('longitude'), // Preserve the GPS coordinates
        }));
      }
    }
  });

  // Auto-select GPS-detected locality when localities are fetched
  useEffect(() => {
    if (gpsDetectedLocality && availableLocalities.length > 0) {
      // Find the matching locality in the fetched list
      const matchingLocality = availableLocalities.find(
        locality => locality.toLowerCase().trim() === gpsDetectedLocality.toLowerCase().trim()
      );

      if (matchingLocality) {
        setValue('locality', matchingLocality);
        setGpsDetectedLocality(null); // Clear the stored value

        // Trigger validation after auto-selection
        setTimeout(() => {
          trigger();
        }, 100);
      }
    }
  }, [availableLocalities, gpsDetectedLocality, setValue, trigger]);

  // Handle pincode input changes
  const handlePincodeInputChange = (value: string) => {
    const cleanedPincode = value.replace(/\D/g, '').substring(0, 6);
    setValue('pincode', cleanedPincode);
    if (cleanedPincode.length === 6) {
      fetchPincodeDetails(cleanedPincode);
    }
  };

  // Handle locality selection
  const handleLocalitySelect = (locality: string) => {
    setValue('locality', locality);
    // Trigger validation to ensure form recognizes all filled fields including business status
    setTimeout(() => {
      trigger();
    }, 100);
  };

  const handleFormSubmit = async (values: FormData) => {
    setIsLoading(true);

    try {
      
      await saveOnboardingData(values);

      // Navigate to next step with all form data
      let nextUrl = '/(onboarding)/plan-selection';
      const searchParams = new URLSearchParams();

      // Pass previous step data
      if (params.businessName) searchParams.append('businessName', params.businessName);
      if (params.businessCategory) searchParams.append('businessCategory', params.businessCategory);
      if (params.memberName) searchParams.append('memberName', params.memberName);
      if (params.establishedYear) searchParams.append('establishedYear', params.establishedYear);
      if (params.title) searchParams.append('title', params.title);
      if (params.phone) searchParams.append('phone', params.phone);
      if (params.businessSlug) searchParams.append('businessSlug', params.businessSlug);

      // Pass current form data, including coordinates
      Object.entries(values).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });

      // Pass redirect parameters if available
      if (params.redirect) {
        searchParams.append('redirect', params.redirect);
      }

      if (params.message) {
        searchParams.append('message', params.message);
      }

      if (searchParams.toString()) {
        nextUrl += `?${searchParams.toString()}`;
      }

      router.push(nextUrl);
    } catch (error) {
      console.error('Navigation error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };





  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <StatusBar
        style={theme.isDark ? 'light' : 'dark'}
        backgroundColor={theme.colors.background}
      />
      <OnboardingContainer
        currentStep={3}
        totalSteps={4}
        onBack={() => router.back()}
        onNext={handleSubmit(handleFormSubmit)}
        nextButtonText={isLoading ? "Loading..." : "Continue"}
        isNextDisabled={isLoading || !isValid}
        showProgress={false}
      >
              <View style={{ gap: theme.spacing.md, paddingHorizontal: theme.spacing.xs }}>
                <View style={{ alignItems: 'center', marginBottom: theme.spacing.lg }}>
                  <Text style={{
                    fontSize: theme.typography.fontSize.xxl,
                    fontWeight: '700',
                    color: theme.colors.textPrimary,
                    textAlign: 'center',
                    marginBottom: theme.spacing.xs,
                  }}>
                    Address Information
                  </Text>
                  <Text style={{
                    fontSize: theme.typography.fontSize.base,
                    color: theme.colors.textSecondary,
                    textAlign: 'center',
                    lineHeight: theme.typography.lineHeight.normal * theme.typography.fontSize.base,
                  }}>
                    Where is your business located?
                  </Text>
                  <Text style={{
                    fontSize: theme.typography.fontSize.xs,
                    color: theme.colors.textSecondary,
                    textAlign: 'center',
                    lineHeight: 16,
                    marginTop: theme.spacing.xs,
                  }}>
                    Use GPS auto-detection or manually enter your address details
                  </Text>
                </View>

          {/* Address Line Field */}
          <FormField
            control={control}
            name="addressLine"
            label="Address Line"
            placeholder="e.g., 123 Main Street, Building A"
            leftIcon={<Home size={20} color="#D4AF37" />}
            keyboardType="default"
            autoCapitalize="words"
            autoComplete="off"
            multiline
            numberOfLines={2}
          />

          {/* Location Picker */}
          <LocationPicker
            onLocationDetected={(latitude, longitude) => {
              setValue('latitude', latitude);
              setValue('longitude', longitude);
              setHasGpsCoordinates(true);
              // Trigger validation to ensure form recognizes the coordinates
              trigger(['latitude', 'longitude']);
            }}
            onAddressDetected={(address) => {
              // Auto-populate form fields with detected address
              setValue('pincode', address.pincode);
              setValue('city', address.city);
              setValue('state', address.state);

              // Store the GPS-detected locality name for auto-selection after localities are fetched
              setGpsDetectedLocality(address.locality);

              // Update initial form data to reflect the changes
              setInitialFormData(prev => ({
                ...prev,
                pincode: address.pincode,
                addressLine: watch('addressLine'), // Preserve the current address line
                city: address.city,
                state: address.state,
                locality: '', // Will be set after localities are fetched and matched
                latitude: watch('latitude'), // Preserve the GPS coordinates
                longitude: watch('longitude'), // Preserve the GPS coordinates
              }));

              // Fetch localities for the detected pincode - this will trigger auto-selection
              fetchPincodeDetails(address.pincode);

              // Trigger validation to ensure form recognizes all auto-filled fields
              setTimeout(() => {
                trigger();
              }, 200);
            }}
            onError={(error) => {

            }}
            disabled={isLoading || isPincodeLoading}
          />

          {/* Location Permission Message */}
          {locationPermission && !locationPermission.granted && !hasGpsCoordinates && (
            <View style={{
              marginTop: 8,
              padding: 12,
              backgroundColor: isDark ? '#FEF3C7' : '#FEF3C7',
              borderRadius: 8,
              borderLeftWidth: 4,
              borderLeftColor: '#F59E0B'
            }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <MapPin size={16} color="#92400E" style={{ marginRight: 6 }} />
                <Text style={{
                  fontSize: 14,
                  color: '#92400E',
                  fontWeight: '500'
                }}>
                  Location permission is required to continue
                </Text>
              </View>
              <Text style={{
                fontSize: 12,
                color: '#92400E',
                marginTop: 4,
                lineHeight: 16
              }}>
                Tap &ldquo;Use Current Location&rdquo; above and grant location permission to get your exact GPS coordinates. This ensures accurate business location for your customers.
              </Text>
            </View>
          )}

          {/* Pincode Field */}
          <View style={{ position: 'relative' }}>
            <Controller
              control={control}
              name="pincode"
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <Input
                  label="Pincode"
                  value={value || ''}
                  onChangeText={(text: string) => {
                    // Handle the form field change
                    onChange(text);
                    // Handle pincode lookup
                    handlePincodeInputChange(text);
                  }}
                  placeholder="Enter 6-digit pincode"
                  error={error?.message}
                  leftIcon={<MapPin size={20} color="#D4AF37" />}
                  rightIcon={isPincodeLoading ? <Loader2 size={20} color={theme.colors.primary} /> : undefined}
                  keyboardType="numeric"
                  autoCapitalize="none"
                  autoComplete="off"
                  maxLength={6}
                />
              )}
            />
          </View>

          {/* Locality Picker */}
          <FormPicker
            control={control}
            name="locality"
            label="Locality/Area"
            placeholder="Select your locality"
            options={availableLocalities.map(loc => ({ label: loc, value: loc }))}
            onPress={() => localityPickerRef.current?.present()}
            leftIcon={<Building2 size={20} color="#D4AF37" />}
          />

          {/* City and State Fields in a single row */}
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', gap: theme.spacing.md }}>
            <View style={{ flex: 1 }}>
              <FormField
                control={control}
                name="city"
                label="City"
                placeholder="Auto-filled from pincode"
                leftIcon={<Building2 size={20} color="#D4AF37" />}
                keyboardType="default"
                autoCapitalize="words"
                autoComplete="off"
                editable={false}
              />
            </View>
            <View style={{ flex: 1 }}>
              <FormField
                control={control}
                name="state"
                label="State"
                placeholder="Auto-filled from pincode"
                leftIcon={<MapPin size={20} color="#D4AF37" />}
                keyboardType="default"
                autoCapitalize="words"
                autoComplete="off"
                editable={false}
              />
            </View>
          </View>

          {/* Business Status Field */}
          <Controller
            control={control}
            name="businessStatus"
            render={({ field: { onChange, value } }) => (
              <View>
                <Text style={{
                  fontSize: theme.typography.fontSize.sm,
                  color: theme.colors.textSecondary,
                  marginBottom: theme.spacing.xs,
                }}>Business Status</Text>
                <View style={{ flexDirection: 'row', gap: theme.spacing.sm }}>
                  <TouchableOpacity
                    style={{
                      flex: 1,
                      paddingVertical: theme.spacing.md,
                      paddingHorizontal: theme.spacing.lg,
                      borderRadius: theme.spacing.sm,
                      backgroundColor: value === 'online' ? theme.colors.primary : theme.colors.card,
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderWidth: 1,
                      borderColor: value === 'online' ? theme.colors.primary : theme.colors.border,
                    }}
                    onPress={() => onChange('online')}
                  >
                    <Text style={{
                      color: value === 'online' ? theme.colors.textOnPrimary : theme.colors.textPrimary,
                      fontWeight: '600',
                      fontSize: theme.typography.fontSize.sm,
                    }}>Online</Text>
                    <Text style={{
                      color: value === 'online' ? theme.colors.textOnPrimary : theme.colors.textSecondary,
                      fontSize: theme.typography.fontSize.xs,
                      opacity: 0.75,
                      marginTop: 2,
                      textAlign: 'center',
                    }}>Ready to serve customers</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={{
                      flex: 1,
                      paddingVertical: theme.spacing.md,
                      paddingHorizontal: theme.spacing.lg,
                      borderRadius: theme.spacing.sm,
                      backgroundColor: value === 'offline' ? theme.colors.primary : theme.colors.card,
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderWidth: 1,
                      borderColor: value === 'offline' ? theme.colors.primary : theme.colors.border,
                    }}
                    onPress={() => onChange('offline')}
                  >
                    <Text style={{
                      color: value === 'offline' ? theme.colors.textOnPrimary : theme.colors.textPrimary,
                      fontWeight: '600',
                      fontSize: theme.typography.fontSize.sm,
                    }}>Offline</Text>
                    <Text style={{
                      color: value === 'offline' ? theme.colors.textOnPrimary : theme.colors.textSecondary,
                      fontSize: theme.typography.fontSize.xs,
                      opacity: 0.75,
                      marginTop: 2,
                      textAlign: 'center',
                    }}>Setting up business</Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          />
        </View>
      </OnboardingContainer>

      {/* Locality Bottom Sheet Picker */}
      <LocalityBottomSheetPicker
        ref={localityPickerRef}
        localities={availableLocalities}
        selectedLocality={watch('locality')}
        onLocalitySelect={handleLocalitySelect}
      />
    </GestureHandlerRootView>
  );
}
