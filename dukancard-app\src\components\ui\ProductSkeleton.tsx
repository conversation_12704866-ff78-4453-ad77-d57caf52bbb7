import React from 'react';
import { View } from 'react-native';
import { SkeletonLoader } from './SkeletonLoader';
import { createSkeletonLoaderStyles } from '@/styles/ui/SkeletonLoader-styles';
import { useColorScheme } from '@/src/hooks/useColorScheme';

// Enhanced Single Product Card Skeleton - Matches exact ProductCard design
export const ProductCardSkeleton: React.FC = () => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createSkeletonLoaderStyles(isDark);

  return (
    <View style={styles.productCard}>
      {/* Product Image Container - matches exact height and styling */}
      <View style={styles.productImageContainer}>
        <SkeletonLoader width="100%" height={140} borderRadius={8} />

        {/* Discount badge skeleton - positioned exactly like real badge */}
        <View style={styles.productBadgeContainer}>
          <SkeletonLoader width={32} height={24} borderRadius={6} />
        </View>
      </View>

      {/* Product Info - matches exact padding and layout */}
      <View style={styles.productCardContent}>
        {/* Product name - single line with ellipsis like real component */}
        <SkeletonLoader width="85%" height={12} style={{ marginBottom: 4 }} />

        {/* Price Container - matches exact layout */}
        <View style={styles.productCardPriceRow}>
          <View style={styles.productPriceContainer}>
            {/* Final price */}
            <SkeletonLoader width={45} height={12} style={{ marginRight: 6 }} />
            {/* Original price (strikethrough) */}
            <SkeletonLoader width={35} height={10} />
          </View>
        </View>
      </View>
    </View>
  );
};

// Product Grid Skeleton (for initial loading)
export const ProductGridSkeleton: React.FC = () => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createSkeletonLoaderStyles(isDark);

  return (
    <View style={styles.productGrid}>
      {[1, 2, 3, 4, 5, 6].map((item) => (
        <View key={item} style={styles.productGridItem}>
          <ProductCardSkeleton />
        </View>
      ))}
    </View>
  );
};

// Product Loading More Skeleton (for infinite scroll)
export const ProductLoadingMoreSkeleton: React.FC = () => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createSkeletonLoaderStyles(isDark);

  return (
    <View style={styles.loadMoreContainer}>
      <View style={styles.productRow}>
        <View style={styles.productGridItem}>
          <ProductCardSkeleton />
        </View>
        <View style={styles.productGridItem}>
          <ProductCardSkeleton />
        </View>
      </View>
    </View>
  );
};

// Single Product Page Skeleton
export const SingleProductSkeleton: React.FC = () => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createSkeletonLoaderStyles(isDark);

  return (
    <View style={styles.singleProduct}>
      {/* Header with back button and spacers (no title or share button) */}
      <View style={styles.singleProductHeader}>
        <SkeletonLoader width={24} height={24} borderRadius={12} />
        <View style={{ flex: 1 }} />
        <View style={{ flex: 1 }} />
      </View>

      <View style={styles.singleProductScrollContent}>
        {/* Product Image Carousel - Square aspect ratio */}
        <SkeletonLoader width="100%" height={375} borderRadius={0} style={{ marginBottom: 0 }} />

        {/* Product Info */}
        <View style={styles.singleProductInfoCard}>
          {/* Product Name */}
          <SkeletonLoader width="85%" height={24} style={{ marginBottom: 8 }} />

          {/* Price Container */}
          <View style={styles.singleProductPriceRow}>
            <SkeletonLoader width="25%" height={20} style={{ marginRight: 8 }} />
            <SkeletonLoader width="20%" height={16} />
          </View>

          {/* Description */}
          <View style={styles.singleProductDescription}>
            <SkeletonLoader width="100%" height={16} style={{ marginTop: 12 }} />
            <SkeletonLoader width="90%" height={16} style={{ marginTop: 6 }} />
            <SkeletonLoader width="75%" height={16} style={{ marginTop: 6 }} />
          </View>
        </View>

        {/* Variant Selector (optional) */}
        <View style={styles.singleProductVariants}>
          <SkeletonLoader width="30%" height={16} style={{ marginBottom: 12 }} />
          <View style={styles.singleProductVariantOptions}>
            <SkeletonLoader width={80} height={36} borderRadius={18} />
            <SkeletonLoader width={80} height={36} borderRadius={18} />
            <SkeletonLoader width={80} height={36} borderRadius={18} />
          </View>
        </View>

        {/* Business Info Card */}
        <View style={styles.singleProductBusinessCard}>
          <View style={styles.singleProductBusinessHeader}>
            <SkeletonLoader width={48} height={48} borderRadius={24} />
            <View style={styles.singleProductBusinessInfo}>
              <SkeletonLoader width="70%" height={18} />
              <SkeletonLoader width="50%" height={14} style={{ marginTop: 4 }} />
            </View>
          </View>
        </View>

        {/* Contact Buttons */}
        <View style={styles.singleProductContactButtons}>
          <SkeletonLoader width="48%" height={44} borderRadius={8} />
          <SkeletonLoader width="48%" height={44} borderRadius={8} />
        </View>

        {/* Share Button */}
        <View style={styles.singleProductShareButton}>
          <SkeletonLoader width="100%" height={44} borderRadius={8} />
        </View>

        {/* Ad Section */}
        <View style={styles.singleProductAdSection}>
          <SkeletonLoader width="100%" height={120} borderRadius={8} />
        </View>

        {/* Product Recommendations */}
        <View style={styles.singleProductRecommendations}>
          <SkeletonLoader width="50%" height={20} style={{ marginBottom: 16 }} />
          <View style={styles.singleProductRecommendationGrid}>
            <ProductCardSkeleton />
            <ProductCardSkeleton />
          </View>
        </View>
      </View>
    </View>
  );
};
