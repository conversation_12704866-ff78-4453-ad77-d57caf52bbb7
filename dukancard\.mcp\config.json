{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "disabled": false, "autoApprove": [], "transportType": "stdio", "timeout": 60}, "github.com/ahujasid/blender-mcp": {"command": "cmd", "args": ["/c", "uvx", "blender-mcp"], "disabled": false, "autoApprove": [], "transportType": "stdio", "timeout": 60}, "github.com/modelcontextprotocol/servers/tree/main/src/brave-search": {"autoApprove": [], "disabled": false, "timeout": 60, "command": "docker", "args": ["run", "-i", "--rm", "-e", "BRAVE_API_KEY", "mcp/brave-search"], "env": {"BRAVE_API_KEY": "BSAycjkqi-vgv4WjNqAv8FkLUI4omGp"}, "transportType": "stdio", "alwaysAllow": ["brave_web_search"]}, "github.com/modelcontextprotocol/servers/tree/main/src/github": {"autoApprove": [], "disabled": false, "timeout": 60, "command": "docker", "args": ["run", "-i", "--rm", "-e", "GITHUB_PERSONAL_ACCESS_TOKEN", "mcp/github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"}, "transportType": "stdio"}, "github.com/modelcontextprotocol/servers/tree/main/src/git": {"autoApprove": ["git_status", "git_add"], "disabled": false, "timeout": 60, "command": "py", "args": ["-m", "mcp_server_git", "--repository", "c:/Users/<USER>/OneDrive/Desktop/Code Projects/shopify-lugdi"], "transportType": "stdio", "alwaysAllow": ["git_status", "git_add", "git_commit"]}, "github.com/modelcontextprotocol/servers/tree/main/src/puppeteer": {"autoApprove": [], "disabled": false, "timeout": 60, "command": "docker", "args": ["run", "-i", "--rm", "--init", "-e", "DOCKER_CONTAINER=true", "mcp/puppeteer"], "transportType": "stdio"}, "github.com/modelcontextprotocol/servers/tree/main/src/sequentialthinking": {"autoApprove": [], "disabled": false, "timeout": 60, "command": "docker", "args": ["run", "--rm", "-i", "--init", "-e", "DOCKER_CONTAINER=true", "mcp/sequentialthinking"], "transportType": "stdio", "alwaysAllow": ["sequentialthinking"]}, "github.com/pashpashpash/shopify-mcp-server": {"autoApprove": [], "disabled": false, "timeout": 60, "command": "node", "args": ["C:\\Users\\<USER>\\OneDrive\\Documents\\Cline\\MCP\\shopify-mcp-server\\build\\index.js"], "env": {"SHOPIFY_ACCESS_TOKEN": "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", "MYSHOPIFY_DOMAIN": "a2rjzy-uz.myshopify.com"}, "transportType": "stdio"}, "shopify-dev-mcp": {"command": "cmd", "args": ["/k", "npx", "-y", "@shopify/dev-mcp@latest"], "disabled": false, "autoApprove": []}, "github.com/supabase-community/supabase-mcp": {"command": "cmd", "args": ["/c", "npx", "-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"], "disabled": false, "autoApprove": ["list_projects"], "alwaysAllow": ["list_tables", "list_projects", "apply_migration", "execute_sql"]}}}