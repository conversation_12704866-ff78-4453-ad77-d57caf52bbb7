import { StyleSheet } from 'react-native';

export const createThemedTextStyles = () => {
  return StyleSheet.create({
    default: {
      fontSize: 16,
      lineHeight: 24,
    },
    defaultSemiBold: {
      fontSize: 16,
      lineHeight: 24,
      fontWeight: '600',
    },
    title: {
      fontSize: 32,
      fontWeight: 'bold',
      lineHeight: 32,
    },
    subtitle: {
      fontSize: 20,
      fontWeight: 'bold',
    },
    link: {
      fontSize: 16,
      lineHeight: 30,
      color: '#C29D5B',
    },
  });
};
