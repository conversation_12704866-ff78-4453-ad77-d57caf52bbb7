import { useColorScheme } from '@/src/hooks/useColorScheme';
import { Image } from 'expo-image';
import { Camera, X } from 'lucide-react-native';
import React, { useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import ImagePickerBottomSheet, { ImagePickerBottomSheetRef } from '@/src/components/pickers/ImagePickerBottomSheet';
import { openCameraForAvatar, openGalleryForAvatar } from '@/backend/supabase/services/storage/avatarUploadService';
import * as ImageManipulator from 'expo-image-manipulator';


interface AvatarUploadProps {
  size?: number;
  showLabel?: boolean;
  disabled?: boolean;
  onImageUpload: (uri: string) => void; // New prop for handling uploaded image
  onImageDelete?: () => void;
  initialImageUri?: string;
}

export function AvatarUpload({
  size = 100,
  showLabel = true,
  disabled = false,
  onImageUpload,
  onImageDelete,
  initialImageUri,
}: AvatarUploadProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [selectedImageUri, setSelectedImageUri] = useState<string | null>(initialImageUri || null);
  const [isSelecting, setIsSelecting] = useState(false);
  const [imageToCrop, setImageToCrop] = useState<string | null>(null);

  const imagePickerRef = useRef<ImagePickerBottomSheetRef>(null);
  

  // Update selected image when initialImageUri changes
  useEffect(() => {
    setSelectedImageUri(initialImageUri || null);
  }, [initialImageUri]);

  const handleImagePickerPress = () => {
    if (disabled || isSelecting) return;
    imagePickerRef.current?.present();
  };

  const handleCameraSelection = async () => {
    try {
      setIsSelecting(true);
      const result = await openCameraForAvatar();
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const manipResult = await ImageManipulator.manipulateAsync(
          result.assets[0].uri,
          [{ resize: { width: 400, height: 400 } }],
          { compress: 0.8, format: ImageManipulator.SaveFormat.JPEG }
        );
        handleCropComplete(manipResult.uri);
      }
    } catch (error) {
      console.error('Camera error:', error);
    } finally {
      setIsSelecting(false);
      imagePickerRef.current?.dismiss();
    }
  };

  const handleGallerySelection = async () => {
    try {
      setIsSelecting(true);
      const result = await openGalleryForAvatar();
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const manipResult = await ImageManipulator.manipulateAsync(
          result.assets[0].uri,
          [{ resize: { width: 400, height: 400 } }],
          { compress: 0.8, format: ImageManipulator.SaveFormat.JPEG }
        );
        handleCropComplete(manipResult.uri);
      }
    } catch (error) {
      console.error('Gallery error:', error);
    } finally {
      setIsSelecting(false);
      imagePickerRef.current?.dismiss();
    }
  };

  const handleCropComplete = (croppedUri: string) => {
    setSelectedImageUri(croppedUri);
    onImageUpload(croppedUri);
    setImageToCrop(null);
  };

  const handleCropDialogClose = () => {
    setImageToCrop(null);
  };

  // Theme colors
  const textColor = isDark ? '#FFFFFF' : '#000000';
  const goldColor = '#D4AF37';
  const mutedTextColor = isDark ? '#9CA3AF' : '#6B7280';

  const avatarSize = size;

  return (
    <View style={styles.container}>
      {showLabel && (
        <Text style={[styles.label, { color: textColor }]}>
          Profile Picture
        </Text>
      )}

      <View style={styles.avatarContainer}>
        <TouchableOpacity
          style={[
            styles.avatarButton,
            {
              width: avatarSize,
              height: avatarSize,
              borderRadius: avatarSize / 2,
              borderColor: goldColor,
              backgroundColor: isDark ? '#1F2937' : '#F9FAFB',
            },
            disabled && styles.disabled,
          ]}
          onPress={handleImagePickerPress}
          disabled={disabled || isSelecting}
          activeOpacity={0.7}
        >
          {selectedImageUri ? (
            <Image
              source={{ uri: selectedImageUri }}
              style={[
                styles.avatarImage,
                {
                  width: avatarSize - 4,
                  height: avatarSize - 4,
                  borderRadius: (avatarSize - 4) / 2,
                },
              ]}
              contentFit="contain"
              transition={200}
            />
          ) : (
            <View
              style={[
                styles.avatarPlaceholder,
                {
                  width: avatarSize - 4,
                  height: avatarSize - 4,
                  borderRadius: (avatarSize - 4) / 2,
                  backgroundColor: goldColor + '20',
                },
              ]}
            >
              {isSelecting ? (
                <ActivityIndicator size="small" color={goldColor} />
              ) : (
                <Camera size={Math.max(24, avatarSize * 0.25)} color={goldColor} />
              )}
            </View>
          )}
        </TouchableOpacity>

        {/* Delete button - only show when image is selected */}
        {selectedImageUri && onImageDelete && (
          <TouchableOpacity
            style={[
              styles.deleteButton,
              {
                top: -4,
                right: -4,
              },
            ]}
            onPress={onImageDelete}
            disabled={disabled || isSelecting}
            activeOpacity={0.7}
          >
            <View style={styles.deleteButtonInner}>
              <X size={12} color="#FFFFFF" />
            </View>
          </TouchableOpacity>
        )}
      </View>

      {showLabel && (
        <Text style={[styles.hint, { color: mutedTextColor }]}>
          {isSelecting
            ? 'Selecting...'
            : 'Tap to change picture'}
        </Text>
      )}

      <ImagePickerBottomSheet
        ref={imagePickerRef}
        onCameraPress={handleCameraSelection}
        onGalleryPress={handleGallerySelection}
        title="Select Profile Picture"
        cameraLabel="Take Photo"
        galleryLabel="Choose from Gallery"
      />

      
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  avatarContainer: {
    position: 'relative',
  },
  avatarButton: {
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    overflow: 'hidden',
  },
  avatarImage: {
    // Image styles are set dynamically
  },
  avatarPlaceholder: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  initialsText: {
    fontWeight: 'bold',
  },
  uploadOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    justifyContent: 'center',
    alignItems: 'center',
    opacity: 0.8,
  },
  statusIndicator: {
    position: 'absolute',
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  hint: {
    fontSize: 12,
    marginTop: 8,
    textAlign: 'center',
  },
  disabled: {
    opacity: 0.5,
  },
  deleteButton: {
    position: 'absolute',
    zIndex: 10,
  },
  deleteButtonInner: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#FF4444',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#FFFFFF',
  },
});

export default AvatarUpload;

