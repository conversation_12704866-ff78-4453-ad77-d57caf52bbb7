/**
 * Distance calculation utilities for React Native
 * Based on dukancard-app/backend/supabase/utils/locationUtils.ts
 */

import { CurrentLocationData } from "@/src/contexts/LocationContext";

/**
 * Converts degrees to radians
 * @param degrees Angle in degrees
 * @returns Angle in radians
 */
function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}

/**
 * Calculates the distance between two geographical points using the Haversine formula.
 * @param lat1 Latitude of point 1
 * @param lon1 Longitude of point 1
 * @param lat2 Latitude of point 2
 * @param lon2 Longitude of point 2
 * @returns Distance in kilometers (rounded to 2 decimal places)
 */
export function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371; // Radius of Earth in kilometers

  const dLat = toRadians(lat2 - lat1);
  const dLon = toRadians(lon2 - lon1);

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) *
      Math.cos(toRadians(lat2)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c; // Distance in km

  return Math.round(distance * 100) / 100; // Round to 2 decimal places
}

/**
 * Validates GPS coordinates
 * @param latitude Latitude value
 * @param longitude Longitude value
 * @returns True if coordinates are valid
 */
export function validateCoordinates(
  latitude: number,
  longitude: number
): boolean {
  return (
    typeof latitude === "number" &&
    typeof longitude === "number" &&
    !isNaN(latitude) &&
    !isNaN(longitude) &&
    latitude >= -90 &&
    latitude <= 90 &&
    longitude >= -180 &&
    longitude <= 180
  );
}

/**
 * Formats distance for display
 * @param distance Distance in kilometers
 * @param showUnit Whether to include the unit (km)
 * @returns Formatted distance string
 */
export function formatDistance(
  distance: number,
  showUnit: boolean = true
): string {
  if (distance < 0.1) {
    return showUnit ? "< 0.1 km" : "< 0.1";
  }

  if (distance < 1) {
    return showUnit
      ? `${(distance * 1000).toFixed(0)} m`
      : (distance * 1000).toFixed(0);
  }

  return showUnit ? `${distance.toFixed(1)} km` : distance.toFixed(1);
}

/**
 * Calculates distance with validation and error handling
 * @param lat1 Latitude of point 1
 * @param lon1 Longitude of point 1
 * @param lat2 Latitude of point 2
 * @param lon2 Longitude of point 2
 * @returns Distance in kilometers or null if coordinates are invalid
 */
export function calculateDistanceSafe(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number | null {
  if (!validateCoordinates(lat1, lon1) || !validateCoordinates(lat2, lon2)) {
    return null;
  }

  return calculateDistance(lat1, lon1, lat2, lon2);
}

/**
 * Calculates distance from current GPS location to target coordinates
 * @param currentLocation Current GPS location from LocationContext
 * @param targetLatitude Target latitude
 * @param targetLongitude Target longitude
 * @returns Distance in kilometers or null if current location is not available
 */
export function calculateDistanceFromCurrentLocation(
  currentLocation: CurrentLocationData | null,
  targetLatitude: number,
  targetLongitude: number
): number | null {
  if (!currentLocation) {
    return null;
  }

  return calculateDistanceSafe(
    currentLocation.latitude,
    currentLocation.longitude,
    targetLatitude,
    targetLongitude
  );
}

/**
 * Calculates distance with fallback to profile location
 * @param currentLocation Current GPS location from LocationContext
 * @param profileLatitude Fallback latitude from user profile
 * @param profileLongitude Fallback longitude from user profile
 * @param targetLatitude Target latitude
 * @param targetLongitude Target longitude
 * @returns Distance in kilometers or null if no location is available
 */
export function calculateDistanceWithFallback(
  currentLocation: CurrentLocationData | null,
  profileLatitude: number | null,
  profileLongitude: number | null,
  targetLatitude: number,
  targetLongitude: number
): number | null {
  // Try current GPS location first
  if (currentLocation) {
    return calculateDistanceFromCurrentLocation(
      currentLocation,
      targetLatitude,
      targetLongitude
    );
  }

  // Fallback to profile location
  if (profileLatitude && profileLongitude) {
    return calculateDistanceSafe(
      profileLatitude,
      profileLongitude,
      targetLatitude,
      targetLongitude
    );
  }

  return null;
}
