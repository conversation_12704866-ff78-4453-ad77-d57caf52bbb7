import { StyleSheet } from 'react-native';
import { useTheme } from '@/src/hooks/useTheme';
import { responsiveFontSize } from '@/lib/theme/colors';

export const createNotFoundStyles = (theme: ReturnType<typeof useTheme>) => {
  return StyleSheet.create({
    container: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      padding: theme.spacing.lg,
    },
    link: {
      marginTop: theme.spacing.md,
      paddingVertical: theme.spacing.md,
    },
  });
};
