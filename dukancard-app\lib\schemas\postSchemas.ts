import * as yup from 'yup';

export const businessPostSchema = yup.object().shape({
  content: yup.string()
    .required('Content is required')
    .min(1, 'Content cannot be empty')
    .max(2000, 'Content cannot exceed 2000 characters'),
  image_url: yup.string().url('Image URL must be a valid URL').nullable(),
  product_ids: yup.array().of(yup.string()).nullable(),
  mentioned_business_ids: yup.array().of(yup.string()).nullable(),
});

export const customerPostSchema = yup.object().shape({
  content: yup.string()
    .required('Content is required')
    .min(1, 'Content cannot be empty')
    .max(2000, 'Content cannot exceed 2000 characters'),
  image_url: yup.string().url('Image URL must be a valid URL').nullable(),
  mentioned_business_ids: yup.array().of(yup.string()).nullable(),
});

export type BusinessPostFormData = yup.InferType<typeof businessPostSchema>;
export type CustomerPostFormData = yup.InferType<typeof customerPostSchema>;