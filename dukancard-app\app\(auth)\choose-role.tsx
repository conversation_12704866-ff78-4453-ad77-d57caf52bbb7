
import { RoleCard } from '@/src/components/ui/RoleCard';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/hooks/useTheme';
import { useAuthErrorHandler } from '@/src/hooks/useAuthErrorHandler';
import { NetworkStatusBanner } from '@/src/components/ui/NetworkStatusBanner';
import { InlineErrorHandler } from '@/src/components/ui/InlineErrorHandler';
import { ErrorRecovery } from '@/src/components/ui/ErrorRecovery';
import { createCustomerProfile } from '@/backend/supabase/services/customer/customerProfileService';
import type { RoleOption } from '@/src/types/auth';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    KeyboardAvoidingView,
    Platform,
    SafeAreaView,
    Text,
    View
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { createChooseRoleStyles } from '@/styles/auth/choose-role-styles';

export default function ChooseRoleScreen() {
  const { user, refreshProfileStatus } = useAuth();
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const styles = createChooseRoleStyles(theme, insets);
  const params = useLocalSearchParams<{
    redirect?: string;
    message?: string;
  }>();

  // Enhanced error handling
  const errorHandler = useAuthErrorHandler({
    maxRetries: 3,
    showToastOnError: true,
    showAlertOnError: false,
    context: 'ChooseRoleScreen'
  });

  const [selectedRole, setSelectedRole] = useState<'customer' | 'business' | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);
  const [showErrorRecovery, setShowErrorRecovery] = useState(false);

  const roleOptions: RoleOption[] = [
    {
      id: 'customer',
      title: 'Customer',
      description: 'Browse and connect with businesses',
      icon: 'user',
      features: [
        'Discover local businesses',
        'View business cards and products',
        'Contact businesses directly',
        'Save favorite businesses',
      ],
    },
    {
      id: 'business',
      title: 'Business',
      description: 'Create your digital card and store',
      icon: 'briefcase',
      features: [
        'Create digital business card',
        'Showcase products and services',
        'Manage customer inquiries',
        'Track business analytics',
      ],
    },
  ];

  const handleCustomerSelection = async () => {
    if (!user) {
      Alert.alert('Error', 'User not found. Please try logging in again.');
      return;
    }

    if (isLoading || isNavigating || errorHandler.isLoading) return; // Prevent multiple calls

    setSelectedRole('customer');
    errorHandler.clearError();

    await errorHandler.executeWithErrorHandling({
      operation: async () => {
        setIsLoading(true);
        setIsNavigating(true);

        // Create customer profile with initial data from auth user (matching Next.js implementation)
        const profileData = {
          name: user.user_metadata?.full_name ?? user.user_metadata?.name ?? undefined,
          email: user.email ?? undefined,
        };

        const result = await createCustomerProfile(user.id, profileData);

        if (result.error) {
          throw new Error(result.error.message || 'Failed to create customer profile');
        }

        return result;
      },
      onSuccess: async () => {
        // Refresh profile status to update context
        await refreshProfileStatus();

        // Navigate based on redirect parameter or default to customer dashboard
        if (params.redirect) {
          // If there's a redirect slug, go to that business card
          router.replace(`/(dashboard)/customer?redirect=${params.redirect}`);
        } else {
          router.replace('/(dashboard)/customer');
        }
      },
      onError: (error) => {
        setSelectedRole(null);
        setIsLoading(false);
        setIsNavigating(false);
        setShowErrorRecovery(true);
      },
      context: 'CustomerProfileCreation'
    });
  };

  const handleBusinessSelection = async () => {
    if (isLoading || isNavigating || errorHandler.isLoading) return; // Prevent multiple calls

    setSelectedRole('business');
    errorHandler.clearError();

    await errorHandler.executeWithErrorHandling({
      operation: async () => {
        setIsLoading(true);
        setIsNavigating(true);

        // Check network connectivity before navigation
        if (!errorHandler.isOnline) {
          throw new Error('No internet connection. Please check your connection and try again.');
        }

        // Navigate to onboarding with redirect parameters if available
        let onboardingUrl = '/(onboarding)/business-details';
        const searchParams = new URLSearchParams();

        if (params.redirect) {
          searchParams.append('redirect', params.redirect);
        }

        if (params.message) {
          searchParams.append('message', params.message);
        }

        if (searchParams.toString()) {
          onboardingUrl += `?${searchParams.toString()}`;
        }

        // Use setTimeout to ensure state updates are processed before navigation
        return new Promise((resolve, reject) => {
          setTimeout(() => {
            try {
              router.replace(onboardingUrl);
              resolve(true);
            } catch (error) {
              reject(error);
            }
          }, 100);
        });
      },
      onSuccess: () => {
        // Navigation successful - states will be reset by unmounting
      },
      onError: (error) => {
        // Reset states on error
        setIsLoading(false);
        setIsNavigating(false);
        setSelectedRole(null);
        setShowErrorRecovery(true);
      },
      context: 'BusinessRoleSelection'
    });
  };

  const handleRoleSelect = (roleId: 'customer' | 'business') => {
    if (isLoading || isNavigating) return;

    if (roleId === 'customer') {
      handleCustomerSelection();
    } else {
      handleBusinessSelection();
    }
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: theme.isDark ? '#000000' : '#ffffff',
      }}
    >
      <StatusBar
        style={theme.isDark ? 'light' : 'dark'}
        backgroundColor={theme.isDark ? '#000000' : '#ffffff'}
      />

      {/* Network Status Banner */}
      <NetworkStatusBanner
        onRetry={() => {
          // Retry the last failed operation based on selected role
          if (selectedRole === 'customer') {
            handleCustomerSelection();
          } else if (selectedRole === 'business') {
            handleBusinessSelection();
          }
        }}
        showWhenOnline={true}
      />

      {/* Error Recovery Modal */}
      {showErrorRecovery && errorHandler.hasError && (
        <ErrorRecovery
          error={errorHandler.error!}
          onRetry={() => {
            setShowErrorRecovery(false);
            errorHandler.clearError();
            // Retry based on selected role
            if (selectedRole === 'customer') {
              handleCustomerSelection();
            } else if (selectedRole === 'business') {
              handleBusinessSelection();
            }
          }}
          onDismiss={() => {
            setShowErrorRecovery(false);
            errorHandler.clearError();
            setSelectedRole(null);
          }}
          isRetrying={errorHandler.isLoading}
          retryCount={errorHandler.retryCount}
          maxRetries={3}
          style={{ position: 'absolute', top: 100, left: 0, right: 0, zIndex: 1000 }}
        />
      )}

      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.container}>
          {/* Top Section - Title */}
          <View style={styles.topSection}>
            <View style={styles.welcomeContainer}>
              <Text style={styles.welcomeTitle}>
                Choose Your Role
              </Text>
              <Text style={styles.welcomeSubtitle}>
                Select how you&apos;ll be using our platform. This is a one-time setup that cannot be changed later.
              </Text>
            </View>

            {params.message && (
              <View style={styles.messageContainer}>
                <Text style={styles.messageText}>
                  {params.message}
                </Text>
              </View>
            )}
          </View>

          {/* Middle Section - Role Selection */}
          <View style={styles.middleSection}>
            {/* Inline Error Handler */}
            {errorHandler.hasError && !showErrorRecovery && (
              <InlineErrorHandler
                error={errorHandler.error}
                onRetry={() => {
                  errorHandler.clearError();
                  if (selectedRole === 'customer') {
                    handleCustomerSelection();
                  } else if (selectedRole === 'business') {
                    handleBusinessSelection();
                  }
                }}
                onDismiss={() => {
                  errorHandler.clearError();
                  setSelectedRole(null);
                }}
                isRetrying={errorHandler.isLoading}
                showRetry={errorHandler.canRetryOperation}
              />
            )}

            {(isLoading || isNavigating || errorHandler.isLoading) ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={theme.colors.primary} />
                <Text style={styles.loadingText}>
                  {selectedRole === 'customer' ? 'Setting up your account...' : 'Redirecting to onboarding...'}
                </Text>
              </View>
            ) : (
              <View style={styles.roleContainer}>
                {roleOptions.map((role) => (
                  <RoleCard
                    key={role.id}
                    role={role}
                    selected={selectedRole === role.id}
                    onPress={() => role.id && handleRoleSelect(role.id as 'customer' | 'business')}
                    disabled={isLoading || isNavigating || errorHandler.isLoading || !errorHandler.isOnline}
                  />
                ))}
              </View>
            )}
          </View>

          {/* Bottom Section - Warning */}
          <View style={styles.bottomSection}>
            <View style={styles.warningContainer}>
              <Text style={styles.warningText}>
                Note: This choice is permanent and cannot be changed later.
              </Text>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
