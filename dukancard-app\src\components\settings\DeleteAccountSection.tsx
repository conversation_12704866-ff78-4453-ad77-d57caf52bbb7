import React, { useState } from 'react';
import { View, Text, Alert, Modal, TouchableOpacity } from 'react-native';
import { Trash2, AlertTriangle } from 'lucide-react-native';
import { Input } from '@/src/components/ui/Input';
import { Button } from '@/src/components/ui/Button';
import { useTheme } from '@/src/hooks/useTheme';
import { createDeleteAccountSectionStyles } from '@/styles/dashboard/customer/settings/delete-account';

export function DeleteAccountSection() {
  const theme = useTheme();
  const styles = createDeleteAccountSectionStyles(theme);

  const [deleteConfirmText, setDeleteConfirmText] = useState("");
  const [isDeleting, setIsDeleting] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);

  // Handle account deletion with confirmation
  const handleDeleteAccount = async () => {
    if (deleteConfirmText !== "DELETE") {
      Alert.alert('Error', 'Please type "DELETE" to confirm');
      return;
    }

    Alert.alert(
      'Final Confirmation',
      'Are you absolutely sure you want to delete your account? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            setIsDeleting(true);
            try {
              // TODO: Implement actual account deletion logic
              // For now, simulate the process
              await new Promise(resolve => setTimeout(resolve, 2000));
              Alert.alert('Account Deleted', 'Your account has been successfully deleted.');
              setIsModalVisible(false);
              setDeleteConfirmText("");
            } catch (error) {
              Alert.alert('Error', 'Failed to delete account. Please try again.');
              console.error(error);
            } finally {
              setIsDeleting(false);
            }
          },
        },
      ]
    );
  };

  const resetModalState = () => {
    setDeleteConfirmText("");
    setIsModalVisible(false);
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <View style={styles.iconContainer}>
            <Trash2 size={16} color="#EF4444" />
          </View>
          <View style={styles.titleContent}>
            <Text style={styles.title}>Delete Account</Text>
            <Text style={styles.subtitle}>Permanently delete your account and all associated data</Text>
          </View>
        </View>
      </View>

      <View style={styles.warningContainer}>
        <View style={styles.warningContent}>
          <AlertTriangle size={20} color="#EF4444" />
          <View style={styles.warningTextContainer}>
            <Text style={styles.warningTitle}>Warning: This action cannot be undone</Text>
            <Text style={styles.warningDescription}>
              Deleting your account will permanently remove all your data, including your saved cards,
              likes, reviews, and subscriptions. You will not be able to recover this information.
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.buttonContainer}>
        <Button
          title={isDeleting ? "Deleting..." : "Delete Account"}
          onPress={() => setIsModalVisible(true)}
          disabled={isDeleting}
          loading={isDeleting}
          variant="primary"
          style={styles.deleteButton}
          icon={isDeleting ? undefined : <Trash2 size={16} color="#FFFFFF" />}
        />
      </View>

      <Modal
        visible={isModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => {
          if (!isDeleting) {
            resetModalState();
          }
        }}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <View style={styles.modalIconContainer}>
                <Trash2 size={32} color="#EF4444" />
              </View>
              <Text style={styles.modalTitle}>Delete your account?</Text>
              <Text style={styles.modalDescription}>
                This action cannot be undone. All your data will be permanently removed.
              </Text>
            </View>

            <View style={styles.modalBody}>
              <View style={styles.modalWarningContainer}>
                <View style={styles.modalWarningContent}>
                  <AlertTriangle size={20} color="#EF4444" />
                  <View style={styles.modalWarningTextContainer}>
                    <Text style={styles.modalWarningTitle}>This will permanently delete:</Text>
                    <Text style={styles.modalWarningList}>• Your saved business cards</Text>
                    <Text style={styles.modalWarningList}>• Your likes and subscriptions</Text>
                    <Text style={styles.modalWarningList}>• Your reviews and ratings</Text>
                    <Text style={styles.modalWarningList}>• Your account information</Text>
                  </View>
                </View>
              </View>

              <View style={styles.confirmationContainer}>
                <Text style={styles.confirmationLabel}>
                  Type <Text style={styles.confirmationHighlight}>DELETE</Text> to confirm:
                </Text>
                <Input
                  value={deleteConfirmText}
                  onChangeText={setDeleteConfirmText}
                  placeholder="DELETE"
                  containerStyle={styles.confirmationInput}
                />
              </View>
            </View>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={resetModalState}
                disabled={isDeleting}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <Button
                title={isDeleting ? "Deleting..." : "Delete Account"}
                onPress={handleDeleteAccount}
                disabled={deleteConfirmText !== "DELETE" || isDeleting}
                loading={isDeleting}
                variant="primary"
                style={styles.confirmDeleteButton}
                icon={isDeleting ? undefined : <Trash2 size={16} color="#FFFFFF" />}
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}
