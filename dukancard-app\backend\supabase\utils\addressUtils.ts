/**
 * Address Utilities
 * Utilities for address processing and formatting
 */

export interface AddressData {
  street?: string;
  city?: string;
  state?: string;
  pincode?: string;
  country?: string;
}

export function formatAddress(address: AddressData): string {
  const parts = [
    address.street,
    address.city,
    address.state,
    address.pincode,
    address.country
  ].filter(Boolean);
  
  return parts.join(', ');
}

export function validateAddress(address: AddressData): boolean {
  return !!(address.city && address.state && address.pincode);
}
