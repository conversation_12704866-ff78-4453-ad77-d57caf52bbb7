import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Animated,
  Easing,
} from "react-native";
import { router } from "expo-router";
import { ArrowLeft, Loader2 } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { responsiveFontSize } from "@/lib/theme/colors";
import { useAuth } from "@/src/contexts/AuthContext";
import { useForm, FormProvider } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { customerProfileCompletionSchema } from "@/src/utils/validationSchemas";
import { useLocationPermission } from "@/src/hooks/useLocationPermission";
import { usePincodeDetails } from "@/src/hooks/usePincodeDetails";
import { useToast } from "@/src/components/ui/Toast";
import { useAlertDialog } from "@/src/components/providers/AlertProvider";
import { FormData, InitialFormData } from "@/src/types/profile";
import LoadingOverlay from "@/src/components/common/LoadingOverlay";
import AvatarUploadSection from "@/src/components/profile/AvatarUploadSection";
import PersonalInformationSection from "@/src/components/profile/PersonalInformationSection";
import AddressInformationSection from "@/src/components/profile/AddressInformationSection";
import ImagePickerBottomSheet, {
  ImagePickerBottomSheetRef,
} from "@/src/components/pickers/ImagePickerBottomSheet";
import LocalityBottomSheetPicker, {
  LocalityBottomSheetPickerRef,
} from "@/src/components/pickers/LocalityBottomSheetPicker";
import { completeCustomerProfileStyles } from "@/styles/auth/complete-customer-profile-styles";
import {
  uploadAvatarImage,
  openCameraForAvatar,
  openGalleryForAvatar,
  deleteCustomerAvatar,
} from "@/backend/supabase/services/storage/avatarUploadService";
import { compressImageModerateClient } from "@/src/utils/client-image-compression";
import { cleanAddressData } from "@/backend/supabase/utils/addressValidation";
import { getCurrentUser } from "@/lib/auth/customerAuth";
import {
  getCustomerProfileForEdit,
  updateCustomerProfileWithCoordinates,
} from "@/backend/supabase/services/customer/customerProfileService";
import NetInfo from "@react-native-community/netinfo";

export default function EditProfileScreen() {
  const theme = useTheme();
  const { isDark } = theme;
  const { user, refreshProfileStatus } = useAuth();
  const toast = useToast();
  const { confirm, error: showError } = useAlertDialog();
  const styles = completeCustomerProfileStyles(theme);

  const [initialFormData, setInitialFormData] = useState<InitialFormData>({
    name: "",
    address: "",
    pincode: "",
    city: "",
    state: "",
    locality: "",
    latitude: undefined,
    longitude: undefined,
    avatarUri: "",
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [isPincodeLoading, setIsPincodeLoading] = useState(false);
  const [availableLocalities, setAvailableLocalities] = useState<string[]>([]);

  // Refs for bottom sheets
  const imagePickerRef = useRef<ImagePickerBottomSheetRef | null>(null);
  const localityPickerRef = useRef<LocalityBottomSheetPickerRef | null>(null);
  const scrollViewRef = useRef<ScrollView>(null);

  // Animation for loading spinner
  const spinValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const spinAnimation = Animated.loop(
      Animated.timing(spinValue, {
        toValue: 1,
        duration: 1000,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    );
    spinAnimation.start();
    return () => spinAnimation.stop();
  }, [spinValue]);

  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "360deg"],
  });

  const [gpsDetectedLocality, setGpsDetectedLocality] = useState<string | null>(
    null
  );
  const [hasGpsCoordinates, setHasGpsCoordinates] = useState(false);

  // Location permission hook
  const { permission: locationPermission } = useLocationPermission();

  // React Hook Form setup
  const formMethods = useForm<FormData>({
    resolver: yupResolver(customerProfileCompletionSchema) as any,
    defaultValues: {
      name: "",
      address: "",
      pincode: "",
      city: "",
      state: "",
      locality: "",
      avatarUri: "",
      // latitude and longitude will be set when GPS location is detected
    },
    mode: "onChange",
    reValidateMode: "onChange",
  });

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    trigger,
    formState,
    formState: { errors, isValid },
    reset,
  } = formMethods;

  // Pincode details hook
  const {
    handlePincodeChange: fetchPincodeDetails,
    isPincodeLoading: pincodeLoading,
    availableLocalities: localities,
  } = usePincodeDetails();

  // Update local state when pincode hook updates
  useEffect(() => {
    setIsPincodeLoading(pincodeLoading);
    setAvailableLocalities(localities);
  }, [pincodeLoading, localities]);

  const loadExistingProfile = React.useCallback(async () => {
    try {
      const currentUser = await getCurrentUser();
      if (!currentUser) {
        router.replace("/(auth)/login");
        return;
      }

      const { data: profile, error } = await getCustomerProfileForEdit(
        currentUser.id
      );
      if (error) {
        console.error("Error loading profile:", error);
        toast.error("Error", "Failed to load profile data");
        return;
      }

      if (profile) {
        const profileData = {
          name: profile.name || "",
          address: profile.address || "",
          pincode: profile.pincode || "",
          city: profile.city || "",
          state: profile.state || "",
          locality: profile.locality || "",
          latitude: profile.latitude || undefined,
          longitude: profile.longitude || undefined,
          avatarUri: profile.avatar_url || "",
        };

        setInitialFormData(profileData);

        // Reset form with loaded data
        reset(profileData);

        // Check if GPS coordinates are already available
        if (profile.latitude && profile.longitude) {
          setHasGpsCoordinates(true);
        }

        // If pincode exists, fetch localities
        if (profile.pincode && profile.pincode.length === 6) {
          fetchPincodeDetails(profile.pincode);
        }
      } else {
        // No existing profile - initialize with user metadata if available
        const userMetadata = user?.user_metadata;
        if (userMetadata?.full_name) {
          setInitialFormData((prev) => ({
            ...prev,
            name: userMetadata.full_name,
          }));
          reset({
            ...initialFormData,
            name: userMetadata.full_name,
          });
        }
      }
    } catch (error) {
      console.error("Error loading existing profile:", error);
      toast.error("Error", "Failed to load profile data");
    } finally {
      setIsInitialLoading(false);
    }
  }, [reset, fetchPincodeDetails, toast, user, initialFormData]);

  useEffect(() => {
    loadExistingProfile();
  }, [loadExistingProfile]);

  // Auto-select locality when GPS detects it and localities are available
  useEffect(() => {
    if (gpsDetectedLocality && availableLocalities.length > 0) {
      // Small delay to ensure the localities are fully loaded
      setTimeout(() => {
        const matchingLocality = availableLocalities.find(
          (locality) =>
            locality.toLowerCase().trim() ===
            gpsDetectedLocality.toLowerCase().trim()
        );

        console.log("[AUTO-SELECT] Matching locality found:", matchingLocality);

        if (matchingLocality) {
          setValue("locality", matchingLocality);
          setGpsDetectedLocality(null); // Clear the stored value

          console.log(
            "[AUTO-SELECT] Auto-selected locality:",
            matchingLocality
          );

          // Trigger validation after auto-selection
          setTimeout(() => {
            trigger();
          }, 100);
        } else {
          console.log(
            "[AUTO-SELECT] No exact match found. GPS:",
            gpsDetectedLocality,
            "Available:",
            availableLocalities
          );
        }
      }, 50); // Small delay to ensure onPincodeChange completes first
    }
  }, [availableLocalities, gpsDetectedLocality, setValue, trigger]);

  // Handle pincode input changes with GPS lookup
  const handlePincodeInputChange = (value: string) => {
    const cleanedPincode = value.replace(/\D/g, "").substring(0, 6);
    setValue("pincode", cleanedPincode);
    if (cleanedPincode.length === 6) {
      fetchPincodeDetails(cleanedPincode);
    }
  };

  // Handle GPS location detected
  const handleLocationDetected = (latitude: number, longitude: number) => {
    setValue("latitude", latitude);
    setValue("longitude", longitude);
    setHasGpsCoordinates(true);
    // Trigger validation to ensure form recognizes the coordinates
    trigger(["latitude", "longitude"]);
  };

  // Handle address detected from GPS
  const handleAddressDetected = (address: {
    pincode: string;
    city: string;
    state: string;
    locality: string;
  }) => {
    // Auto-populate form fields with detected address
    setValue("pincode", address.pincode);
    setValue("city", address.city);
    setValue("state", address.state);

    // Store the GPS-detected locality name for auto-selection after localities are fetched
    setGpsDetectedLocality(address.locality);

    // Update initial form data to reflect the changes
    setInitialFormData((prev) => ({
      ...prev,
      pincode: address.pincode,
      address: watch("address"), // Preserve the current address
      city: address.city,
      state: address.state,
      locality: "", // Will be set after localities are fetched and matched
      latitude: watch("latitude"), // Preserve the GPS coordinates
      longitude: watch("longitude"), // Preserve the GPS coordinates
    }));

    // Fetch localities for the detected pincode - this will trigger auto-selection
    fetchPincodeDetails(address.pincode);

    // Trigger validation to ensure form recognizes all auto-filled fields
    setTimeout(() => {
      trigger();
    }, 200);
  };

  // Handle locality selection
  const handleLocalitySelect = (locality: string) => {
    setValue("locality", locality);
    trigger("locality");
  };

  // Handle image selection with compression
  const handleImageSelect = async (imageUri: string) => {
    try {
      setIsLoading(true);
      const user = await getCurrentUser();
      if (!user) {
        router.replace("/(auth)/login");
        return;
      }

      // Get original file size
      const response = await fetch(imageUri);
      const blob = await response.blob();
      const originalSize = blob.size;

      // Use Sharp-like compression logic targeting 50KB
      const compressionResult = await compressImageModerateClient(
        imageUri,
        originalSize,
        {
          targetSizeKB: 45, // Target 45KB to ensure we stay under 50KB
          maxDimension: 400,
          quality: 0.7,
          format: "webp",
        }
      );

      // Validate compression result
      if (!compressionResult.uri) {
        throw new Error("Compression did not return a valid URI");
      }

      // Store compressed image URI for upload on form submission
      setValue("avatarUri", compressionResult.uri);
    } catch (error) {
      console.error("Image compression failed:", error);
      toast.error(
        "Compression Failed",
        "Failed to compress image. Please try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Handle camera selection
  const handleCameraSelection = async () => {
    imagePickerRef.current?.dismiss(); // Dismiss bottom sheet
    try {
      const result = await openCameraForAvatar();
      if (result && !result.canceled && result.assets && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        // Check file size (15MB limit)
        const response = await fetch(imageUri);
        const blob = await response.blob();
        if (blob.size > 15 * 1024 * 1024) {
          toast.error("Error", "File size must be less than 15MB.");
          return;
        }
        await handleImageSelect(imageUri);
      }
    } catch (error) {
      console.error("Camera error:", error);
      toast.error("Error", "Failed to take photo. Please try again.");
    }
  };

  // Handle gallery selection
  const handleGallerySelection = async () => {
    imagePickerRef.current?.dismiss(); // Dismiss bottom sheet
    try {
      const result = await openGalleryForAvatar();
      if (result && !result.canceled && result.assets && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        // Check file size (15MB limit)
        const response = await fetch(imageUri);
        const blob = await response.blob();
        if (blob.size > 15 * 1024 * 1024) {
          toast.error("Error", "File size must be less than 15MB.");
          return;
        }
        await handleImageSelect(imageUri);
      }
    } catch (error) {
      console.error("Gallery error:", error);
      toast.error("Error", "Failed to select image. Please try again.");
    }
  };

  // Form submission
  const onSubmit = async (values: FormData) => {
    // Check network connectivity
    const netInfo = await NetInfo.fetch();
    if (!netInfo.isConnected) {
      toast.error(
        "No Internet Connection",
        "Please check your internet connection and try again."
      );
      return;
    }

    // Validate GPS coordinates are present
    if (!values.latitude || !values.longitude) {
      toast.error(
        "Location Required",
        "GPS coordinates are required. Please enable location services and try again."
      );
      return;
    }

    setIsLoading(true);
    try {
      const currentUser = await getCurrentUser();
      if (!currentUser) {
        router.replace("/(auth)/login");
        return;
      }

      // Handle avatar changes (upload new, delete old, or remove)
      let avatarUrl = null;
      const currentAvatarUri = values.avatarUri;
      const previousAvatarUrl = initialFormData.avatarUri;

      if (currentAvatarUri && currentAvatarUri !== previousAvatarUrl) {
        // New avatar selected - upload it
        const uploadResult = await uploadAvatarImage(
          currentAvatarUri,
          currentUser.id
        );
        if (uploadResult.success && uploadResult.url) {
          avatarUrl = uploadResult.url;

          // Delete old avatar from storage if it exists
          if (previousAvatarUrl && previousAvatarUrl.startsWith("http")) {
            try {
              await deleteCustomerAvatar(previousAvatarUrl);
            } catch (error) {
              console.warn("Failed to delete old avatar:", error);
              // Don't fail the whole operation if old avatar deletion fails
            }
          }
        } else {
          toast.warning(
            "Avatar Upload Failed",
            "Profile saved but avatar upload failed. You can update it later."
          );
        }
      } else if (!currentAvatarUri && previousAvatarUrl) {
        // Avatar was removed - delete from storage and set to null
        if (previousAvatarUrl.startsWith("http")) {
          try {
            const deleteResult = await deleteCustomerAvatar(previousAvatarUrl);
            if (deleteResult.success) {
              avatarUrl = null; // Explicitly set to null to remove from profile
            } else {
              console.warn(
                "Failed to delete avatar from storage:",
                deleteResult.error
              );
              // Continue with profile update even if storage deletion fails
              avatarUrl = null;
            }
          } catch (error) {
            console.warn("Error deleting avatar:", error);
            avatarUrl = null; // Still remove from profile even if storage deletion fails
          }
        } else {
          avatarUrl = null; // Remove from profile
        }
      } else if (currentAvatarUri === previousAvatarUrl) {
        // No change in avatar - keep existing
        avatarUrl = previousAvatarUrl;
      }

      // Clean address data
      const addressData = cleanAddressData({
        pincode: values.pincode,
        state: values.state,
        city: values.city,
        locality: values.locality,
        address: values.address,
      });

      // Update customer profile (matching complete-profile structure)
      const profileData = {
        name: values.name.trim(),
        ...addressData, // Spread address data like complete-profile
        latitude: values.latitude,
        longitude: values.longitude,
        updated_at: new Date().toISOString(), // Add updated_at like complete-profile
        // Handle avatar URL properly - include even if null to remove avatar
        avatar_url: avatarUrl,
      };

      const { data, error } = await updateCustomerProfileWithCoordinates(
        currentUser.id,
        profileData
      );

      if (error) {
        console.error("Error updating profile:", error);
        toast.error(
          "Profile Update Failed",
          "Failed to update profile. Please try again."
        );
        return;
      }

      // Verify the data was actually updated
      if (!data) {
        toast.error(
          "Profile Update Failed",
          "Profile update was not confirmed. Please try again."
        );
        return;
      }

      // Show success toast
      toast.success(
        "Profile Updated!",
        "Your profile has been updated successfully!"
      );

      // Wait a moment for database replication
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Refresh profile status in AuthContext
      await refreshProfileStatus();

      // Navigate back to profile screen
      router.back();
    } catch (error) {
      console.error("Error submitting profile:", error);

      // Check if it's a network error
      const networkState = await NetInfo.fetch();
      if (!networkState.isConnected) {
        toast.error(
          "Connection Lost",
          "Your internet connection was lost. Please try again when connected."
        );
      } else {
        toast.error(
          "Unexpected Error",
          "An unexpected error occurred. Please try again."
        );
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  // Theme colors
  const backgroundColor = isDark ? "#000000" : "#FFFFFF";
  const textColor = isDark ? "#FFFFFF" : "#000000";
  const borderColor = isDark ? "#374151" : "#E5E7EB";
  const goldColor = "#D4AF37";

  if (isInitialLoading) {
    return (
      <LoadingOverlay textColor={textColor} backgroundColor={backgroundColor} />
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: borderColor }]}>
          <TouchableOpacity onPress={handleGoBack} style={styles.backButton}>
            <ArrowLeft size={24} color={textColor} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: textColor }]}>
            Edit Profile
          </Text>
        </View>

        <ScrollView
          ref={scrollViewRef}
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          nestedScrollEnabled={true}
        >
          <FormProvider {...formMethods}>
            {/* Form */}
            <View style={styles.form}>
              <AvatarUploadSection
                avatarUri={watch("avatarUri") || ""}
                isLoading={isLoading}
                theme={theme}
                imagePickerRef={imagePickerRef}
              />

              <PersonalInformationSection
                control={control}
                textColor={textColor}
                borderColor={borderColor}
                styles={styles}
              />

              <AddressInformationSection
                control={control}
                textColor={textColor}
                borderColor={borderColor}
                styles={styles}
                isDark={isDark}
                isPincodeLoading={isPincodeLoading}
                availableLocalities={availableLocalities}
                locationPermission={locationPermission}
                hasGpsCoordinates={hasGpsCoordinates}
                handlePincodeInputChange={handlePincodeInputChange}
                handleLocationDetected={handleLocationDetected}
                handleAddressDetected={handleAddressDetected}
                handleLocalitySelect={handleLocalitySelect}
                toast={toast}
                trigger={trigger}
                localityPickerRef={localityPickerRef}
              />
            </View>
          </FormProvider>
        </ScrollView>

        {/* Submit Button */}
        <View
          style={[
            styles.footer,
            {
              borderTopColor: borderColor,
              backgroundColor: backgroundColor,
            },
          ]}
        >
          <TouchableOpacity
            style={[
              styles.submitButton,
              { backgroundColor: goldColor },
              isLoading && styles.submitButtonDisabled,
            ]}
            onPress={async () => {
              // Always trigger validation first
              const isFormValid = await trigger();
              if (isFormValid) {
                // If valid, proceed with form submission
                handleSubmit(onSubmit)();
              } else {
                // If invalid, show validation errors
                const errors = formState.errors;
                const errorFields = Object.keys(errors);
                if (errorFields.length > 0) {
                  const firstError =
                    errors[errorFields[0] as keyof typeof errors];
                  toast.error(
                    "Please fix the following errors:",
                    firstError?.message || `${errorFields[0]} is required`
                  );
                }
              }
            }}
            disabled={isLoading}
          >
            {isLoading ? (
              <View style={{ flexDirection: "row", alignItems: "center" }}>
                <Animated.View style={{ transform: [{ rotate: spin }] }}>
                  <Loader2 size={20} color="white" style={{ marginRight: 8 }} />
                </Animated.View>
                <Text style={styles.submitButtonText}>Updating...</Text>
              </View>
            ) : (
              <Text style={styles.submitButtonText}>Update Profile</Text>
            )}
          </TouchableOpacity>
        </View>

        {/* Loading Overlay */}
        {isLoading && (
          <LoadingOverlay
            textColor={textColor}
            backgroundColor={backgroundColor}
          />
        )}

        {/* Image Picker Bottom Sheet */}
        <ImagePickerBottomSheet
          ref={imagePickerRef}
          onCameraPress={handleCameraSelection}
          onGalleryPress={handleGallerySelection}
          title="Select Profile Picture"
          cameraLabel="Take Photo"
          galleryLabel="Choose from Gallery"
        />

        {/* Locality Picker Bottom Sheet */}
        <LocalityBottomSheetPicker
          ref={localityPickerRef}
          localities={availableLocalities}
          selectedLocality={watch("locality") || ""}
          onLocalitySelect={handleLocalitySelect}
          placeholder="Select your locality"
        />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
