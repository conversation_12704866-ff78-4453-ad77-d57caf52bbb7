import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { Input } from '@/src/components/ui/Input';
import { Button } from '@/src/components/ui/Button';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { KeyRound, Info, CheckCircle } from 'lucide-react-native';
import { updatePassword } from '@/backend/supabase/services/common/settingsService';

interface PasswordManagementSectionProps {
  registrationType: 'google' | 'email' | 'phone';
  onPasswordUpdated?: () => void;
}

interface FormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface FormErrors {
  currentPassword?: string;
  newPassword?: string;
  confirmPassword?: string;
}



export function PasswordManagementSection({
  registrationType,
  onPasswordUpdated,
}: PasswordManagementSectionProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [formData, setFormData] = useState<FormData>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);

  const isGoogleUser = registrationType === 'google';

  const validatePassword = (password: string): string | undefined => {
    if (!password) {
      return 'Password is required';
    }
    if (password.length < 8) {
      return 'Password must be at least 8 characters';
    }
    if (!/(?=.*[a-z])/.test(password)) {
      return 'Password must contain at least one lowercase letter';
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      return 'Password must contain at least one uppercase letter';
    }
    if (!/(?=.*\d)/.test(password)) {
      return 'Password must contain at least one number';
    }
    if (!/(?=.*[@$!%*?&])/.test(password)) {
      return 'Password must contain at least one special character (@$!%*?&)';
    }
    return undefined;
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.currentPassword.trim()) {
      newErrors.currentPassword = 'Current password is required';
    }

    const newPasswordError = validatePassword(formData.newPassword);
    if (newPasswordError) {
      newErrors.newPassword = newPasswordError;
    }

    if (!formData.confirmPassword.trim()) {
      newErrors.confirmPassword = 'Please confirm your new password';
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const result = await updatePassword({
        currentPassword: formData.currentPassword,
        newPassword: formData.newPassword,
        confirmPassword: formData.confirmPassword,
      });

      if (result.success) {
        Alert.alert('Success', 'Password updated successfully!');
        setFormData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: '',
        });
        setErrors({});
        onPasswordUpdated?.();
      } else {
        Alert.alert('Error', result.error || 'Failed to update password');
        if (result.error?.includes('current password')) {
          setErrors({ currentPassword: result.error });
        }
      }
    } catch (error) {
      console.error('Password update error:', error);
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFieldChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };



  const getPasswordStrength = (password: string): { strength: number; label: string; color: string } => {
    if (!password) return { strength: 0, label: '', color: '#e5e5e5' };

    let score = 0;
    if (password.length >= 8) score++;
    if (/(?=.*[a-z])/.test(password)) score++;
    if (/(?=.*[A-Z])/.test(password)) score++;
    if (/(?=.*\d)/.test(password)) score++;
    if (/(?=.*[@$!%*?&])/.test(password)) score++;

    if (score <= 2) return { strength: score, label: 'Weak', color: '#ef4444' };
    if (score <= 3) return { strength: score, label: 'Fair', color: '#f59e0b' };
    if (score <= 4) return { strength: score, label: 'Good', color: '#10b981' };
    return { strength: score, label: 'Strong', color: '#059669' };
  };

  const passwordStrength = getPasswordStrength(formData.newPassword);

  return (
    <View style={[styles.container, { backgroundColor: isDark ? '#1a1a1a' : '#fff' }]}>
      <View style={styles.header}>
        <View style={[styles.iconContainer, { backgroundColor: isDark ? '#92400E20' : '#FEF3C7' }]}>
          <KeyRound size={20} color="#F59E0B" />
        </View>
        <View style={styles.headerContent}>
          <Text style={[styles.title, { color: isDark ? '#fff' : '#000' }]}>
            Password Management
          </Text>
          <Text style={[styles.description, { color: isDark ? '#999' : '#666' }]}>
            {isGoogleUser
              ? "You signed up with Google. Password management is handled by your Google account."
              : "Change your account password for enhanced security."
            }
          </Text>
        </View>
      </View>

      <View style={styles.content}>
        {isGoogleUser ? (
          // Google users - show info message
          <View style={[styles.infoContainer, { backgroundColor: isDark ? '#92400E20' : '#FEF3C7' }]}>
            <View style={styles.infoContent}>
              <Info size={16} color="#F59E0B" />
              <Text style={[styles.infoText, { color: isDark ? '#FBBF24' : '#92400E' }]}>
                You signed up with Google. Password management is handled by your Google account.
              </Text>
            </View>
          </View>
        ) : (
          // Non-Google users - show password form
          <View style={styles.formContainer}>
            {/* Current Password */}
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: isDark ? '#fff' : '#000' }]}>
                Current Password
              </Text>
              <Input
                value={formData.currentPassword}
                onChangeText={(text) => handleFieldChange('currentPassword', text)}
                placeholder="Enter your current password"
                error={errors.currentPassword}
                type="password"
                leftIcon={<KeyRound size={20} color={isDark ? '#999' : '#666'} />}
                editable={!isLoading}
              />
            </View>

            {/* New Password */}
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: isDark ? '#fff' : '#000' }]}>
                New Password
              </Text>
              <Input
                value={formData.newPassword}
                onChangeText={(text) => handleFieldChange('newPassword', text)}
                placeholder="Enter your new password"
                error={errors.newPassword}
                type="password"
                leftIcon={<KeyRound size={20} color={isDark ? '#999' : '#666'} />}
                editable={!isLoading}
              />
              
              {/* Password Strength Indicator */}
              {formData.newPassword ? (
                <View style={styles.strengthContainer}>
                  <View style={styles.strengthBar}>
                    {[1, 2, 3, 4, 5].map((level) => (
                      <View
                        key={level}
                        style={[
                          styles.strengthSegment,
                          {
                            backgroundColor: level <= passwordStrength.strength
                              ? passwordStrength.color
                              : isDark ? '#333' : '#e5e5e5',
                          },
                        ]}
                      />
                    ))}
                  </View>
                  <Text style={[styles.strengthLabel, { color: passwordStrength.color }]}>
                    {passwordStrength.label}
                  </Text>
                </View>
              ) : null}

              <Text style={[styles.helperText, { color: isDark ? '#999' : '#666' }]}>
                Password must be at least 8 characters with uppercase, lowercase, number, and special character.
              </Text>
            </View>

            {/* Confirm Password */}
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: isDark ? '#fff' : '#000' }]}>
                Confirm New Password
              </Text>
              <Input
                value={formData.confirmPassword}
                onChangeText={(text) => handleFieldChange('confirmPassword', text)}
                placeholder="Confirm your new password"
                error={errors.confirmPassword}
                type="password"
                leftIcon={<KeyRound size={20} color={isDark ? '#999' : '#666'} />}
                editable={!isLoading}
              />
            </View>

            <Button
              title={isLoading ? 'Updating...' : 'Update Password'}
              onPress={handleSubmit}
              disabled={
                isLoading ||
                !formData.currentPassword.trim() ||
                !formData.newPassword.trim() ||
                !formData.confirmPassword.trim()
              }
              variant="primary"
              icon={
                isLoading ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <CheckCircle size={20} color="#fff" />
                )
              }
            />
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
  },
  content: {
    gap: 16,
  },
  infoContainer: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#F59E0B',
  },
  infoContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
    lineHeight: 20,
  },
  formContainer: {
    gap: 20,
  },
  inputGroup: {
    gap: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
  },
  strengthContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginTop: 4,
  },
  strengthBar: {
    flexDirection: 'row',
    gap: 2,
    flex: 1,
  },
  strengthSegment: {
    height: 4,
    flex: 1,
    borderRadius: 2,
  },
  strengthLabel: {
    fontSize: 12,
    fontWeight: '500',
    minWidth: 50,
    textAlign: 'right',
  },
  helperText: {
    fontSize: 12,
    lineHeight: 16,
  },
});
