/**
 * Storage Services
 * All file upload and storage-related services
 */

// Re-export with aliases to avoid conflicts
export * from './storageService';
export {
  uploadImage,
  checkCameraPermission as checkImageCameraPermission,
  openCamera as openImageCamera,
  requestCameraPermission as requestImageCameraPermission,
  showImagePickerOptions as showImagePickerOptionsForImage
} from './imageUploadService';
export * from './avatarUploadService';
export { uploadBusinessPostImage } from './businessPostImageUploadService';
export { uploadCustomerPostImage } from './customerPostImageUploadService';
