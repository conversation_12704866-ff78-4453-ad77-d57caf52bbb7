export interface ProductVariantsRow {
  id: string;
  product_id: string;
  variant_name: string;
  variant_values: any;
  base_price: number | null;
  discounted_price: number | null;
  is_available: boolean;
  images: string[] | null;
  featured_image_index: number | null;
  created_at: string;
  updated_at: string;
}

export interface ProductVariantsInsert {
  id?: string;
  product_id: string;
  variant_name: string;
  variant_values: any;
  base_price?: number | null;
  discounted_price?: number | null;
  is_available?: boolean;
  images?: string[] | null;
  featured_image_index?: number | null;
  created_at?: string;
  updated_at?: string;
}

export interface ProductVariantsUpdate {
  id?: string;
  product_id?: string;
  variant_name?: string;
  variant_values?: any;
  base_price?: number | null;
  discounted_price?: number | null;
  is_available?: boolean;
  images?: string[] | null;
  featured_image_index?: number | null;
  created_at?: string;
  updated_at?: string;
}

export type ProductVariants = ProductVariantsRow;
