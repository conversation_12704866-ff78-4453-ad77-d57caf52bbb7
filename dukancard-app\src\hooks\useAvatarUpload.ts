import { useState, useCallback } from 'react';
import { Alert } from 'react-native';
import {
  showImagePickerOptions,
  openCamera,
  openImagePicker,
  uploadAvatarImage,
  updateCustomerAvatarUrl,
  type AvatarUploadResult,
} from '@/backend/supabase/services/storage/avatarUploadService';

export type AvatarUploadStatus = 'idle' | 'selecting' | 'uploading' | 'success' | 'error';

export interface UseAvatarUploadOptions {
  initialAvatarUrl?: string;
  onUploadSuccess?: (url: string) => void;
  onUploadError?: (error: string) => void;
}

export interface UseAvatarUploadReturn {
  avatarUrl: string | null;
  uploadStatus: AvatarUploadStatus;
  uploadError: string | null;
  isUploading: boolean;
  selectAndUploadAvatar: () => Promise<void>;
  resetUpload: () => void;
}

export function useAvatarUpload({
  initialAvatarUrl,
  onUploadSuccess,
  onUploadError,
}: UseAvatarUploadOptions = {}): UseAvatarUploadReturn {
  const [avatarUrl, setAvatarUrl] = useState<string | null>(initialAvatarUrl || null);
  const [uploadStatus, setUploadStatus] = useState<AvatarUploadStatus>('idle');
  const [uploadError, setUploadError] = useState<string | null>(null);

  const isUploading = uploadStatus === 'uploading' || uploadStatus === 'selecting';

  const resetUpload = useCallback(() => {
    setUploadStatus('idle');
    setUploadError(null);
  }, []);

  const handleUploadError = useCallback((error: string) => {
    setUploadStatus('error');
    setUploadError(error);
    onUploadError?.(error);
    
    Alert.alert(
      'Upload Failed',
      error,
      [{ text: 'OK', onPress: resetUpload }]
    );
  }, [onUploadError, resetUpload]);

  const handleUploadSuccess = useCallback((url: string) => {
    setAvatarUrl(url);
    setUploadStatus('success');
    setUploadError(null);
    onUploadSuccess?.(url);
    
    Alert.alert(
      'Success',
      'Your profile picture has been updated successfully!',
      [{ text: 'OK', onPress: resetUpload }]
    );
  }, [onUploadSuccess, resetUpload]);

  const selectAndUploadAvatar = useCallback(async () => {
    try {
      setUploadStatus('selecting');
      setUploadError(null);

      // Show image picker options
      const option = await showImagePickerOptions();
      
      if (!option) {
        setUploadStatus('idle');
        return;
      }

      let result;
      
      if (option === 'camera') {
        result = await openCamera();
      } else {
        result = await openImagePicker();
      }

      if (result.canceled || !result.assets || result.assets.length === 0) {
        setUploadStatus('idle');
        return;
      }

      const selectedImage = result.assets[0];
      
      if (!selectedImage.uri) {
        handleUploadError('No image selected');
        return;
      }

      // Validate file size (15MB limit)
      if (selectedImage.fileSize && selectedImage.fileSize > 15 * 1024 * 1024) {
        handleUploadError('Image size must be less than 15MB. Please select a smaller image.');
        return;
      }

      setUploadStatus('uploading');

      // Upload image to Supabase storage
      const uploadResult: AvatarUploadResult = await uploadAvatarImage(selectedImage.uri);

      if (!uploadResult.success || !uploadResult.url) {
        handleUploadError(uploadResult.error || 'Failed to upload image');
        return;
      }

      // Update customer profile with new avatar URL
      const updateResult: AvatarUploadResult = await updateCustomerAvatarUrl(uploadResult.url);

      if (!updateResult.success) {
        handleUploadError(updateResult.error || 'Failed to update profile');
        return;
      }

      handleUploadSuccess(uploadResult.url);

    } catch (error) {
      console.error('Error in selectAndUploadAvatar:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('permission')) {
          handleUploadError(
            'Permission required. Please grant camera or photo library access in your device settings.'
          );
        } else {
          handleUploadError(error.message);
        }
      } else {
        handleUploadError('An unexpected error occurred while uploading your avatar');
      }
    }
  }, [handleUploadError, handleUploadSuccess]);

  return {
    avatarUrl,
    uploadStatus,
    uploadError,
    isUploading,
    selectAndUploadAvatar,
    resetUpload,
  };
}
