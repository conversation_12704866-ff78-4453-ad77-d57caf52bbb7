import { DashboardLayout } from "@/src/components/shared/layout/DashboardLayout";
import React, { useEffect, useState, useCallback } from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Image,
  ActivityIndicator,
} from "react-native";
import { createCustomerProfileStyles } from "@/styles/dashboard/customer/profile-styles";
import { getCustomerMetrics } from "@/backend/supabase/services/common/metricsService";
import { getCustomerProfile } from "@/backend/supabase/services/common/profileService";

import { useFocusEffect } from "@react-navigation/native";
import { useAuth } from "@/src/contexts/AuthContext";
import { useRouter } from "expo-router";
import {
  User,
  Settings,
  ChevronRight,
  Heart,
  Users,
  Star,
  Palette,
  LogOut,
} from "lucide-react-native";
import { useColorScheme } from "@/src/hooks/useColorScheme";
import { useTheme } from "@/src/hooks/useTheme";
import { ProfileSkeleton } from "@/src/components/ui/SkeletonLoader";
import { ErrorState } from "@/src/components/ui/ErrorState";
import { handleNetworkError, logError } from "@/src/utils/errorHandling";
import { ThemeToggleButton } from "@/src/components/ui/ThemeToggleButton";
import { formatIndianNumberShort } from "@/lib/utils";
import ComingSoonModal from "@/src/components/ui/ComingSoonModal";

export default function CustomerProfileScreen() {
  const { user, profileStatus, signOut } = useAuth();
  const router = useRouter();
  const colorScheme = useColorScheme();
  const [customerProfile, setCustomerProfile] = useState<any>(null);
  const [metrics, setMetrics] = useState({
    reviewCount: 0,
    subscriptionCount: 0,
    likesCount: 0,
  });
  const [isLoadingMetrics, setIsLoadingMetrics] = useState(true);
  const [isLoadingProfile, setIsLoadingProfile] = useState(true);
  const [metricsError, setMetricsError] = useState<any>(null);
  const [profileError, setProfileError] = useState<any>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  // Coming soon modal state
  const [comingSoonModal, setComingSoonModal] = useState({
    visible: false,
    featureName: "",
    description: "",
  });

  // Get customer name from profile (fallback to user metadata)
  const customerName =
    customerProfile?.name ||
    (profileStatus?.roleStatus?.hasCustomerProfile
      ? user?.user_metadata?.name ||
        user?.email?.split("@")[0] ||
        "Valued Customer"
      : "Valued Customer");

  // Helper function to show coming soon modal
  const showComingSoonModal = (featureName: string, description?: string) => {
    setComingSoonModal({
      visible: true,
      featureName,
      description:
        description ||
        `${featureName} is coming soon to the mobile app. Full functionality is available on our website.`,
    });
  };

  // Helper function to close coming soon modal
  const closeComingSoonModal = () => {
    setComingSoonModal({
      visible: false,
      featureName: "",
      description: "",
    });
  };

  // Load customer profile with caching
  const loadCustomerProfile = useCallback(async () => {
    // Early return if user is not authenticated or logging out
    if (!user?.id || isLoggingOut) return;

    try {
      setIsLoadingProfile(true);
      setProfileError(null);

      // Fetch fresh data from database
      const result = await getCustomerProfile();
      if (result.success && result.data) {
        setCustomerProfile(result.data);
      } else {
        // Only show error if user is still authenticated
        if (user?.id && !isLoggingOut) {
          setProfileError({
            type: "generic",
            title: "Unable to load profile",
            message: result.error || "Please try again later",
          });
        }
      }
    } catch (error) {
      console.error("Error loading customer profile:", error);
      // Only show error if user is still authenticated
      if (user?.id && !isLoggingOut) {
        setProfileError({
          type: "generic",
          title: "Something went wrong",
          message: "Unable to load your profile",
        });
      }
    } finally {
      // Only update loading state if user is still authenticated
      if (user?.id && !isLoggingOut) {
        setIsLoadingProfile(false);
      }
    }
  }, [user?.id, isLoggingOut]);

  // Load customer metrics with caching
  const loadMetrics = useCallback(async () => {
    // Early return if user is not authenticated or logging out
    if (!user?.id || isLoggingOut) return;

    try {
      setIsLoadingMetrics(true);
      setMetricsError(null);

      // Fetch fresh data from database
      const result = await getCustomerMetrics();

      if (result.success && result.data) {
        const metricsData = {
          reviewCount: result.data.reviewCount,
          subscriptionCount: result.data.subscriptionCount,
          likesCount: result.data.likesCount,
          lastUpdated: new Date().toISOString(),
        };
        setMetrics(metricsData);
      } else {
        // Only show error if user is still authenticated
        if (user?.id && !isLoggingOut) {
          const appError = handleNetworkError(
            new Error(result.error || "Failed to load metrics")
          );
          setMetricsError(appError);
          logError(appError, "CustomerProfileScreen.loadMetrics");
        }
      }
    } catch (err) {
      console.error("Error loading metrics:", err);
      // Only show error if user is still authenticated
      if (user?.id && !isLoggingOut) {
        const appError = handleNetworkError(err);
        setMetricsError(appError);
        logError(appError, "CustomerProfileScreen.loadMetrics");
      }
    } finally {
      // Only update loading state if user is still authenticated
      if (user?.id && !isLoggingOut) {
        setIsLoadingMetrics(false);
      }
    }
  }, [user?.id, isLoggingOut]);

  // Handle pull-to-refresh (force fresh data, no cache)
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await Promise.all([loadCustomerProfile(), loadMetrics()]);
    } catch (error) {
      console.error("Error during refresh:", error);
    } finally {
      setRefreshing(false);
    }
  }, [loadCustomerProfile, loadMetrics]);

  // Load data on initial mount
  useEffect(() => {
    loadCustomerProfile();
    loadMetrics();
  }, [loadCustomerProfile, loadMetrics]);

  // Refresh data when screen becomes active (with cache first, then fresh data)
  useFocusEffect(
    useCallback(() => {
      // Only load data if user is authenticated and not logging out
      if (user?.id && !isLoggingOut) {
        loadCustomerProfile();
        loadMetrics();
      }
    }, [loadCustomerProfile, loadMetrics, user?.id, isLoggingOut])
  );

  const theme = useTheme();
  const isDark = colorScheme === "dark";
  const styles = createCustomerProfileStyles(theme);

  return (
    <DashboardLayout
      userName={customerName}
      showNotifications={true}
      hideHeader={true}
    >
      <ScrollView
        style={styles.container}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={["#D4AF37"]}
            tintColor="#D4AF37"
          />
        }
      >
        {/* Profile Content - Matching Skeleton Layout */}
        {(isLoadingMetrics || isLoadingProfile) &&
        !customerProfile &&
        !metrics.reviewCount &&
        !metrics.subscriptionCount &&
        !metrics.likesCount ? (
          <ProfileSkeleton />
        ) : metricsError || profileError ? (
          <ErrorState
            type={(metricsError || profileError)?.type || "generic"}
            title={
              (metricsError || profileError)?.title || "Unable to load data"
            }
            message={
              (metricsError || profileError)?.message ||
              "Please try again later"
            }
            onRetry={() => {
              if (metricsError) loadMetrics();
              if (profileError) loadCustomerProfile();
            }}
            showRetry={true}
            style={styles.errorContainer}
          />
        ) : (
          <View
            style={[
              styles.profileContent,
              { backgroundColor: isDark ? "#000000" : "#FFFFFF" },
            ]}
          >
            {/* Profile Header - Avatar, Name */}
            <View style={styles.profileHeader}>
              <View
                style={[
                  styles.largeAvatar,
                  { backgroundColor: isDark ? "#333" : "#f5f5f5" },
                ]}
              >
                {customerProfile?.avatar_url ? (
                  <Image
                    source={{ uri: customerProfile.avatar_url }}
                    style={styles.avatarImage}
                    resizeMode="cover"
                  />
                ) : (
                  <User size={40} color={isDark ? "#D4AF37" : "#D4AF37"} />
                )}
              </View>
              <Text
                style={[
                  styles.profileName,
                  { color: isDark ? "#fff" : "#000" },
                ]}
              >
                {customerName}
              </Text>
            </View>

            {/* Profile Stats - Matching CustomerMetricsOverview */}
            <View
              style={[
                styles.profileStats,
                { backgroundColor: isDark ? "#2a2a2a" : "#F9FAFB" },
              ]}
            >
              <View style={styles.profileStatItem}>
                <Text
                  style={[
                    styles.statValue,
                    { color: isDark ? "#D4AF37" : "#D4AF37" },
                  ]}
                >
                  {formatIndianNumberShort(metrics.likesCount)}
                </Text>
                <Text
                  style={[
                    styles.statLabel,
                    { color: isDark ? "#999" : "#666" },
                  ]}
                >
                  Likes
                </Text>
              </View>
              <View style={styles.profileStatItem}>
                <Text
                  style={[
                    styles.statValue,
                    { color: isDark ? "#D4AF37" : "#D4AF37" },
                  ]}
                >
                  {formatIndianNumberShort(metrics.subscriptionCount)}
                </Text>
                <Text
                  style={[
                    styles.statLabel,
                    { color: isDark ? "#999" : "#666" },
                  ]}
                >
                  Following
                </Text>
              </View>
              <View style={styles.profileStatItem}>
                <Text
                  style={[
                    styles.statValue,
                    { color: isDark ? "#D4AF37" : "#D4AF37" },
                  ]}
                >
                  {formatIndianNumberShort(metrics.reviewCount)}
                </Text>
                <Text
                  style={[
                    styles.statLabel,
                    { color: isDark ? "#999" : "#666" },
                  ]}
                >
                  Reviews
                </Text>
              </View>
            </View>

            {/* Profile Menu - 5 Menu Items */}
            <View style={styles.profileMenu}>
              <TouchableOpacity
                style={styles.profileMenuItem}
                onPress={() =>
                  router.push("/(dashboard)/customer/edit-profile")
                }
              >
                <User size={24} color={isDark ? "#D4AF37" : "#D4AF37"} />
                <Text
                  style={[
                    styles.menuItemText,
                    { color: isDark ? "#fff" : "#000" },
                  ]}
                >
                  Edit Profile
                </Text>
                <ChevronRight size={16} color={isDark ? "#999" : "#666"} />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.profileMenuItem}
                onPress={() =>
                  router.push("/(dashboard)/customer/activity/likes")
                }
              >
                <Heart size={24} color={isDark ? "#D4AF37" : "#D4AF37"} />
                <Text
                  style={[
                    styles.menuItemText,
                    { color: isDark ? "#fff" : "#000" },
                  ]}
                >
                  My Likes
                </Text>
                <ChevronRight size={16} color={isDark ? "#999" : "#666"} />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.profileMenuItem}
                onPress={() =>
                  router.push("/(dashboard)/customer/activity/subscriptions")
                }
              >
                <Users size={24} color={isDark ? "#D4AF37" : "#D4AF37"} />
                <Text
                  style={[
                    styles.menuItemText,
                    { color: isDark ? "#fff" : "#000" },
                  ]}
                >
                  Following
                </Text>
                <ChevronRight size={16} color={isDark ? "#999" : "#666"} />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.profileMenuItem}
                onPress={() =>
                  router.push("/(dashboard)/customer/activity/reviews")
                }
              >
                <Star size={24} color={isDark ? "#D4AF37" : "#D4AF37"} />
                <Text
                  style={[
                    styles.menuItemText,
                    { color: isDark ? "#fff" : "#000" },
                  ]}
                >
                  My Reviews
                </Text>
                <ChevronRight size={16} color={isDark ? "#999" : "#666"} />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.profileMenuItem}
                onPress={() =>
                  showComingSoonModal(
                    "Settings",
                    "Account settings including preferences, notifications, privacy controls, and security options are coming soon to the mobile app."
                  )
                }
              >
                <Settings size={24} color={isDark ? "#D4AF37" : "#D4AF37"} />
                <Text
                  style={[
                    styles.menuItemText,
                    { color: isDark ? "#fff" : "#000" },
                  ]}
                >
                  Settings
                </Text>
                <ChevronRight size={16} color={isDark ? "#999" : "#666"} />
              </TouchableOpacity>
            </View>

            {/* Theme Toggle Section */}
            <View
              style={[
                styles.themeSection,
                { backgroundColor: isDark ? "#1a1a1a" : "#f8f9fa" },
              ]}
            >
              <View style={styles.themeSectionHeader}>
                <Palette size={20} color={isDark ? "#D4AF37" : "#D4AF37"} />
                <Text
                  style={[
                    styles.themeSectionTitle,
                    { color: isDark ? "#fff" : "#000" },
                  ]}
                >
                  App Theme
                </Text>
              </View>
              <ThemeToggleButton variant="profile" showLabel />
            </View>

            {/* Logout Button */}
            <TouchableOpacity
              style={[
                styles.logoutButton,
                { backgroundColor: isDark ? "#2a1a1a" : "#fef2f2" },
              ]}
              onPress={async () => {
                if (isLoggingOut) return; // Prevent multiple clicks

                setIsLoggingOut(true);
                try {
                  const { error } = await signOut();
                  if (error) {
                    console.error("Logout error:", error);
                    setIsLoggingOut(false); // Reset loading state on error
                  }
                  // On success, the auth context will handle navigation
                } catch (error) {
                  console.error("Logout error:", error);
                  setIsLoggingOut(false); // Reset loading state on error
                }
              }}
              disabled={isLoggingOut}
            >
              {isLoggingOut ? (
                <ActivityIndicator size="small" color="#ef4444" />
              ) : (
                <LogOut size={20} color="#ef4444" />
              )}
              <Text style={[styles.logoutButtonText, { color: "#ef4444" }]}>
                {isLoggingOut ? "Logging out..." : "Logout"}
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>

      {/* Coming Soon Modal */}
      <ComingSoonModal
        visible={comingSoonModal.visible}
        onClose={closeComingSoonModal}
        featureName={comingSoonModal.featureName}
        description={comingSoonModal.description}
      />
    </DashboardLayout>
  );
}
