import { NextRequest, NextResponse } from "next/server";
import { createAdminClient } from "@/utils/supabase/admin";
import { verifySubscriptionConsistency } from "@/lib/razorpay/webhooks/handlers/transactionUtils";

/**
 * Fix Subscription Inconsistency API
 * 
 * This endpoint allows administrators to fix subscription inconsistencies
 * between the payment_subscriptions and business_profiles tables.
 * 
 * @param req The incoming request
 * @returns Result of the fix operation
 * 
 * Request body:
 * ```json
 * {
 *   "business_profile_id": "uuid-123-456-789",
 *   "dry_run": false
 * }
 * ```
 * 
 * Response:
 * ```json
 * {
 *   "success": true,
 *   "message": "Fixed inconsistency for business uuid-123-456-789",
 *   "before": {
 *     "subscription_status": "active",
 *     "has_active_subscription": false
 *   },
 *   "after": {
 *     "subscription_status": "active",
 *     "has_active_subscription": true
 *   },
 *   "verification": {
 *     "consistent": true,
 *     "details": "Subscription and business profile are consistent"
 *   }
 * }
 * ```
 */
export async function POST(req: NextRequest) {
  try {
    // Verify API key for security
    const apiKey = req.headers.get("x-api-key") || "";
    const expectedApiKey = process.env.ADMIN_API_KEY || process.env.WEBHOOK_RETRY_API_KEY;
    
    if (!expectedApiKey || apiKey !== expectedApiKey) {
      console.error("[FIX_INCONSISTENCY] Invalid or missing API key");
      return NextResponse.json(
        { success: false, message: "Invalid or missing API key" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await req.json();
    const { business_profile_id, dry_run = false } = body;

    if (!business_profile_id) {
      return NextResponse.json(
        { success: false, message: "Missing business_profile_id parameter" },
        { status: 400 }
      );
    }

    console.log(`[FIX_INCONSISTENCY] ${dry_run ? 'Dry run' : 'Fixing'} inconsistency for business ${business_profile_id}`);

    const adminClient = createAdminClient();

    // Get current state before fixing
    const { data: currentSubscription, error: subError } = await adminClient
      .from('payment_subscriptions')
      .select('subscription_status, plan_id, razorpay_subscription_id')
      .eq('business_profile_id', business_profile_id)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (subError) {
      console.error("[FIX_INCONSISTENCY] Error getting subscription:", subError);
      return NextResponse.json(
        { success: false, message: `Error getting subscription: ${subError.message}` },
        { status: 500 }
      );
    }

    if (!currentSubscription) {
      return NextResponse.json(
        { success: false, message: "No subscription found for this business profile" },
        { status: 404 }
      );
    }

    const { data: currentProfile, error: profileError } = await adminClient
      .from('business_profiles')
      .select('has_active_subscription')
      .eq('id', business_profile_id)
      .single();

    if (profileError) {
      console.error("[FIX_INCONSISTENCY] Error getting business profile:", profileError);
      return NextResponse.json(
        { success: false, message: `Error getting business profile: ${profileError.message}` },
        { status: 500 }
      );
    }

    // Determine what the correct state should be
    const shouldBeActive = currentSubscription.subscription_status === 'active' ||
                          currentSubscription.subscription_status === 'authenticated' ||
                          currentSubscription.subscription_status === 'trial';

    const isInconsistent = currentProfile.has_active_subscription !== shouldBeActive;

    if (!isInconsistent) {
      return NextResponse.json({
        success: true,
        message: "No inconsistency detected - subscription and business profile are already consistent",
        current_state: {
          subscription_status: currentSubscription.subscription_status,
          has_active_subscription: currentProfile.has_active_subscription,
          plan_id: currentSubscription.plan_id
        },
        verification: {
          consistent: true,
          details: "Subscription and business profile are consistent"
        }
      });
    }

    const beforeState = {
      subscription_status: currentSubscription.subscription_status,
      has_active_subscription: currentProfile.has_active_subscription,
      plan_id: currentSubscription.plan_id
    };

    if (dry_run) {
      return NextResponse.json({
        success: true,
        message: `Dry run: Would fix inconsistency for business ${business_profile_id}`,
        dry_run: true,
        before: beforeState,
        after: {
          subscription_status: currentSubscription.subscription_status,
          has_active_subscription: shouldBeActive,
          plan_id: currentSubscription.plan_id
        },
        changes_needed: {
          has_active_subscription: `${currentProfile.has_active_subscription} → ${shouldBeActive}`
        }
      });
    }

    // Fix the inconsistency using the database function
    const { data: fixResult, error: fixError } = await adminClient
      .rpc('fix_subscription_inconsistency', {
        target_business_profile_id: business_profile_id
      });

    if (fixError) {
      console.error("[FIX_INCONSISTENCY] Error fixing inconsistency:", fixError);
      return NextResponse.json(
        { success: false, message: `Error fixing inconsistency: ${fixError.message}` },
        { status: 500 }
      );
    }

    // Verify the fix worked
    let verification;
    if (currentSubscription.razorpay_subscription_id) {
      verification = await verifySubscriptionConsistency(currentSubscription.razorpay_subscription_id);
    } else {
      verification = { consistent: true, details: "No Razorpay subscription ID to verify" };
    }

    const afterState = {
      subscription_status: currentSubscription.subscription_status,
      has_active_subscription: shouldBeActive,
      plan_id: currentSubscription.plan_id
    };

    // Log the fix to system alerts
    await adminClient
      .from('system_alerts')
      .insert({
        alert_type: 'SUBSCRIPTION_INCONSISTENCY',
        severity: 'MEDIUM',
        message: `Fixed subscription inconsistency for business ${business_profile_id}`,
        entity_id: business_profile_id,
        subscription_id: currentSubscription.razorpay_subscription_id,
        metadata: {
          action: 'manual_fix',
          before: beforeState,
          after: afterState,
          fix_result: fixResult,
          verification: verification
        },
        resolved: true,
        resolved_at: new Date().toISOString()
      });

    console.log(`[FIX_INCONSISTENCY] Successfully fixed inconsistency for business ${business_profile_id}`);

    return NextResponse.json({
      success: true,
      message: fixResult || `Fixed inconsistency for business ${business_profile_id}`,
      before: beforeState,
      after: afterState,
      verification,
      changes_made: {
        has_active_subscription: `${currentProfile.has_active_subscription} → ${shouldBeActive}`
      }
    });

  } catch (error) {
    console.error("[FIX_INCONSISTENCY] Error during fix operation:", error);
    
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    return NextResponse.json(
      {
        success: false,
        message: "Fix operation failed",
        error: errorMessage
      },
      { status: 500 }
    );
  }
}

/**
 * Get list of subscription inconsistencies
 */
export async function GET(req: NextRequest) {
  try {
    // Verify API key
    const apiKey = req.headers.get("x-api-key") || "";
    const expectedApiKey = process.env.ADMIN_API_KEY || process.env.WEBHOOK_RETRY_API_KEY;
    
    if (!expectedApiKey || apiKey !== expectedApiKey) {
      return NextResponse.json(
        { success: false, message: "Invalid or missing API key" },
        { status: 401 }
      );
    }

    const adminClient = createAdminClient();

    // Get all inconsistencies
    const { data: inconsistencies, error } = await adminClient
      .rpc('find_subscription_inconsistencies');

    if (error) {
      console.error("[FIX_INCONSISTENCY] Error getting inconsistencies:", error);
      return NextResponse.json(
        { success: false, message: `Error getting inconsistencies: ${error.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      count: inconsistencies?.length || 0,
      inconsistencies: inconsistencies || []
    });

  } catch (error) {
    console.error("[FIX_INCONSISTENCY] Error getting inconsistencies:", error);
    
    return NextResponse.json(
      {
        success: false,
        message: "Failed to get inconsistencies",
        error: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
