/**
 * Customer Metrics Service for React Native
 * Handles fetching customer engagement metrics
 */

import { supabase } from '@/lib/supabase';

export interface CustomerMetrics {
  reviewCount: number;
  subscriptionCount: number;
  likesCount: number;
  activityScore: number;
}

export interface MetricsResponse {
  success: boolean;
  data?: CustomerMetrics;
  error?: string;
}

/**
 * Fetch customer metrics for the current user
 */
export async function getCustomerMetrics(): Promise<MetricsResponse> {
  try {
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return {
        success: false,
        error: 'Authentication required'
      };
    }

    // Fetch all metrics in parallel
    const [reviewsResult, subscriptionsResult, likesResult] = await Promise.all([
      // Fetch reviews count
      supabase
        .from('reviews')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id),
      
      // Fetch subscriptions count
      supabase
        .from('subscriptions')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id),
      
      // Fetch likes count
      supabase
        .from('likes')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id),
    ]);

    // Handle any errors
    if (reviewsResult.error) {
      console.error('Error fetching reviews count:', reviewsResult.error);
    }
    
    if (subscriptionsResult.error) {
      console.error('Error fetching subscriptions count:', subscriptionsResult.error);
    }
    
    if (likesResult.error) {
      console.error('Error fetching likes count:', likesResult.error);
    }

    // Extract counts (default to 0 if error)
    const reviewCount = reviewsResult.count || 0;
    const subscriptionCount = subscriptionsResult.count || 0;
    const likesCount = likesResult.count || 0;

    // Calculate activity score (reviews + subscriptions * 2 + likes)
    const activityScore = reviewCount + subscriptionCount * 2 + likesCount;

    return {
      success: true,
      data: {
        reviewCount,
        subscriptionCount,
        likesCount,
        activityScore,
      }
    };
  } catch (error) {
    console.error('Error in getCustomerMetrics:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get individual metric count
 */
export async function getMetricCount(
  table: 'reviews' | 'subscriptions' | 'likes'
): Promise<{ count: number; error?: string }> {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return { count: 0, error: 'Authentication required' };
    }

    const { count, error } = await supabase
      .from(table)
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id);

    if (error) {
      console.error(`Error fetching ${table} count:`, error);
      return { count: 0, error: error.message };
    }

    return { count: count || 0 };
  } catch (error) {
    console.error(`Error in getMetricCount for ${table}:`, error);
    return { 
      count: 0, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Calculate activity score based on metrics
 */
export function calculateActivityScore(
  reviewCount: number,
  subscriptionCount: number,
  likesCount: number
): number {
  // Activity score formula: reviews + subscriptions * 2 + likes
  // Subscriptions are weighted more heavily as they indicate stronger engagement
  return reviewCount + subscriptionCount * 2 + likesCount;
}

/**
 * Get activity score level description
 */
export function getActivityScoreLevel(score: number): {
  level: string;
  description: string;
  color: 'red' | 'yellow' | 'blue' | 'brand';
} {
  if (score >= 50) {
    return {
      level: 'Super Active',
      description: 'You are highly engaged with local businesses!',
      color: 'brand',
    };
  } else if (score >= 20) {
    return {
      level: 'Active',
      description: 'You are actively engaging with businesses.',
      color: 'blue',
    };
  } else if (score >= 5) {
    return {
      level: 'Getting Started',
      description: 'You are beginning to explore local businesses.',
      color: 'yellow',
    };
  } else {
    return {
      level: 'New User',
      description: 'Start exploring and engaging with local businesses!',
      color: 'red',
    };
  }
}

/**
 * Format metric value for display using Indian number system
 * @deprecated Use formatIndianNumberShort from utils.ts instead
 */
export function formatMetricValue(value: number): string {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`;
  } else if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`;
  } else {
    return value.toString();
  }
}

/**
 * Get metric trend (placeholder for future implementation)
 */
export function getMetricTrend(
  currentValue: number,
  previousValue: number
): {
  trend: 'up' | 'down' | 'stable';
  percentage: number;
} {
  if (previousValue === 0) {
    return { trend: 'stable', percentage: 0 };
  }

  const percentage = ((currentValue - previousValue) / previousValue) * 100;
  
  if (percentage > 5) {
    return { trend: 'up', percentage };
  } else if (percentage < -5) {
    return { trend: 'down', percentage: Math.abs(percentage) };
  } else {
    return { trend: 'stable', percentage: Math.abs(percentage) };
  }
}
