import React from 'react';
import { View, StyleSheet } from 'react-native';
import { FeedFilterType } from '@/lib/types/posts';
import { FeedFilters } from './FeedFilters';

interface FeedHeaderProps {
  activeFilter?: FeedFilterType;
  onFilterChange?: (filter: FeedFilterType) => void;
  isLoading?: boolean;
}

export function FeedHeader({
  activeFilter = 'smart',
  onFilterChange,
  isLoading = false
}: FeedHeaderProps) {
  return (
    <View style={styles.container}>
      {/* Include filters if filter props are provided */}
      {onFilterChange && (
        <FeedFilters
          activeFilter={activeFilter}
          onFilterChange={onFilterChange}
          isLoading={isLoading}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 8,
  },
});
