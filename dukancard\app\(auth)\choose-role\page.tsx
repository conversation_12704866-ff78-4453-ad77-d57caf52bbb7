import React from "react";
import { <PERSON>ada<PERSON> } from "next";
import ChooseRoleClient from "./ChooseRoleClient";
import { createClient } from "@/utils/supabase/server";
import { redirect as nextRedirect } from "next/navigation";

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: "Choose Your Role", // Uses template: "Choose Your Role - Dukancard"
    description: "Select how you will use Dukancard.",
    robots: "noindex, nofollow", // Keep preventing indexing and following
  };
}

// This page should only be accessible to logged-in users without a profile
export default async function ChooseRolePage({
  searchParams,
}: {
  searchParams: Promise<{ redirect?: string; message?: string }>;
}) {
  const supabase = await createClient();
  const { redirect, message } = await searchParams;
  const redirectSlug = redirect || null;
  const messageParam = message || null;

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    // Should be handled by middleware, but good safeguard
    return nextRedirect("/login");
  }

  // Check if profile already exists in either table (middleware should prevent this, but double-check)
  const [customerProfileRes, businessProfileRes] = await Promise.all([
    supabase
      .from("customer_profiles")
      .select("id")
      .eq("id", user.id)
      .maybeSingle(),
    supabase
      .from("business_profiles")
      .select("id")
      .eq("id", user.id)
      .maybeSingle(),
  ]);

  if (customerProfileRes.error || businessProfileRes.error) {
    // Handle error appropriately - redirect to login for safety
    return nextRedirect("/login?message=Error checking profile status");
  }

  if (customerProfileRes.data || businessProfileRes.data) {
    // User already has a profile, redirect them away
    const userType = customerProfileRes.data ? "customer" : "business";
    const redirectPath =
      userType === "business" ? "/dashboard/business" : "/dashboard/customer";
    return nextRedirect(redirectPath);
  }

  // If no profile exists and user is logged in, render the choice component
  // This div acts as a minimal layout for this specific route
  return (
      <ChooseRoleClient userId={user.id} redirectSlug={redirectSlug} message={messageParam} />
  );
}
