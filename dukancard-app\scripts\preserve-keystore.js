#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const GRADLE_PROPERTIES_PATH = path.join(__dirname, '../android/gradle.properties');
const BACKUP_PATH = path.join(__dirname, '../android/gradle.properties.backup');
const BUILD_GRADLE_PATH = path.join(__dirname, '../android/build.gradle');
const APP_BUILD_GRADLE_PATH = path.join(__dirname, '../android/app/build.gradle');
const SECURITY_BACKUP_DIR = path.join(__dirname, '../.security-backup');
const JNI_DIR = path.join(__dirname, '../android/app/src/main/jni');
const JAVA_SECURITY_DIR = path.join(__dirname, '../android/app/src/main/java/com/dukancardapp/dukancard');

const KEYSTORE_CONFIG = `
# Force specific NDK version that has source.properties
android.ndkVersion=27.0.12077973

# Keystore configuration for production builds
# These properties are used by android/app/build.gradle for release signing
DUKANCARD_UPLOAD_STORE_FILE=../keystore/dukancard-upload-key.keystore
DUKANCARD_UPLOAD_KEY_ALIAS=dukancard-upload-key
DUKANCARD_UPLOAD_STORE_PASSWORD=DukancardProd2025
DUKANCARD_UPLOAD_KEY_PASSWORD=DukancardProd2025

# CMake configuration for Windows long path support
android.cmake.arguments=-DCMAKE_OBJECT_PATH_MAX=500,-DCMAKE_CXX_COMPILER_LAUNCHER=,-DCMAKE_C_COMPILER_LAUNCHER=,-DCMAKE_BUILD_TYPE=Release
android.cmake.cppFlags=-DCMAKE_OBJECT_PATH_MAX=500
android.cmake.cFlags=-DCMAKE_OBJECT_PATH_MAX=500

# Additional Ninja configuration for long paths
android.ninja.arguments=--verbose`;

function backupGradleProperties() {
  if (fs.existsSync(GRADLE_PROPERTIES_PATH)) {
    fs.copyFileSync(GRADLE_PROPERTIES_PATH, BACKUP_PATH);
    console.log('✅ Backed up gradle.properties');
  }
}

function backupSecurityFiles() {
  // Create backup directory
  if (!fs.existsSync(SECURITY_BACKUP_DIR)) {
    fs.mkdirSync(SECURITY_BACKUP_DIR, { recursive: true });
  }

  // Backup JNI files
  if (fs.existsSync(JNI_DIR)) {
    const jniBackupDir = path.join(SECURITY_BACKUP_DIR, 'jni');
    if (!fs.existsSync(jniBackupDir)) {
      fs.mkdirSync(jniBackupDir, { recursive: true });
    }

    const jniFiles = fs.readdirSync(JNI_DIR);
    jniFiles.forEach(file => {
      fs.copyFileSync(
        path.join(JNI_DIR, file),
        path.join(jniBackupDir, file)
      );
    });
    console.log('✅ Backed up JNI security files');
  }

  // Backup Java security modules
  const securityFiles = [
    'SecureKeysModule.java',
    'SecureKeysPackage.java'
  ];

  securityFiles.forEach(file => {
    const sourcePath = path.join(JAVA_SECURITY_DIR, file);
    if (fs.existsSync(sourcePath)) {
      fs.copyFileSync(sourcePath, path.join(SECURITY_BACKUP_DIR, file));
      console.log(`✅ Backed up ${file}`);
    }
  });
}

function restoreKeystoreConfig() {
  if (fs.existsSync(GRADLE_PROPERTIES_PATH)) {
    let content = fs.readFileSync(GRADLE_PROPERTIES_PATH, 'utf8');

    // Check if keystore config already exists
    if (!content.includes('DUKANCARD_UPLOAD_STORE_FILE')) {
      content += KEYSTORE_CONFIG;
      fs.writeFileSync(GRADLE_PROPERTIES_PATH, content);
      console.log('✅ Restored keystore configuration to gradle.properties');
    } else {
      console.log('✅ Keystore configuration already exists in gradle.properties');
    }
  }
}

function fixBuildGradle() {
  if (fs.existsSync(BUILD_GRADLE_PATH)) {
    let content = fs.readFileSync(BUILD_GRADLE_PATH, 'utf8');

    // Check if NDK configuration is missing
    if (!content.includes('ext.ndkVersion')) {
      const ndkConfig = `
// Set NDK version globally for all modules
def ndkDir = new File(System.getenv("ANDROID_HOME") ?: System.getenv("ANDROID_SDK_ROOT") ?: "", "ndk")
if (ndkDir.exists()) {
    def validNdks = ndkDir.listFiles()?.findAll {
        it.isDirectory() && new File(it, "source.properties").exists()
    }?.sort { it.name }
    if (validNdks && !validNdks.isEmpty()) {
        ext.ndkVersion = validNdks.last().name
        println "Global NDK version set to: \${ext.ndkVersion}"
    }
}

`;

      // Insert NDK config before the apply plugin lines
      content = content.replace(
        'apply plugin: "expo-root-project"',
        ndkConfig + 'apply plugin: "expo-root-project"'
      );

      fs.writeFileSync(BUILD_GRADLE_PATH, content);
      console.log('✅ Fixed build.gradle NDK configuration');
    } else {
      console.log('✅ NDK configuration already exists in build.gradle');
    }
  }
}

function fixAppBuildGradle() {
  if (fs.existsSync(APP_BUILD_GRADLE_PATH)) {
    let content = fs.readFileSync(APP_BUILD_GRADLE_PATH, 'utf8');

    // Check if production signing config is missing
    if (!content.includes('DUKANCARD_UPLOAD_STORE_FILE')) {
      // Add release signing config
      const releaseSigningConfig = `        release {
            if (project.hasProperty('DUKANCARD_UPLOAD_STORE_FILE')) {
                storeFile file(DUKANCARD_UPLOAD_STORE_FILE)
                storePassword DUKANCARD_UPLOAD_STORE_PASSWORD
                keyAlias DUKANCARD_UPLOAD_KEY_ALIAS
                keyPassword DUKANCARD_UPLOAD_KEY_PASSWORD
            }
        }`;

      // Insert after debug signing config
      content = content.replace(
        /(\s+debug\s*\{[^}]+\})/,
        '$1\n' + releaseSigningConfig
      );

      // Fix release build type to use production signing
      content = content.replace(
        /signingConfig signingConfigs\.debug(\s+shrinkResources)/,
        'signingConfig signingConfigs.release$1'
      );

      fs.writeFileSync(APP_BUILD_GRADLE_PATH, content);
      console.log('✅ Fixed app build.gradle signing configuration');
    } else {
      console.log('✅ Production signing configuration already exists in app build.gradle');
    }
  }
}

function restoreSecurityFiles() {
  if (!fs.existsSync(SECURITY_BACKUP_DIR)) {
    console.log('⚠️  No security backup found');
    return;
  }

  // Restore JNI files
  const jniBackupDir = path.join(SECURITY_BACKUP_DIR, 'jni');
  if (fs.existsSync(jniBackupDir)) {
    if (!fs.existsSync(JNI_DIR)) {
      fs.mkdirSync(JNI_DIR, { recursive: true });
    }

    const jniFiles = fs.readdirSync(jniBackupDir);
    jniFiles.forEach(file => {
      fs.copyFileSync(
        path.join(jniBackupDir, file),
        path.join(JNI_DIR, file)
      );
    });
    console.log('✅ Restored JNI security files');
  }

  // Restore Java security modules
  const securityFiles = [
  ];

  securityFiles.forEach(file => {
    const backupPath = path.join(SECURITY_BACKUP_DIR, file);
    if (fs.existsSync(backupPath)) {
      if (!fs.existsSync(JAVA_SECURITY_DIR)) {
        fs.mkdirSync(JAVA_SECURITY_DIR, { recursive: true });
      }
      fs.copyFileSync(backupPath, path.join(JAVA_SECURITY_DIR, file));
      console.log(`✅ Restored ${file}`);
    }
  });
}

const command = process.argv[2];

switch (command) {
  case 'backup':
    backupGradleProperties();
    backupSecurityFiles();
    break;
  case 'restore':
    restoreKeystoreConfig();
    fixBuildGradle();
    fixAppBuildGradle();
    restoreSecurityFiles();
    break;
  default:
    console.log('Usage: node preserve-keystore.js [backup|restore]');
    process.exit(1);
}
