import { Input } from '@/src/components/ui/Input';
import { useTheme } from '@/src/hooks/useTheme';
import { Building2, Mail } from 'lucide-react-native';
import React from 'react';
import {
  Text,
  View
} from 'react-native';
import { Control, FieldErrors } from 'react-hook-form';
import { FormField } from '@/src/components/forms/FormField';
import { useOnboarding } from '@/src/contexts/OnboardingContext';

interface BusinessDetailsFormData {
  businessName: string;
  email: string;
  businessSlug: string;
}

interface BusinessDetailsContentProps {
  control: Control<BusinessDetailsFormData>;
  errors: FieldErrors<BusinessDetailsFormData>;
}

const BusinessDetailsContent: React.FC<BusinessDetailsContentProps> = React.memo(
  ({ control, errors }) => {
    const theme = useTheme();
    const { slugValidation, validateSlug } = useOnboarding();

    

    return (
      <View style={{ gap: theme.spacing.md, paddingHorizontal: theme.spacing.xs }}>
        <View style={{ alignItems: 'center', marginBottom: theme.spacing.lg }}>
          <Text style={{
            fontSize: theme.typography.fontSize.xxl,
            fontWeight: '700',
            color: theme.colors.textPrimary,
            textAlign: 'center',
            marginBottom: theme.spacing.xs,
          }}>
            Business Details
          </Text>
          <Text style={{
            fontSize: theme.typography.fontSize.base,
            color: theme.colors.textSecondary,
            textAlign: 'center',
            lineHeight: theme.typography.lineHeight.normal * theme.typography.fontSize.base,
          }}>
            Let&apos;s set up your business profile
          </Text>
        </View>

        <FormField
          control={control}
          name="businessName"
          label="Business Name"
          placeholder="My Awesome Business"
          autoCapitalize="words"
          leftIcon={<Building2 size={20} />}
        />

        <FormField
          control={control}
          name="businessSlug"
          label="Business URL"
          placeholder="my-awesome-business"
          autoCapitalize="none"
          leftIcon={<Building2 size={20} />}
          
          error={errors.businessSlug || (slugValidation.error ? { type: 'custom', message: slugValidation.error } : undefined)}
        />

        <FormField
          control={control}
          name="email"
          label="Contact Email"
          placeholder="<EMAIL>"
          keyboardType="email-address"
          autoCapitalize="none"
          leftIcon={<Mail size={20} />}
        />
      </View>
    );
  }
);

BusinessDetailsContent.displayName = 'BusinessDetailsContent';

export default BusinessDetailsContent;
