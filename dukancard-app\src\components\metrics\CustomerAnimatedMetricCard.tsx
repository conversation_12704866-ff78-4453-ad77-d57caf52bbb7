import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { LucideIcon } from 'lucide-react-native';

interface CustomerAnimatedMetricCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  description: string;
  color: 'blue' | 'indigo' | 'purple' | 'rose' | 'red' | 'yellow' | 'brand';
  index?: number;
  onPress?: () => void;
}

const { width: screenWidth } = Dimensions.get('window');

export function CustomerAnimatedMetricCard({
  title,
  value,
  icon: Icon,
  description,
  color,
  index = 0,
  onPress,
}: CustomerAnimatedMetricCardProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(20)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const glowAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Staggered entrance animation
    const delay = index * 150;
    
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        delay,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        delay,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 500,
        delay,
        useNativeDriver: true,
      }),
    ]).start();

    // Continuous glow animation
    const glowAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(glowAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(glowAnim, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    );
    
    glowAnimation.start();

    return () => glowAnimation.stop();
  }, [fadeAnim, slideAnim, scaleAnim, glowAnim, index]);

  // Color variants
  const colorVariants = {
    blue: {
      backgroundColor: isDark ? '#1E3A8A' : '#DBEAFE',
      iconColor: isDark ? '#60A5FA' : '#2563EB',
      borderColor: isDark ? '#1E40AF' : '#93C5FD',
      glowColor: 'rgba(59, 130, 246, 0.3)',
    },
    red: {
      backgroundColor: isDark ? '#991B1B' : '#FEE2E2',
      iconColor: isDark ? '#F87171' : '#DC2626',
      borderColor: isDark ? '#B91C1C' : '#FCA5A5',
      glowColor: 'rgba(239, 68, 68, 0.3)',
    },
    yellow: {
      backgroundColor: isDark ? '#92400E' : '#FEF3C7',
      iconColor: isDark ? '#FBBF24' : '#D97706',
      borderColor: isDark ? '#B45309' : '#FCD34D',
      glowColor: 'rgba(245, 158, 11, 0.3)',
    },
    brand: {
      backgroundColor: isDark ? '#92400E' : '#FEF3C7',
      iconColor: '#D4AF37',
      borderColor: isDark ? '#B45309' : '#D4AF37',
      glowColor: 'rgba(212, 175, 55, 0.3)',
    },
    indigo: {
      backgroundColor: isDark ? '#312E81' : '#E0E7FF',
      iconColor: isDark ? '#818CF8' : '#4F46E5',
      borderColor: isDark ? '#3730A3' : '#A5B4FC',
      glowColor: 'rgba(79, 70, 229, 0.3)',
    },
    purple: {
      backgroundColor: isDark ? '#581C87' : '#F3E8FF',
      iconColor: isDark ? '#C084FC' : '#7C3AED',
      borderColor: isDark ? '#6B21A8' : '#DDD6FE',
      glowColor: 'rgba(124, 58, 237, 0.3)',
    },
    rose: {
      backgroundColor: isDark ? '#9F1239' : '#FFE4E6',
      iconColor: isDark ? '#FB7185' : '#E11D48',
      borderColor: isDark ? '#BE185D' : '#FDA4AF',
      glowColor: 'rgba(225, 29, 72, 0.3)',
    },
  };

  const selectedColor = colorVariants[color];
  const cardWidth = title === 'Activity Score' ? screenWidth - 32 : (screenWidth - 56) / 3;

  const handlePress = () => {
    if (onPress) {
      // Animate press
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 0.95,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();
      
      onPress();
    }
  };

  const glowOpacity = glowAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  return (
    <Animated.View
      style={[
        styles.container,
        {
          width: cardWidth,
          opacity: fadeAnim,
          transform: [
            { translateY: slideAnim },
            { scale: scaleAnim },
          ],
        }
      ]}
    >
      <TouchableOpacity
        style={[
          styles.card,
          {
            backgroundColor: selectedColor.backgroundColor,
            borderColor: selectedColor.borderColor,
          }
        ]}
        onPress={handlePress}
        disabled={!onPress}
        activeOpacity={onPress ? 0.8 : 1}
      >
        {/* Glow effect */}
        <Animated.View
          style={[
            styles.glowEffect,
            {
              backgroundColor: selectedColor.glowColor,
              opacity: glowOpacity,
            }
          ]}
        />

        {/* Content */}
        <View style={styles.content}>
          {/* Icon */}
          <View style={[styles.iconContainer, { backgroundColor: selectedColor.iconColor + '20' }]}>
            <Icon size={24} color={selectedColor.iconColor} />
          </View>

          {/* Value */}
          <Text style={[styles.value, { color: isDark ? '#FFFFFF' : '#000000' }]}>
            {value}
          </Text>

          {/* Title */}
          <Text style={[styles.title, { color: isDark ? '#E5E7EB' : '#374151' }]}>
            {title}
          </Text>

          {/* Description */}
          <Text style={[styles.description, { color: isDark ? '#9CA3AF' : '#6B7280' }]}>
            {description}
          </Text>

          {/* Interactive indicator */}
          {onPress && (
            <View style={[styles.interactiveIndicator, { backgroundColor: selectedColor.iconColor }]} />
          )}
        </View>

        {/* Decorative elements */}
        <View style={[styles.decorativeCircle1, { backgroundColor: selectedColor.glowColor }]} />
        <View style={[styles.decorativeCircle2, { backgroundColor: selectedColor.glowColor }]} />
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 12,
  },
  card: {
    borderRadius: 16,
    borderWidth: 1,
    padding: 16,
    position: 'relative',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  glowEffect: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 16,
  },
  content: {
    alignItems: 'center',
    position: 'relative',
    zIndex: 10,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  value: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
    textAlign: 'center',
  },
  title: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
    textAlign: 'center',
  },
  description: {
    fontSize: 10,
    textAlign: 'center',
    lineHeight: 14,
    paddingHorizontal: 4,
  },
  interactiveIndicator: {
    width: 20,
    height: 2,
    borderRadius: 1,
    marginTop: 8,
    opacity: 0.6,
  },
  decorativeCircle1: {
    position: 'absolute',
    top: -10,
    right: -10,
    width: 30,
    height: 30,
    borderRadius: 15,
    opacity: 0.3,
  },
  decorativeCircle2: {
    position: 'absolute',
    bottom: -8,
    left: -8,
    width: 20,
    height: 20,
    borderRadius: 10,
    opacity: 0.2,
  },
});
