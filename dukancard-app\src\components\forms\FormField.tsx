import React from 'react';
import { Control, Controller, FieldError, FieldPath, FieldValues } from 'react-hook-form';
import { Input } from '@/src/components/ui/Input';
import { Text, View, StyleSheet } from 'react-native';
import { useTheme } from '@/src/hooks/useTheme';

interface FormFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> {
  control: Control<TFieldValues>;
  name: TName;
  label: string;
  placeholder?: string;
  error?: FieldError;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad' | 'number-pad';
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  autoComplete?: 'email' | 'password' | 'username' | 'name' | 'tel' | 'off';
  secureTextEntry?: boolean;
  multiline?: boolean;
  numberOfLines?: number;
  editable?: boolean;
  maxLength?: number;
  onBlur?: () => void;
  onFocus?: () => void;
  testID?: string;
}

export function FormField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  control,
  name,
  label,
  placeholder,
  error,
  leftIcon,
  rightIcon,
  keyboardType = 'default',
  autoCapitalize = 'sentences',
  autoComplete,
  secureTextEntry = false,
  multiline = false,
  numberOfLines = 1,
  editable = true,
  maxLength,
  onBlur,
  onFocus,
  
  testID,
}: FormFieldProps<TFieldValues, TName>) {
  const theme = useTheme();

  return (
    <View style={styles.container}>
      <Controller
        control={control}
        name={name}
        render={({ field: { onChange: fieldOnChange, onBlur: fieldOnBlur, value }, fieldState: { error: fieldError } }) => (
          <Input
            label={label}
            value={value || ''}
            onChangeText={(text) => {
              fieldOnChange(text);
            }}
            onBlur={() => {
              fieldOnBlur();
              onBlur?.();
            }}
            onFocus={onFocus}
            placeholder={placeholder}
            error={fieldError?.message || error?.message}
            leftIcon={leftIcon}
            rightIcon={rightIcon}
            keyboardType={keyboardType}
            autoCapitalize={autoCapitalize}
            autoComplete={autoComplete}
            secureTextEntry={secureTextEntry}
            multiline={multiline}
            numberOfLines={numberOfLines}
            editable={editable}
            maxLength={maxLength}
            testID={testID}
          />
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
});
