export interface PaymentSubscriptionsRow {
  id: string;
  business_profile_id: string;
  razorpay_subscription_id: string | null;
  razorpay_customer_id: string | null;
  plan_id: string;
  plan_cycle: string;
  subscription_status: string;
  subscription_start_date: string | null;
  subscription_expiry_time: string | null;
  subscription_charge_time: string | null;
  last_payment_id: string | null;
  last_payment_date: string | null;
  last_payment_method: string | null;
  cancellation_requested_at: string | null;
  cancellation_reason: string | null;
  subscription_paused_at: string | null;
  created_at: string;
  updated_at: string;
  cancelled_at: string | null;
  last_webhook_timestamp: string | null;
  original_plan_id: string | null;
  original_plan_cycle: string | null;
}

export interface PaymentSubscriptionsInsert {
  id?: string;
  business_profile_id: string;
  razorpay_subscription_id?: string | null;
  razorpay_customer_id?: string | null;
  plan_id: string;
  plan_cycle: string;
  subscription_status?: string;
  subscription_start_date?: string | null;
  subscription_expiry_time?: string | null;
  subscription_charge_time?: string | null;
  last_payment_id?: string | null;
  last_payment_date?: string | null;
  last_payment_method?: string | null;
  cancellation_requested_at?: string | null;
  cancellation_reason?: string | null;
  subscription_paused_at?: string | null;
  created_at?: string;
  updated_at?: string;
  cancelled_at?: string | null;
  last_webhook_timestamp?: string | null;
  original_plan_id?: string | null;
  original_plan_cycle?: string | null;
}

export interface PaymentSubscriptionsUpdate {
  id?: string;
  business_profile_id?: string;
  razorpay_subscription_id?: string | null;
  razorpay_customer_id?: string | null;
  plan_id?: string;
  plan_cycle?: string;
  subscription_status?: string;
  subscription_start_date?: string | null;
  subscription_expiry_time?: string | null;
  subscription_charge_time?: string | null;
  last_payment_id?: string | null;
  last_payment_date?: string | null;
  last_payment_method?: string | null;
  cancellation_requested_at?: string | null;
  cancellation_reason?: string | null;
  subscription_paused_at?: string | null;
  created_at?: string;
  updated_at?: string;
  cancelled_at?: string | null;
  last_webhook_timestamp?: string | null;
  original_plan_id?: string | null;
  original_plan_cycle?: string | null;
}

export type PaymentSubscriptions = PaymentSubscriptionsRow;
