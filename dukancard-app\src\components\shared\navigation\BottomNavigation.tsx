import { Home, QrCode, Search, Store, User, Users } from 'lucide-react-native';
import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface TabItem {
  key: string;
  icon: React.ComponentType<{ size?: number; color?: string }>;
  label: string;
  onPress: () => void;
  isActive?: boolean;
  disabled?: boolean;
}

interface DukancardBottomTabsProps {
  tabs: TabItem[];
  onQRScanPress?: () => void;
  colorScheme?: 'light' | 'dark' | null;
}

const TabButton: React.FC<{
  item: TabItem;
  colorScheme?: 'light' | 'dark' | null;
}> = ({ item, colorScheme }) => {
  const goldColor = '#D4AF37';
  const inactiveColor = colorScheme === 'dark' ? '#9CA3AF' : '#6B7280';
  const disabledColor = colorScheme === 'dark' ? '#4B5563' : '#D1D5DB';
  const IconComponent = item.icon;

  const getIconColor = () => {
    if (item.disabled) return disabledColor;
    return item.isActive ? goldColor : inactiveColor;
  };

  const getTextColor = () => {
    if (item.disabled) return disabledColor;
    return item.isActive ? goldColor : inactiveColor;
  };

  return (
    <TouchableOpacity
      style={[styles.tabButton, item.disabled && styles.disabledTab]}
      onPress={item.disabled ? undefined : item.onPress}
      activeOpacity={item.disabled ? 1 : 0.7}
      disabled={item.disabled}
    >
      <IconComponent
        size={24}
        color={getIconColor()}
      />
      <Text
        style={[
          styles.tabLabel,
          { color: getTextColor() }
        ]}
      >
        {item.label}
      </Text>
    </TouchableOpacity>
  );
};

const QRScanButton: React.FC<{ onPress?: () => void }> = ({ onPress }) => {
  const goldColor = '#D4AF37';

  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
    }
  };

  return (
    <TouchableOpacity
      style={[styles.qrButton, { backgroundColor: goldColor }]}
      onPress={handlePress}
      activeOpacity={0.8}
    >
      <QrCode color="#000000" size={24} strokeWidth={2} />
      <Text style={styles.qrButtonText}>Scan</Text>
    </TouchableOpacity>
  );
};

export const DukancardBottomTabs: React.FC<DukancardBottomTabsProps> = ({
  tabs,
  onQRScanPress,
  colorScheme,
}) => {
  const insets = useSafeAreaInsets();

  return (
    <View style={[
      styles.container,
      {
        backgroundColor: colorScheme === 'dark' ? '#000000' : '#FFFFFF',
        paddingBottom: Math.max(insets.bottom, 8),
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
      }
    ]}>
      {tabs.slice(0, 2).map((tab) => (
        <TabButton key={tab.key} item={tab} colorScheme={colorScheme} />
      ))}

      <QRScanButton onPress={onQRScanPress} />

      {tabs.slice(2).map((tab) => (
        <TabButton key={tab.key} item={tab} colorScheme={colorScheme} />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingTop: 8,
    paddingHorizontal: 16,
    borderTopWidth: 0.5,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  tabLabel: {
    fontSize: 10,
    fontWeight: '500',
    marginTop: 4,
  },
  disabledTab: {
    opacity: 0.5,
  },
  qrButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#D4AF37',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    marginHorizontal: 8,
  },
  qrButtonText: {
    fontSize: 8,
    fontWeight: '600',
    color: '#000000',
    marginTop: 2,
  },
});

// Export icons for use in customer layout
export { Home, Search, Store, User, Users };

export default DukancardBottomTabs;
