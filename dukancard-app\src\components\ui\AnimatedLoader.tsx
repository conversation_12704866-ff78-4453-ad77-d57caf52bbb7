import React, { useEffect, useRef } from 'react';
import { Animated } from 'react-native';
import { Loader2 } from 'lucide-react-native';

interface AnimatedLoaderProps {
  size?: number;
  color?: string;
  style?: any;
}

export const AnimatedLoader: React.FC<AnimatedLoaderProps> = ({
  size = 24,
  color = '#D4AF37',
  style,
}) => {
  const rotateAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const startRotation = () => {
      rotateAnim.setValue(0);
      Animated.loop(
        Animated.timing(rotateAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        })
      ).start();
    };

    startRotation();

    return () => {
      rotateAnim.stopAnimation();
    };
  }, [rotateAnim]);

  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <Animated.View
      style={[
        {
          transform: [{ rotate }],
        },
        style,
      ]}
    >
      <Loader2 size={size} color={color} />
    </Animated.View>
  );
};

export default AnimatedLoader;
