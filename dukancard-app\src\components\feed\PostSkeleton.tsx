import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';

interface PostSkeletonProps {
  index?: number;
  showImage?: boolean;
  showProducts?: boolean;
}

export function PostSkeleton({ index = 0, showImage = true, showProducts = false }: PostSkeletonProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const animatedValue = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(20)).current;

  useEffect(() => {
    // Shimmer animation
    const shimmerAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1200,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1200,
          useNativeDriver: false,
        }),
      ])
    );

    // Entrance animation
    const entranceAnimation = Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 400,
        delay: index * 100,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 400,
        delay: index * 100,
        useNativeDriver: true,
      }),
    ]);

    shimmerAnimation.start();
    entranceAnimation.start();

    return () => {
      shimmerAnimation.stop();
    };
  }, [animatedValue, fadeAnim, slideAnim, index]);

  const backgroundColor = isDark ? '#000000' : '#FFFFFF';
  const borderColor = isDark ? '#000000' : '#FFFFFF'; // Seamless separators
  const skeletonBaseColor = isDark ? '#1F2937' : '#F3F4F6';
  const skeletonHighlightColor = isDark ? '#374151' : '#E5E7EB';

  const animatedStyle = {
    opacity: animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [0.3, 0.8],
    }),
    backgroundColor: animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [skeletonBaseColor, skeletonHighlightColor],
    }),
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor,
          borderColor,
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        }
      ]}
    >
      {/* Header Skeleton */}
      <View style={styles.header}>
        <View style={styles.authorInfo}>
          {/* Avatar Skeleton */}
          <Animated.View style={[styles.avatar, animatedStyle]} />

          <View style={styles.authorDetails}>
            {/* Author Name Skeleton */}
            <Animated.View style={[styles.authorName, animatedStyle]} />

            {/* Business Slug Skeleton */}
            <Animated.View style={[styles.businessSlug, animatedStyle]} />

            {/* Address Skeleton */}
            <View style={styles.addressContainer}>
              <Animated.View style={[styles.addressIcon, animatedStyle]} />
              <Animated.View style={[styles.address, animatedStyle]} />
            </View>
          </View>
        </View>
        
        {/* More Button Skeleton */}
        <Animated.View style={[styles.moreButton, animatedStyle]} />
      </View>

      {/* Content Skeleton */}
      <View style={styles.content}>
        <Animated.View style={[styles.textLine, animatedStyle]} />
        <Animated.View style={[styles.textLine, styles.textLineShort, animatedStyle]} />
        <Animated.View style={[styles.textLine, styles.textLineMedium, animatedStyle]} />
      </View>

      {/* Image Skeleton */}
      {showImage && (
        <Animated.View style={[styles.imageContainer, animatedStyle]} />
      )}

      {/* Products Skeleton */}
      {showProducts && (
        <View style={styles.productsContainer}>
          <View style={styles.productsHeader}>
            <Animated.View style={[styles.productsTitle, animatedStyle]} />
          </View>
          <View style={styles.productsGrid}>
            {[1, 2, 3, 4].map((item) => (
              <View key={item} style={styles.productItem}>
                <Animated.View style={[styles.productImage, animatedStyle]} />
                <Animated.View style={[styles.productName, animatedStyle]} />
                <Animated.View style={[styles.productPrice, animatedStyle]} />
              </View>
            ))}
          </View>
        </View>
      )}

      {/* Contact Actions Skeleton */}
      <View style={[styles.contactActions, { borderTopColor: borderColor }]}>
        <Animated.View style={[styles.contactButton, animatedStyle]} />
        <Animated.View style={[styles.contactButton, animatedStyle]} />
      </View>

      {/* Actions Skeleton - now shows timestamp */}
      <View style={[styles.actions, { borderTopColor: borderColor }]}>
        <Animated.View style={[styles.timestampSkeleton, animatedStyle]} />
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 0,
    borderWidth: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 3.84,
    elevation: 2,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 12,
  },
  authorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
  },
  authorDetails: {
    flex: 1,
  },
  authorName: {
    height: 16,
    width: 120,
    borderRadius: 8,
    marginBottom: 6,
  },
  businessSlug: {
    height: 14,
    width: 80,
    borderRadius: 7,
    marginBottom: 4,
  },
  addressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  addressIcon: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  address: {
    height: 12,
    width: 140,
    borderRadius: 6,
    flex: 1,
  },
  metaInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaItem: {
    height: 12,
    width: 60,
    borderRadius: 6,
  },
  metaDot: {
    height: 4,
    width: 4,
    borderRadius: 2,
    marginHorizontal: 6,
  },
  moreButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  content: {
    paddingHorizontal: 16,
    paddingBottom: 12,
  },
  textLine: {
    height: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  textLineShort: {
    width: '60%',
  },
  textLineMedium: {
    width: '80%',
  },
  imageContainer: {
    width: '100%',
    aspectRatio: 4 / 3,
    marginHorizontal: 0, // Ensure full width for images
  },
  productsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  productsHeader: {
    marginBottom: 12,
  },
  productsTitle: {
    height: 16,
    width: 120,
    borderRadius: 8,
  },
  productsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  productItem: {
    width: '48%',
    marginBottom: 8,
  },
  productImage: {
    width: '100%',
    aspectRatio: 1,
    borderRadius: 8,
    marginBottom: 8,
  },
  productName: {
    height: 14,
    width: '80%',
    borderRadius: 7,
    marginBottom: 4,
  },
  productPrice: {
    height: 12,
    width: '60%',
    borderRadius: 6,
  },
  contactActions: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    gap: 12,
  },
  contactButton: {
    flex: 1,
    height: 36,
    borderRadius: 8,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
  },
  timestampSkeleton: {
    height: 12,
    width: 80,
    borderRadius: 6,
  },
});
