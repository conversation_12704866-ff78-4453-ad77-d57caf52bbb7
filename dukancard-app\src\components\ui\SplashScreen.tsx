import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Dimensions } from 'react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { DukancardLogo } from './DukancardLogo';

interface SplashScreenProps {
  onAnimationComplete?: () => void;
  duration?: number;
}

export function SplashScreen({ 
  onAnimationComplete, 
  duration = 2000 // 2 seconds as per best practices
}: SplashScreenProps) {
  const colorScheme = useColorScheme();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    // Start the animation sequence
    const startAnimation = () => {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();

      // Complete the splash screen after the specified duration
      setTimeout(() => {
        if (onAnimationComplete) {
          onAnimationComplete();
        }
      }, duration);
    };

    startAnimation();
  }, [fadeAnim, scaleAnim, duration, onAnimationComplete]);

  // Background color based on theme
  const backgroundColor = colorScheme === 'dark' ? '#000000' : '#FFFFFF';

  return (
    <View style={[styles.container, { backgroundColor }]}>
      <Animated.View
        style={[
          styles.logoContainer,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <DukancardLogo 
          size="hero" 
          showText={true} 
          showTagline={true} 
        />
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height,
  },
  logoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
