import React, { createContext, useContext, useState, ReactNode } from 'react';
import { Modal, View, TouchableOpacity, StyleSheet, Animated, Dimensions } from 'react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';

interface DrawerContextType {
  isOpen: boolean;
  openDrawer: () => void;
  closeDrawer: () => void;
  toggleDrawer: () => void;
}

const DrawerContext = createContext<DrawerContextType | undefined>(undefined);

interface DrawerProviderProps {
  children: ReactNode;
}

export const DrawerProvider: React.FC<DrawerProviderProps> = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);

  const openDrawer = () => setIsOpen(true);
  const closeDrawer = () => setIsOpen(false);
  const toggleDrawer = () => setIsOpen(!isOpen);

  return (
    <DrawerContext.Provider value={{ isOpen, openDrawer, closeDrawer, toggleDrawer }}>
      {children}
    </DrawerContext.Provider>
  );
};

export const useDrawer = () => {
  const context = useContext(DrawerContext);
  if (context === undefined) {
    throw new Error('useDrawer must be used within a DrawerProvider');
  }
  return context;
};

interface DrawerProps {
  children: ReactNode;
  width?: number;
}

export const Drawer: React.FC<DrawerProps> = ({ 
  children, 
  width = Dimensions.get('window').width * 0.8 
}) => {
  const { isOpen, closeDrawer } = useDrawer();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const backgroundColor = isDark ? '#000000' : '#FFFFFF';
  const overlayColor = isDark ? 'rgba(0, 0, 0, 0.5)' : 'rgba(0, 0, 0, 0.3)';

  return (
    <Modal
      visible={isOpen}
      transparent
      animationType="none"
      onRequestClose={closeDrawer}
    >
      <View style={styles.container}>
        {/* Overlay */}
        <TouchableOpacity 
          style={[styles.overlay, { backgroundColor: overlayColor }]}
          activeOpacity={1}
          onPress={closeDrawer}
        />
        
        {/* Drawer Content */}
        <Animated.View 
          style={[
            styles.drawer,
            { 
              backgroundColor,
              width,
            }
          ]}
        >
          {children}
        </Animated.View>
      </View>
    </Modal>
  );
};

interface DrawerTriggerProps {
  children: ReactNode;
}

export const DrawerTrigger: React.FC<DrawerTriggerProps> = ({ children }) => {
  const { toggleDrawer } = useDrawer();

  return (
    <TouchableOpacity onPress={toggleDrawer} activeOpacity={0.7}>
      {children}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
  },
  overlay: {
    flex: 1,
  },
  drawer: {
    height: '100%',
    shadowColor: '#000',
    shadowOffset: {
      width: -2,
      height: 0,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});

export default DrawerProvider;
