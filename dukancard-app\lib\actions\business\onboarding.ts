import { completeBusinessOnboarding, BusinessOnboardingData, ServiceResult } from '@/backend/supabase/services/business/businessOnboardingService';

/**
 * Completes the business onboarding process by calling the backend service.
 *
 * @param onboardingData - The data collected from the onboarding form.
 * @returns A promise that resolves with the result of the operation.
 */
export async function completeBusinessOnboardingAction(
  onboardingData: BusinessOnboardingData
): Promise<ServiceResult> {
  try {
    // Call the backend service to complete business onboarding
    const result = await completeBusinessOnboarding(onboardingData);

    if (!result.success) {
      // The service layer should provide a user-friendly error message
      return { success: false, error: result.error || 'An unknown error occurred during onboarding.' };
    }

    return { success: true, data: result.data };
  } catch (error) {
    // Handle unexpected errors
    console.error('Unexpected error in completeBusinessOnboardingAction:', error);
    
    // Provide a generic error message to the user
    return { success: false, error: 'An unexpected error occurred. Please try again.' };
  }
}