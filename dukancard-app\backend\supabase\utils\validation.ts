/**
 * Form validation utilities for React Native app
 * Matches web version logic from dukancard/lib/schemas/
 */

// Email validation
export const validateEmail = (email: string): { isValid: boolean; error?: string } => {
  if (!email) {
    return { isValid: false, error: 'Email is required' };
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return { isValid: false, error: 'Please enter a valid email address' };
  }

  return { isValid: true };
};

// Password validation - matches PasswordComplexitySchema from dukancard
export const validatePassword = (password: string): { isValid: boolean; error?: string } => {
  if (!password) {
    return { isValid: false, error: 'Password is required' };
  }

  if (password.length < 6) {
    return { isValid: false, error: 'Password must be at least 6 characters' };
  }

  if (!/[A-Z]/.test(password)) {
    return { isValid: false, error: 'Password must contain at least one capital letter' };
  }

  if (!/[a-z]/.test(password)) {
    return { isValid: false, error: 'Password must contain at least one lowercase letter' };
  }

  if (!/[0-9]/.test(password)) {
    return { isValid: false, error: 'Password must contain at least one number' };
  }

  if (!/[^A-Za-z0-9]/.test(password)) {
    return { isValid: false, error: 'Password must contain at least one symbol' };
  }

  return { isValid: true };
};

// Password confirmation validation
export const validatePasswordConfirmation = (
  password: string, 
  confirmPassword: string
): { isValid: boolean; error?: string } => {
  if (!confirmPassword) {
    return { isValid: false, error: 'Please confirm your password' };
  }

  if (password !== confirmPassword) {
    return { isValid: false, error: "Passwords don't match" };
  }

  return { isValid: true };
};

// Indian mobile number validation - matches IndianMobileSchema
export const validateIndianMobile = (mobile: string): { isValid: boolean; error?: string } => {
  if (!mobile) {
    return { isValid: false, error: 'Mobile number is required' };
  }

  // Remove any non-digit characters for validation
  const cleanMobile = mobile.replace(/\D/g, '');

  if (cleanMobile.length < 10) {
    return { isValid: false, error: 'Mobile number must be at least 10 digits' };
  }

  if (cleanMobile.length > 10) {
    return { isValid: false, error: 'Mobile number must be exactly 10 digits' };
  }

  if (!/^\d{10}$/.test(cleanMobile)) {
    return { isValid: false, error: 'Please enter a valid 10-digit mobile number' };
  }

  return { isValid: true };
};

// Business name validation
export const validateBusinessName = (name: string): { isValid: boolean; error?: string } => {
  if (!name || name.trim().length === 0) {
    return { isValid: false, error: 'Business name is required' };
  }

  if (name.trim().length < 2) {
    return { isValid: false, error: 'Business name must be at least 2 characters' };
  }

  if (name.trim().length > 100) {
    return { isValid: false, error: 'Business name must be less than 100 characters' };
  }

  return { isValid: true };
};

// Business slug validation - matches formSchema businessSlug validation
export const validateBusinessSlug = (slug: string): { isValid: boolean; error?: string } => {
  if (!slug || slug.trim().length === 0) {
    return { isValid: false, error: 'URL slug is required' };
  }

  if (slug.length < 3) {
    return { isValid: false, error: 'URL slug must be at least 3 characters' };
  }

  if (slug.length > 50) {
    return { isValid: false, error: 'URL slug must be less than 50 characters' };
  }

  if (!/^[a-z0-9-]+$/.test(slug)) {
    return { 
      isValid: false, 
      error: 'URL slug can only contain lowercase letters, numbers, and hyphens' 
    };
  }

  if (slug.startsWith('-') || slug.endsWith('-')) {
    return { isValid: false, error: 'URL slug cannot start or end with a hyphen' };
  }

  if (slug.includes('--')) {
    return { isValid: false, error: 'URL slug cannot contain consecutive hyphens' };
  }

  return { isValid: true };
};

// Pincode validation - matches locationSchemas and onboarding types
export const validatePincode = (pincode: string): { isValid: boolean; error?: string } => {
  if (!pincode || pincode.trim().length === 0) {
    return { isValid: false, error: 'Pincode is required' };
  }

  // Remove any non-digit characters
  const cleanPincode = pincode.replace(/\D/g, '');

  if (cleanPincode.length !== 6) {
    return { isValid: false, error: 'Pincode must be exactly 6 digits' };
  }

  if (!/^\d{6}$/.test(cleanPincode)) {
    return { isValid: false, error: 'Pincode must contain only digits' };
  }

  return { isValid: true };
};

// Member name validation
export const validateMemberName = (name: string): { isValid: boolean; error?: string } => {
  if (!name || name.trim().length === 0) {
    return { isValid: false, error: 'Your name is required' };
  }

  if (name.trim().length < 2) {
    return { isValid: false, error: 'Name must be at least 2 characters' };
  }

  if (name.trim().length > 50) {
    return { isValid: false, error: 'Name must be less than 50 characters' };
  }

  return { isValid: true };
};

// Title/designation validation
export const validateTitle = (title: string): { isValid: boolean; error?: string } => {
  if (!title || title.trim().length === 0) {
    return { isValid: false, error: 'Your title/designation is required' };
  }

  if (title.trim().length < 2) {
    return { isValid: false, error: 'Title must be at least 2 characters' };
  }

  if (title.trim().length > 50) {
    return { isValid: false, error: 'Title must be less than 50 characters' };
  }

  return { isValid: true };
};

// Business category validation
export const validateBusinessCategory = (category: string): { isValid: boolean; error?: string } => {
  if (!category || category.trim().length === 0) {
    return { isValid: false, error: 'Business category is required' };
  }

  return { isValid: true };
};

// Address line validation
export const validateAddressLine = (address: string): { isValid: boolean; error?: string } => {
  if (!address || address.trim().length === 0) {
    return { isValid: false, error: 'Address line is required' };
  }

  if (address.trim().length < 5) {
    return { isValid: false, error: 'Address must be at least 5 characters' };
  }

  if (address.trim().length > 200) {
    return { isValid: false, error: 'Address must be less than 200 characters' };
  }

  return { isValid: true };
};

// City validation
export const validateCity = (city: string): { isValid: boolean; error?: string } => {
  if (!city || city.trim().length === 0) {
    return { isValid: false, error: 'City is required' };
  }

  if (city.trim().length < 2) {
    return { isValid: false, error: 'City name must be at least 2 characters' };
  }

  return { isValid: true };
};

// State validation
export const validateState = (state: string): { isValid: boolean; error?: string } => {
  if (!state || state.trim().length === 0) {
    return { isValid: false, error: 'State is required' };
  }

  return { isValid: true };
};

// Locality validation
export const validateLocality = (locality: string): { isValid: boolean; error?: string } => {
  if (!locality || locality.trim().length === 0) {
    return { isValid: false, error: 'Locality/area is required' };
  }

  return { isValid: true };
};

// Plan selection validation
export const validatePlanSelection = (planId: string): { isValid: boolean; error?: string } => {
  if (!planId || planId.trim().length === 0) {
    return { isValid: false, error: 'Please select a plan' };
  }

  return { isValid: true };
};

// Generic required field validation
export const validateRequired = (
  value: string, 
  fieldName: string
): { isValid: boolean; error?: string } => {
  if (!value || value.trim().length === 0) {
    return { isValid: false, error: `${fieldName} is required` };
  }

  return { isValid: true };
};

// Validation result type
export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

// Validation function type
export type ValidationFunction = (value: string) => ValidationResult;
