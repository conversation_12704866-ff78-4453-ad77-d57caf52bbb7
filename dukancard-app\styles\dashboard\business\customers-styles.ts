import { StyleSheet } from 'react-native';

export const createCustomersStyles = () => {
  return StyleSheet.create({
    container: {
      flex: 1,
    },
    content: {
      padding: 20,
      paddingTop: 60,
    },
    header: {
      alignItems: 'center',
      marginBottom: 30,
    },
    subtitle: {
      marginTop: 8,
      textAlign: 'center',
      opacity: 0.7,
    },
    statsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 30,
    },
    statCard: {
      flex: 1,
      alignItems: 'center',
      padding: 16,
      marginHorizontal: 4,
      backgroundColor: 'rgba(10, 126, 164, 0.1)',
      borderRadius: 12,
    },
    statNumber: {
      fontSize: 24,
      marginBottom: 4,
    },
    statLabel: {
      fontSize: 12,
      opacity: 0.7,
    },
    customersContainer: {
      marginBottom: 30,
    },
    sectionTitle: {
      marginBottom: 16,
    },
    customerCard: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      marginBottom: 12,
      backgroundColor: 'rgba(10, 126, 164, 0.1)',
      borderRadius: 12,
    },
    customerAvatar: {
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: '#0a7ea4',
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 16,
    },
    avatarText: {
      color: '#FFFFFF',
      fontWeight: 'bold',
      fontSize: 16,
    },
    customerInfo: {
      flex: 1,
    },
    customerEmail: {
      opacity: 0.7,
      fontSize: 14,
      marginTop: 2,
    },
    customerDate: {
      opacity: 0.5,
      fontSize: 12,
      marginTop: 4,
    },
    emptyState: {
      padding: 40,
      alignItems: 'center',
      backgroundColor: 'rgba(10, 126, 164, 0.05)',
      borderRadius: 12,
      borderWidth: 2,
      borderColor: 'rgba(10, 126, 164, 0.2)',
      borderStyle: 'dashed',
    },
    emptyText: {
      textAlign: 'center',
      opacity: 0.6,
    },
  });
};
