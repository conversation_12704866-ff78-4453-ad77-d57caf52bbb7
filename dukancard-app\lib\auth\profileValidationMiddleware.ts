import { router, useSegments } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import { supabase } from '../supabase';
import { getCurrentUser, getUserType, validateCustomerProfile, type AuthUser } from './customerAuth';

/**
 * Profile completion validation middleware for React Native
 * Based on dukancard/utils/supabase/middleware.ts and dukancard/lib/actions/customerProfiles/addressValidation.ts
 */

export interface ProfileValidationState {
  isLoading: boolean;
  isAuthenticated: boolean;
  user: AuthUser | null;
  userType: 'customer' | 'business' | null;
  hasCompleteProfile: boolean;
  addressValidation?: {
    isValid: boolean;
    missingFields?: string[];
    message?: string;
  };
  error?: string;
}

export interface ProfileValidationOptions {
  exemptFromAddressValidation?: boolean;
  allowedRoutes?: string[];
  redirectOnIncomplete?: boolean;
}

/**
 * Hook for profile validation middleware
 * Use this in screens that require complete profile
 */
export function useProfileValidation(options: ProfileValidationOptions = {}) {
  const {
    exemptFromAddressValidation = false,
    allowedRoutes = [],
    redirectOnIncomplete = true
  } = options;

  const segments = useSegments();
  const [state, setState] = useState<ProfileValidationState>({
    isLoading: true,
    isAuthenticated: false,
    user: null,
    userType: null,
    hasCompleteProfile: false
  });

  const validateProfile = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: undefined }));

      // Get current user
      const user = await getCurrentUser();

      if (!user) {
        setState({
          isLoading: false,
          isAuthenticated: false,
          user: null,
          userType: null,
          hasCompleteProfile: false
        });
        return;
      }

      // Get user type
      const userType = await getUserType(user.id);

      if (!userType) {
        setState({
          isLoading: false,
          isAuthenticated: true,
          user,
          userType: null,
          hasCompleteProfile: false
        });
        return;
      }

      // For customer users, validate profile completion (name and address)
      let profileValidation;
      let hasCompleteProfile = true;

      if (userType === 'customer' && !exemptFromAddressValidation) {
        profileValidation = await validateCustomerProfile(user.id);
        hasCompleteProfile = profileValidation.isValid;
      }

      setState({
        isLoading: false,
        isAuthenticated: true,
        user,
        userType,
        hasCompleteProfile,
        addressValidation: profileValidation
      });

    } catch (error) {
      console.error('Profile validation error:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to validate profile'
      }));
    }
  }, [exemptFromAddressValidation]);

  useEffect(() => {
    validateProfile();
  }, [validateProfile]);

  // Handle redirects based on validation state
  useEffect(() => {
    if (state.isLoading || !redirectOnIncomplete) return;

    const currentPath = `/${segments.join('/')}`;

    // Don't redirect if on allowed routes or exempt routes
    const exemptRoutes = [
      '/(dashboard)/customer/profile',
      '/(dashboard)/customer/settings',
      '/(auth)/complete-profile',
      '/(auth)/login',
      '/(auth)/choose-role'
    ];

    if (allowedRoutes.includes(currentPath) || exemptRoutes.some(route => currentPath.startsWith(route))) {
      return;
    }

    // Redirect unauthenticated users to login
    if (!state.isAuthenticated) {
      router.replace('/(auth)/login');
      return;
    }

    // Redirect users without role to choose role
    if (state.isAuthenticated && !state.userType) {
      router.replace('/(auth)/choose-role');
      return;
    }

    // Redirect customers with incomplete profile to complete profile
    if (state.userType === 'customer' && !state.hasCompleteProfile) {
      router.replace('/(auth)/complete-profile');
      return;
    }
  }, [state.isLoading, state.isAuthenticated, state.userType, state.hasCompleteProfile, redirectOnIncomplete, segments, allowedRoutes]);

  const refreshValidation = () => {
    validateProfile();
  };

  return {
    ...state,
    refreshValidation
  };
}

/**
 * Higher-order component for profile validation
 * Wraps components that require complete profile
 */
export function withProfileValidation<T extends object>(
  Component: React.ComponentType<T>,
  options: ProfileValidationOptions = {}
): React.ComponentType<T> {
  return function ProfileValidatedComponent(props: T): React.ReactElement | null {
    const validation = useProfileValidation(options);

    // Show loading state
    if (validation.isLoading) {
      return null; // Or a loading component
    }

    // Show error state
    if (validation.error) {
      return null; // Or an error component
    }

    // Don't render if profile is incomplete and redirecting
    if (!validation.hasCompleteProfile && options.redirectOnIncomplete !== false) {
      return null;
    }

    return React.createElement(Component, props);
  };
}

/**
 * Utility function to check if current route requires profile validation
 */
export function requiresProfileValidation(segments: string[]): boolean {
  const currentPath = `/${segments.join('/')}`;
  
  // Routes that require authentication and complete profile
  const protectedRoutes = [
    '/(dashboard)/customer',
    '/(dashboard)/business'
  ];

  return protectedRoutes.some(route => currentPath.startsWith(route));
}

/**
 * Utility function to check if current route is exempt from address validation
 */
export function isExemptFromAddressValidation(segments: string[]): boolean {
  const currentPath = `/${segments.join('/')}`;
  
  // Routes exempt from address validation (like settings/profile pages and complete profile)
  const exemptRoutes = [
    '/(dashboard)/customer/profile',
    '/(dashboard)/customer/settings',
    '/(auth)/complete-profile'
  ];

  return exemptRoutes.some(route => currentPath.startsWith(route));
}

/**
 * Authentication state listener for real-time auth changes
 * Note: This now uses AuthContext as the single source of truth for auth state
 * and only provides navigation logic based on auth changes
 */
export function useAuthStateListener() {
  // Note: This hook is deprecated in favor of using AuthContext directly
  // It's kept for backward compatibility but should be replaced with useAuth()
  console.warn('useAuthStateListener is deprecated. Use useAuth() from AuthContext instead.');

  const [authState, setAuthState] = useState<{
    isAuthenticated: boolean;
    user: AuthUser | null;
    isLoading: boolean;
  }>({
    isAuthenticated: false,
    user: null,
    isLoading: true
  });

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const user = await getCurrentUser();
        setAuthState({
          isAuthenticated: !!user,
          user,
          isLoading: false
        });
      } catch (error) {
        console.error('Error getting initial session:', error);
        setAuthState({
          isAuthenticated: false,
          user: null,
          isLoading: false
        });
      }
    };

    getInitialSession();

    // Note: Auth state changes are now handled by AuthContext
    // This hook only provides local state for backward compatibility
  }, []);

  return authState;
}

/**
 * Utility function for manual profile validation
 * Use this when you need to validate profile programmatically
 */
export async function validateProfileManually(
  userId: string,
  options: ProfileValidationOptions = {}
): Promise<{
  isValid: boolean;
  userType?: 'customer' | 'business' | null;
  addressValidation?: {
    isValid: boolean;
    missingFields?: string[];
    message?: string;
  };
  error?: string;
}> {
  try {
    const userType = await getUserType(userId);
    
    if (!userType) {
      return {
        isValid: false,
        userType: null,
        error: 'User type not determined'
      };
    }

    // For customer users, validate profile if not exempt
    if (userType === 'customer' && !options.exemptFromAddressValidation) {
      const profileValidation = await validateCustomerProfile(userId);
      return {
        isValid: profileValidation.isValid,
        userType,
        addressValidation: profileValidation
      };
    }

    // For business users or exempt customers, profile is considered valid
    return {
      isValid: true,
      userType
    };

  } catch (error) {
    console.error('Manual profile validation error:', error);
    return {
      isValid: false,
      error: 'Validation failed'
    };
  }
}
