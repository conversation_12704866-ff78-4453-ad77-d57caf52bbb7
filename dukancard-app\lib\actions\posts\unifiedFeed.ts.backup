import { supabase } from '@/lib/supabase';
import { FeedQueryParams, FeedFilterType } from '@/lib/types/posts';
import { processOptimizedHybrid } from '@/lib/utils/feed/optimizedHybridAlgorithm';

export interface UnifiedPost {
  id: string;
  post_source: 'business' | 'customer';
  author_id: string;
  content: string;
  image_url: string | null;
  created_at: string;
  updated_at: string;
  city_slug: string | null;
  state_slug: string | null;
  locality_slug: string | null;
  pincode: string | null;
  product_ids: string[];
  mentioned_business_ids: string[];
  author_name: string | null;
  author_avatar: string | null;
  business_slug: string | null; // Business slug for business posts, null for customer posts
  phone: string | null; // Phone number for business posts, null for customer posts
  whatsapp_number: string | null; // WhatsApp number for business posts, null for customer posts
  business_plan: string | null; // Plan for business posts, null for customer posts
}

export interface UnifiedFeedResponse {
  success: boolean;
  message: string;
  error?: string;
  data?: {
    items: UnifiedPost[];
    totalCount: number;
    hasMore: boolean;
  };
}

export interface EnhancedFeedResponse {
  success: boolean;
  message: string;
  error?: string;
  data?: {
    items: UnifiedPost[];
    totalCount: number;
    hasMore: boolean;
    hasJustCreatedPost: boolean;
  };
}

export interface PostCreationState {
  hasJustCreated: boolean;
  newPostId?: string;
}

/**
 * Get unified feed posts (business + customer posts) with proper pagination
 * React Native version with complete smart feed algorithm from Next.js
 */
export async function getUnifiedFeedPosts(
  params: FeedQueryParams
): Promise<UnifiedFeedResponse> {
  const {
    filter = 'smart',
    page = 1,
    limit = 10,
    city_slug,
    state_slug,
    locality_slug,
    pincode
  } = params;

  try {
    // Get current user for smart feed and subscription filtering
    const { data: { user } } = await supabase.auth.getUser();

    // Build the query for unified posts
    let query = supabase
      .from('unified_posts')
      .select(`
        id,
        post_source,
        author_id,
        content,
        image_url,
        created_at,
        updated_at,
        city_slug,
        state_slug,
        locality_slug,
        pincode,
        product_ids,
        mentioned_business_ids,
        author_name,
        author_avatar,
        business_slug,
        phone,
        whatsapp_number,
        business_plan
      `, { count: 'exact' });

    // Apply filters based on filter type
    switch (filter) {
      case 'smart':
        if (user) {
          // Get user's subscribed businesses for smart feed
          const { data: subscriptions } = await supabase
            .from('subscriptions')
            .select('business_profile_id')
            .eq('user_id', user.id);

          const subscribedBusinessIds = subscriptions?.map(s => s.business_profile_id) || [];

          // Try to get user's location from both customer and business profiles
          const [customerProfile, businessProfile] = await Promise.all([
            supabase
              .from('customer_profiles')
              .select('city_slug, state_slug, locality_slug, pincode')
              .eq('id', user.id)
              .single(),
            supabase
              .from('business_profiles')
              .select('city_slug, state_slug, locality_slug, pincode')
              .eq('id', user.id)
              .single()
          ]);

          // Use provided location filters or fall back to user's location
          const userLocation = customerProfile.data || businessProfile.data;
          const effectivePincode = pincode || userLocation?.pincode;
          const effectiveLocalitySlug = locality_slug || userLocation?.locality_slug;
          const effectiveCitySlug = city_slug || userLocation?.city_slug;
          const effectiveStateSlug = state_slug || userLocation?.state_slug;

          // Apply location filter (prioritize pincode > locality > city > state)
          if (effectivePincode) {
            query = query.eq('pincode', effectivePincode);
          } else if (effectiveLocalitySlug) {
            query = query.eq('locality_slug', effectiveLocalitySlug);
          } else if (effectiveCitySlug) {
            query = query.eq('city_slug', effectiveCitySlug);
          } else if (effectiveStateSlug) {
            query = query.eq('state_slug', effectiveStateSlug);
          }

          // For smart feed, we'll apply subscription logic after fetching
        } else {
          // No user context, apply basic location filtering
          if (pincode) {
            query = query.eq('pincode', pincode);
          } else if (locality_slug) {
            query = query.eq('locality_slug', locality_slug);
          } else if (city_slug) {
            query = query.eq('city_slug', city_slug);
          } else if (state_slug) {
            query = query.eq('state_slug', state_slug);
          }
        }
        break;

      case 'subscribed':
        if (user) {
          // Only posts from subscribed businesses
          const { data: subscriptions } = await supabase
            .from('subscriptions')
            .select('business_profile_id')
            .eq('user_id', user.id);

          const subscribedBusinessIds = subscriptions?.map(s => s.business_profile_id) || [];

          if (subscribedBusinessIds.length > 0) {
            query = query.in('author_id', subscribedBusinessIds);
          } else {
            // No subscriptions, return empty result
            return {
              success: true,
              message: 'No subscribed businesses found',
              data: { items: [], totalCount: 0, hasMore: false }
            };
          }
        } else {
          // No user, return empty result
          return {
            success: true,
            message: 'User not authenticated',
            data: { items: [], totalCount: 0, hasMore: false }
          };
        }
        break;

      default:
        // Apply location filters for other filter types
        if (pincode) {
          query = query.eq('pincode', pincode);
        } else if (locality_slug) {
          query = query.eq('locality_slug', locality_slug);
        } else if (city_slug) {
          query = query.eq('city_slug', city_slug);
        } else if (state_slug) {
          query = query.eq('state_slug', state_slug);
        }
        break;
    }

    // Fetch exactly the target number of posts to prevent post loss
    // Algorithm will arrange these posts optimally without losing any content
    const from = (page - 1) * limit; // Standard pagination
    const to = from + limit - 1;

    // Execute query with chronological ordering (prioritization applied client-side)
    const { data, error, count } = await query
      .order('created_at', { ascending: false })
      .range(from, to);

    if (error) {
      console.error('Error fetching unified feed posts:', error);
      return {
        success: false,
        message: 'Failed to fetch posts',
        error: error.message
      };
    }

    // Apply optimized hybrid algorithm to ALL feed types
    // Processes exactly the fetched posts without losing any content
    // Business posts get plan prioritization, customer posts maintain chronological order
    // Works with location filters (locality, pincode, city, state, all)
    const prioritizedData = data ? processOptimizedHybrid(data, {
      enableDiversity: true,
      maintainChronologicalFlow: true
    }) : [];

    const totalCount = count || 0;
    // Standard pagination logic - no posts lost
    const hasMore = prioritizedData.length === limit && (from + limit) < totalCount;

    return {
      success: true,
      message: 'Posts fetched successfully',
      data: {
        items: prioritizedData,
        totalCount,
        hasMore
      }
    };
  } catch (error) {
    console.error('Error in getUnifiedFeedPosts:', error);
    return {
      success: false,
      message: 'Failed to fetch posts',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Removed old simplified processSmartFeed function
// Now using complete optimized hybrid algorithm from Next.js

/**
 * Get unified feed posts with author information
 * This is the main function used by components
 */
export async function getUnifiedFeedPostsWithAuthors(
  params: FeedQueryParams
): Promise<UnifiedFeedResponse> {
  return getUnifiedFeedPosts(params);
}
