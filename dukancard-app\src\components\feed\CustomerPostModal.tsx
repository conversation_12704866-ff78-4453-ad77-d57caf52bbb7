import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Image,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { ArrowLeft, Image as ImageIcon, X } from 'lucide-react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { Colors } from '@/src/constants/Colors';
import { createCustomerPost, updateCustomerPost } from '@/lib/actions/customerPosts';
import { uploadCustomerPostImage } from '@/backend/supabase/services/storage/customerPostImageUploadService';
import { supabase } from '@/lib/supabase';
import ImagePickerBottomSheet, { ImagePickerBottomSheetRef } from '@/src/components/pickers/ImagePickerBottomSheet';
import { LocationDisplay } from '@/src/components/ui/LocationDisplay';
import { createCustomerPostModalStyles } from '@/styles/feed/_customer-post-modal-styles';

interface CustomerPostModalProps {
  visible: boolean;
  onClose: () => void;
  customerName?: string;
  onPostCreated?: () => void;
}

export function CustomerPostModal({
  visible,
  onClose,
  customerName = 'Customer',
  onPostCreated
}: CustomerPostModalProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createCustomerPostModalStyles();

  // State
  const [content, setContent] = useState('');
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [imageDimensions, setImageDimensions] = useState<{ width: number; height: number } | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [customerAvatarUrl, setCustomerAvatarUrl] = useState<string | null>(null);
  const [customerDisplayName, setCustomerDisplayName] = useState(customerName);
  const imagePickerRef = React.useRef<ImagePickerBottomSheetRef>(null);

  // Colors
  const backgroundColor = isDark ? Colors.dark.background : Colors.light.background;
  const textColor = isDark ? Colors.dark.text : Colors.light.text;
  const mutedTextColor = isDark ? Colors.dark.tabIconDefault : Colors.light.tabIconDefault;
  const borderColor = isDark ? Colors.dark.border : Colors.light.border;
  const primaryColor = '#D4AF37';

  // Fetch customer profile data
  useEffect(() => {
    if (!visible) return;

    const fetchCustomerProfile = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return;

        const { data: profile } = await supabase
          .from('customer_profiles')
          .select('name, avatar_url')
          .eq('id', user.id)
          .single();

        if (profile) {
          setCustomerDisplayName(profile.name || customerName);
          setCustomerAvatarUrl(profile.avatar_url);
        }
      } catch (error) {
        console.error('Error fetching customer profile:', error);
      }
    };

    fetchCustomerProfile();
  }, [visible, customerName]);

  const handleClose = () => {
    setContent('');
    setImageUri(null);
    setImageDimensions(null);
    onClose();
  };

  const handleCameraSelection = async () => {
    try {
      const { openCamera } = await import('@/backend/supabase/services/storage/imageUploadService');
      const result = await openCamera({ allowsEditing: false }); // Don't allow editing to preserve original dimensions
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        setImageUri(asset.uri);
        // Store original dimensions
        if (asset.width && asset.height) {
          setImageDimensions({ width: asset.width, height: asset.height });
        }
      }
    } catch (error) {
      console.error('Camera error:', error);
      Alert.alert('Camera Error', 'Failed to take photo. Please try again.');
    }
  };

  const handleGallerySelection = async () => {
    try {
      const { openImageGallery } = await import('@/backend/supabase/services/storage/imageUploadService');
      const result = await openImageGallery({ allowsEditing: false }); // Don't allow editing to preserve original dimensions
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        setImageUri(asset.uri);
        // Store original dimensions
        if (asset.width && asset.height) {
          setImageDimensions({ width: asset.width, height: asset.height });
        }
      }
    } catch (error) {
      console.error('Gallery error:', error);
      Alert.alert('Gallery Error', 'Failed to select image. Please try again.');
    }
  };

  const handleRemoveImage = () => {
    setImageUri(null);
    setImageDimensions(null);
  };

  const handleSubmit = async () => {
    if (!content.trim() && !imageUri) {
      Alert.alert('Error', 'Please add some content or an image to your post.');
      return;
    }

    setIsSubmitting(true);

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        Alert.alert('Error', 'You must be logged in to create a post.');
        return;
      }

      const finalData = {
        content: content.trim(),
        image_url: imageUri && imageUri.startsWith('http') ? imageUri : null,
      };

      let result;

      if (imageUri && !imageUri.startsWith('http')) {
        // Two-step process for uploaded images
        const createResult = await createCustomerPost({ ...finalData, image_url: null });
        if (!createResult.success || !createResult.data) {
          Alert.alert('Error', createResult.error || createResult.message || 'Failed to create post');
          return;
        }

        const createdPost = createResult.data as { id: string; created_at: string };

        // Upload the image using the real post ID and creation date
        const uploadResult = await uploadCustomerPostImage(imageUri, createdPost.id, createdPost.created_at);
        if (uploadResult.success && uploadResult.url) {
          const updateResult = await updateCustomerPost(createdPost.id, {
            ...finalData,
            image_url: uploadResult.url
          });
          result = updateResult;
        } else {
          Alert.alert(
            'Warning',
            'Post created but image upload failed. You can edit the post later to add the image.',
            [
              {
                text: 'OK',
                onPress: () => {
                  handleClose();
                  if (onPostCreated) {
                    onPostCreated();
                  }
                }
              }
            ]
          );
          return;
        }
      } else {
        result = await createCustomerPost(finalData);
      }

      if (result.success) {
        Alert.alert(
          'Success',
          'Your post has been created successfully!',
          [
            {
              text: 'OK',
              onPress: () => {
                handleClose();
                if (onPostCreated) {
                  onPostCreated();
                }
              }
            }
          ]
        );
      } else {
        Alert.alert(
          'Error',
          result.message || result.error || 'Failed to create post',
          [
            {
              text: 'Try Again',
              style: 'default'
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error creating post:', error);
      Alert.alert('Error', 'Failed to create post. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!visible) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={handleClose}
    >
      <StatusBar style={isDark ? "light" : "dark"} backgroundColor={backgroundColor} />
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: borderColor }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleClose}
            disabled={isSubmitting}
          >
            <ArrowLeft color={textColor} size={24} />
          </TouchableOpacity>

          <Text style={[styles.headerTitle, { color: textColor }]}>Create Post</Text>

          <TouchableOpacity
            style={[
              styles.postButton,
              {
                backgroundColor: content.trim() || imageUri ? primaryColor : borderColor,
              }
            ]}
            onPress={handleSubmit}
            disabled={(!content.trim() && !imageUri) || isSubmitting}
          >
            <Text style={styles.postButtonText}>
              {isSubmitting ? 'Posting...' : 'Post'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Content */}
        <KeyboardAvoidingView
          style={styles.content}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
            {/* User Info */}
            <View style={styles.userInfo}>
              {customerAvatarUrl ? (
                <Image
                  source={{ uri: customerAvatarUrl }}
                  style={styles.avatar}
                />
              ) : (
                <View style={[styles.avatarPlaceholder, { backgroundColor: primaryColor }]}>
                  <Text style={styles.avatarText}>
                    {customerDisplayName.charAt(0).toUpperCase()}
                  </Text>
                </View>
              )}
              <Text style={[styles.userName, { color: textColor }]}>
                {customerDisplayName}
              </Text>
            </View>

            {/* Location Display */}
            <LocationDisplay />

            {/* Text Input with Character Counter */}
            <View style={styles.textInputContainer}>
              <TextInput
                style={[styles.textInput, { color: textColor, borderColor }]}
                placeholder={`What's on your mind, ${customerDisplayName}?`}
                placeholderTextColor={mutedTextColor}
                value={content}
                onChangeText={setContent}
                multiline
                textAlignVertical="top"
                autoFocus
                maxLength={2000}
              />
              <View style={styles.characterCounter}>
                <Text style={[
                  styles.characterCountText,
                  {
                    color: content.length > 2000 ? '#EF4444' : mutedTextColor
                  }
                ]}>
                  {content.length}/2000
                </Text>
              </View>
            </View>

            {/* Image Preview */}
            {imageUri && (
              <View style={styles.imageContainer}>
                <Image
                  source={{ uri: imageUri }}
                  style={[
                    styles.imagePreview,
                    imageDimensions && {
                      aspectRatio: imageDimensions.width / imageDimensions.height
                    }
                  ]}
                />
                <TouchableOpacity
                  style={styles.removeImageButton}
                  onPress={handleRemoveImage}
                >
                  <X color="#fff" size={20} />
                </TouchableOpacity>
              </View>
            )}
          </ScrollView>

          {/* Bottom Actions */}
          <View style={[styles.bottomActions, { borderTopColor: borderColor }]}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => imagePickerRef.current?.present()}
            >
              <ImageIcon color={primaryColor} size={24} />
              <Text style={[styles.actionButtonText, { color: textColor }]}>Add Photo</Text>
            </TouchableOpacity>
          </View>
        </KeyboardAvoidingView>

        {/* Image Picker Bottom Sheet */}
        <ImagePickerBottomSheet
          ref={imagePickerRef}
          onCameraPress={handleCameraSelection}
          onGalleryPress={handleGallerySelection}
          title="Add Photo to Post"
          cameraLabel="Take Photo"
          galleryLabel="Choose from Gallery"
        />
      </SafeAreaView>
    </Modal>
  );
}
