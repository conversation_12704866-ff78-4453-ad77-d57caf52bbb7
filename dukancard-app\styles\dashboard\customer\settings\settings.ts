import { StyleSheet } from 'react-native';
import { useTheme } from '@/src/hooks/useTheme';
import { responsiveFontSize } from '@/lib/theme/colors';

export const createSettingsPageStyles = (theme: ReturnType<typeof useTheme>) => {
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    cardContainer: {
      padding: theme.spacing.lg,
    },
    card: {
      backgroundColor: theme.colors.card,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      shadowColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
      padding: theme.spacing.lg,
      marginBottom: theme.spacing.md,
    },
    content: {
      gap: theme.spacing.lg,
    },
    header: {
      marginBottom: theme.spacing.md,
      paddingBottom: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerContent: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.sm,
    },
    iconContainer: {
      padding: theme.spacing.sm,
      borderRadius: 8,
      backgroundColor: theme.colors.primary + '20', // 20% opacity
    },
    headerTitle: {
      fontSize: responsiveFontSize(18),
      fontWeight: '600',
      color: theme.colors.foreground,
    },
  });
};
