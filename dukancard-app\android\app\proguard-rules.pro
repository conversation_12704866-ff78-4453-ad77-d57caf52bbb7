# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /usr/local/Cellar/android-sdk/24.3.3/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# React Native specific rules
-keep class com.facebook.react.bridge.** { *; }
-keep class com.facebook.react.uimanager.** { *; }
-keep class com.facebook.react.views.** { *; }
-keep class com.facebook.react.modules.** { *; }
-keep class com.facebook.react.turbomodule.** { *; }
-keep class com.facebook.react.shell.** { *; }
-keep class com.facebook.react.devsupport.** { *; }
-keep class com.facebook.react.packager.** { *; }
-keep class com.facebook.react.common.** { *; }
-keep class com.facebook.react.internal.turbomodule.** { *; }

# react-native-reanimated
-keep class com.swmansion.reanimated.** { *; }
-keep class com.swmansion.rnscreens.** { *; } # For react-native-screens

# react-native-gesture-handler
-keep class com.swmansion.gesturehandler.** { *; }

# @react-native-async-storage/async-storage
-keep class com.reactnativecommunity.asyncstorage.** { *; }

# @react-native-community/netinfo
-keep class com.reactnativecommunity.netinfo.** { *; }

# @react-native-google-signin/google-signin
-keep class com.reactnativegooglesignin.** { *; }

# @react-navigation/* (general rules for navigation libraries)
-keep class com.reactnavigation.** { *; }
-keep class expo.modules.kotlin.views.** { *; } # For Expo modules with native views

# @gorhom/bottom-sheet
-keep class com.gorhom.bottomsheet.** { *; }

# react-native-svg
-keep class com.horcrux.svg.** { *; }

# react-native-webview
-keep class com.reactnativecommunity.webview.** { *; }

# react-native-keychain
-keep class com.oblador.keychain.** { *; }

# Expo modules (general rules, adjust as needed for specific modules)
-keep class expo.modules.** { *; }
-keep class expo.modules.updates.** { *; }
-keep class expo.modules.filesystem.** { *; }
-keep class expo.modules.font.** { *; }
-keep class expo.modules.imageloader.** { *; }
-keep class expo.modules.imagepicker.** { *; }
-keep class expo.modules.location.** { *; }
-keep class expo.modules.medialibrary.** { *; }
-keep class expo.modules.securestore.** { *; }
-keep class expo.modules.splashscreen.** { *; }
-keep class expo.modules.webbrowser.** { *; }
-keep class expo.modules.camera.** { *; }
-keep class expo.modules.constants.** { *; }
-keep class expo.modules.crypto.** { *; }
-keep class expo.modules.device.** { *; }
-keep class expo.modules.haptics.** { *; }
-keep class expo.modules.lineargradient.** { *; }
-keep class expo.modules.linking.** { *; }
-keep class expo.modules.systemui.** { *; }
-keep class expo.modules.symbols.** { *; }
-keep class expo.modules.blur.** { *; }
-keep class expo.modules.buildproperties.** { *; }

# Supabase (if it has native components, though often it's pure JS)
# If you encounter issues, you might need to add specific rules for any native Supabase dependencies.
# -keep class io.supabase.** { *; }

# Add any project specific keep options here:
# For example, if you have custom native modules, you'd add rules for them here.