/**
 * Customer Likes Activity Screen
 * Displays all businesses liked by the customer with unlike functionality
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { useAuth } from '@/src/contexts/AuthContext';
import { DashboardScreenContainer } from '@/src/components/layout/ScreenContainer';
import { LikeCard } from '@/src/components/social/LikeCard';
import { SearchComponent } from '@/src/components/social/SearchComponent';
import { EmptyState } from '@/src/components/shared/ui/EmptyState';
import { LoadingSpinner } from '@/src/components/shared/ui/LoadingSpinner';
import { LikeListSkeleton } from '@/src/components/social/SkeletonLoaders';
import { likesService } from '@/backend/supabase/services/posts/socialService';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { Heart, ArrowLeft } from 'lucide-react-native';
import { createLikesActivityStyles } from '@/styles/dashboard/customer/activity/likes';
import { router } from 'expo-router';

// Types
interface BusinessProfileData {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  address_line: string | null;
}

interface LikeWithProfile {
  id: string;
  business_profiles: BusinessProfileData | null;
}

export default function CustomerLikesActivity() {
  const { user } = useAuth();
  const colorScheme = useColorScheme();
  const styles = createLikesActivityStyles(colorScheme ?? 'light');

  // State
  const [likes, setLikes] = useState<LikeWithProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [hasMore, setHasMore] = useState(false);

  // Fetch likes
  const fetchLikes = useCallback(async (
    page: number = 1, 
    search: string = '', 
    isRefresh: boolean = false
  ) => {
    if (!user) return;

    try {
      if (isRefresh) {
        setRefreshing(true);
      } else if (page === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const result = await likesService.fetchLikes(user.id, page, 10, search);

      if (page === 1 || isRefresh) {
        setLikes(result.items);
      } else {
        setLikes(prev => [...prev, ...result.items]);
      }

      setTotalCount(result.totalCount);
      setHasMore(result.hasMore);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error fetching likes:', error);
      Alert.alert('Error', 'Failed to load liked businesses. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
      setLoadingMore(false);
    }
  }, [user]);

  // Initial load
  useEffect(() => {
    if (user) {
      fetchLikes(1, searchTerm);
    }
  }, [user, fetchLikes, searchTerm]);

  // Handle search
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1);
    fetchLikes(1, term);
  }, [fetchLikes]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setCurrentPage(1);
    fetchLikes(1, searchTerm, true);
  }, [fetchLikes, searchTerm]);

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (hasMore && !loadingMore) {
      const nextPage = currentPage + 1;
      fetchLikes(nextPage, searchTerm);
    }
  }, [hasMore, loadingMore, currentPage, fetchLikes, searchTerm]);

  // Handle unlike
  const handleUnlike = useCallback(async (likeId: string) => {
    try {
      await likesService.unlike(likeId);
      setLikes(prev => prev.filter(like => like.id !== likeId));
      setTotalCount(prev => prev - 1);
      Alert.alert('Success', 'Successfully removed from liked businesses');
    } catch (error) {
      console.error('Error unliking:', error);
      Alert.alert('Error', 'Failed to unlike business. Please try again.');
    }
  }, []);

  // Render like item
  const renderLike = ({ item }: { item: LikeWithProfile }) => (
    <LikeCard
      like={item}
      onUnlike={handleUnlike}
    />
  );

  // Render footer
  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color="#D4AF37" />
      </View>
    );
  };

  // Render empty state
  const renderEmptyState = () => (
    <EmptyState
      icon={Heart}
      title="No Liked Businesses"
      description="You haven't liked any businesses yet. Explore and like businesses to see them here."
      actionText="Discover Businesses"
      onAction={() => {
        // TODO: Navigate to discover screen
      }}
    />
  );

  // Render header
  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => router.back()}
      >
        <ArrowLeft size={24} color={colorScheme === 'dark' ? '#ffffff' : '#1a1a1a'} />
      </TouchableOpacity>
      <Text style={styles.headerTitle}>My Likes</Text>
      <View style={styles.headerSpacer} />
    </View>
  );

  if (loading && likes.length === 0) {
    return (
      <DashboardScreenContainer scrollable={false}>
        {renderHeader()}
        <LikeListSkeleton />
      </DashboardScreenContainer>
    );
  }

  return (
    <DashboardScreenContainer scrollable={false}>
      {renderHeader()}
      <View style={styles.container}>
        {/* Header with count */}
        <View style={styles.header}>
          <Text style={styles.countText}>
            {totalCount} {totalCount === 1 ? 'Business' : 'Businesses'} Liked
          </Text>
        </View>

        {/* Search */}
        <View style={styles.searchContainer}>
          <SearchComponent
            value={searchTerm}
            onChangeText={handleSearch}
            placeholder="Search liked businesses..."
          />
        </View>

        {/* Likes List */}
        <FlatList
          data={likes}
          renderItem={renderLike}
          keyExtractor={(item) => item.id}
          contentContainerStyle={[
            styles.listContainer,
            likes.length === 0 && styles.emptyListContainer
          ]}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={['#D4AF37']}
              tintColor="#D4AF37"
            />
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.1}
          ListFooterComponent={renderFooter}
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </DashboardScreenContainer>
  );
}
