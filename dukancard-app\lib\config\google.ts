/**
 * Google OAuth Configuration
 * Uses hardcoded public keys from centralized configuration
 */

import { GOOGLE_OAUTH_CONFIG } from '@/src/config/publicKeys';

export interface GoogleConfig {
  iosClientId: string;
  webClientId: string;
  androidClientId: string;
  offlineAccess: boolean;
  hostedDomain?: string;
  forceCodeForRefreshToken: boolean;
}

/**
 * Get Google OAuth configuration
 * Returns the appropriate client IDs based on environment
 */
export const getGoogleConfig = async (): Promise<GoogleConfig> => {
  return {
    iosClientId: GOOGLE_OAUTH_CONFIG.iosClientId,
    webClientId: GOOGLE_OAUTH_CONFIG.webClientId,
    androidClientId: GOOGLE_OAUTH_CONFIG.androidClientId,
    offlineAccess: true,
    forceCodeForRefreshToken: true,
    // hostedDomain: undefined, // Set this if you want to restrict to a specific domain
  };
};

// Export individual client IDs for convenience
export const GOOGLE_IOS_CLIENT_ID = GOOGLE_OAUTH_CONFIG.iosClientId;
export const GOOGLE_WEB_CLIENT_ID = GOOGLE_OAUTH_CONFIG.webClientId;
export const GOOGLE_ANDROID_CLIENT_ID = GOOGLE_OAUTH_CONFIG.androidClientId;
