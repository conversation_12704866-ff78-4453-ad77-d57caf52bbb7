import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { Rocket, Clock, Star } from 'lucide-react-native';

interface ComingSoonProps {
  title?: string;
  description?: string;
  icon?: 'rocket' | 'clock' | 'star';
  showAnimation?: boolean;
}

export function ComingSoon({ 
  title = "Coming Soon", 
  description = "We're working hard to bring you something amazing. Stay tuned!",
  icon = 'rocket',
  showAnimation = true 
}: ComingSoonProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const renderIcon = () => {
    const iconColor = isDark ? '#D4AF37' : '#D4AF37';
    const iconSize = 64;

    switch (icon) {
      case 'clock':
        return <Clock size={iconSize} color={iconColor} />;
      case 'star':
        return <Star size={iconSize} color={iconColor} />;
      default:
        return <Rocket size={iconSize} color={iconColor} />;
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: isDark ? '#000000' : '#FFFFFF' }]}>
      <View style={styles.content}>
        {/* Icon */}
        <View style={[styles.iconContainer, { backgroundColor: isDark ? '#1a1a1a' : '#f8f9fa' }]}>
          {renderIcon()}
        </View>

        {/* Title */}
        <Text style={[styles.title, { color: isDark ? '#FFFFFF' : '#000000' }]}>
          {title}
        </Text>

        {/* Description */}
        <Text style={[styles.description, { color: isDark ? '#999999' : '#666666' }]}>
          {description}
        </Text>

        {/* Decorative dots */}
        <View style={styles.dotsContainer}>
          <View style={[styles.dot, { backgroundColor: isDark ? '#D4AF37' : '#D4AF37' }]} />
          <View style={[styles.dot, { backgroundColor: isDark ? '#666' : '#ccc' }]} />
          <View style={[styles.dot, { backgroundColor: isDark ? '#666' : '#ccc' }]} />
        </View>
      </View>
    </View>
  );
}

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  content: {
    alignItems: 'center',
    maxWidth: width * 0.8,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
});
