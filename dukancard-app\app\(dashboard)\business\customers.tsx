import { ScrollView, View } from 'react-native';
import { ThemedText } from '@/src/components/ThemedText';
import { ThemedView } from '@/src/components/ThemedView';
import { createCustomersStyles } from '@/styles/dashboard/business/customers-styles';
import { formatIndianNumberShort } from '@/lib/utils';

export default function BusinessCustomersScreen() {
  const styles = createCustomersStyles();

  return (
    <ThemedView style={styles.container}>
      <ScrollView contentContainerStyle={styles.content}>
        <View style={styles.header}>
          <ThemedText type="title">Customers</ThemedText>
          <ThemedText style={styles.subtitle}>
            Manage your customer relationships
          </ThemedText>
        </View>

        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <ThemedText type="defaultSemiBold" style={styles.statNumber}>
              {formatIndianNumberShort(89)}
            </ThemedText>
            <ThemedText style={styles.statLabel}>Total Followers</ThemedText>
          </View>

          <View style={styles.statCard}>
            <ThemedText type="defaultSemiBold" style={styles.statNumber}>
              {formatIndianNumberShort(156)}
            </ThemedText>
            <ThemedText style={styles.statLabel}>This Week</ThemedText>
          </View>
        </View>

        <View style={styles.customersContainer}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Recent Followers
          </ThemedText>

          <View style={styles.customerCard}>
            <View style={styles.customerAvatar}>
              <ThemedText style={styles.avatarText}>JD</ThemedText>
            </View>
            <View style={styles.customerInfo}>
              <ThemedText type="defaultSemiBold">John Doe</ThemedText>
              <ThemedText style={styles.customerEmail}><EMAIL></ThemedText>
              <ThemedText style={styles.customerDate}>Followed 2 days ago</ThemedText>
            </View>
          </View>

          <View style={styles.customerCard}>
            <View style={styles.customerAvatar}>
              <ThemedText style={styles.avatarText}>AS</ThemedText>
            </View>
            <View style={styles.customerInfo}>
              <ThemedText type="defaultSemiBold">Alice Smith</ThemedText>
              <ThemedText style={styles.customerEmail}><EMAIL></ThemedText>
              <ThemedText style={styles.customerDate}>Followed 1 week ago</ThemedText>
            </View>
          </View>

          <View style={styles.customerCard}>
            <View style={styles.customerAvatar}>
              <ThemedText style={styles.avatarText}>BJ</ThemedText>
            </View>
            <View style={styles.customerInfo}>
              <ThemedText type="defaultSemiBold">Bob Johnson</ThemedText>
              <ThemedText style={styles.customerEmail}><EMAIL></ThemedText>
              <ThemedText style={styles.customerDate}>Followed 2 weeks ago</ThemedText>
            </View>
          </View>
        </View>

        <View style={styles.emptyState}>
          <ThemedText style={styles.emptyText}>
            👥 Advanced customer management features will be implemented here
          </ThemedText>
        </View>
      </ScrollView>
    </ThemedView>
  );
}


