import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';

export interface UsePostOwnershipProps {
  postBusinessId?: string;
  postCustomerId?: string;
  postSource: 'business' | 'customer';
}

export interface UsePostOwnershipReturn {
  isOwner: boolean;
  isLoading: boolean;
  currentUserId: string | null;
}

/**
 * Hook to determine if the current user owns a specific post in React Native
 */
export function usePostOwnership({ 
  postBusinessId, 
  postCustomerId, 
  postSource 
}: UsePostOwnershipProps): UsePostOwnershipReturn {
  const [isOwner, setIsOwner] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);

  useEffect(() => {
    const checkOwnership = async () => {
      try {
        const { data: { user }, error } = await supabase.auth.getUser();

        if (error || !user) {
          setIsOwner(false);
          setCurrentUserId(null);
          setIsLoading(false);
          return;
        }

        setCurrentUserId(user.id);

        // Check ownership based on post source
        if (postSource === 'business') {
          // For business posts, check if current user's ID matches the post's business_id
          setIsOwner(!!(postBusinessId && user.id === postBusinessId));
        } else {
          // For customer posts, check if current user's ID matches the post's customer_id
          setIsOwner(!!(postCustomerId && user.id === postCustomerId));
        }
      } catch (error) {
        console.error("Error checking post ownership:", error);
        setIsOwner(false);
        setCurrentUserId(null);
      } finally {
        setIsLoading(false);
      }
    };

    checkOwnership();
  }, [postBusinessId, postCustomerId, postSource]);

  return {
    isOwner,
    isLoading,
    currentUserId,
  };
}

/**
 * Simple utility function to check if user owns a post
 */
export function checkPostOwnership(
  currentUserId: string | null, 
  postBusinessId: string | undefined,
  postCustomerId: string | undefined,
  postSource: 'business' | 'customer'
): boolean {
  if (!currentUserId) return false;
  
  if (postSource === 'business') {
    return !!(postBusinessId && currentUserId === postBusinessId);
  } else {
    return !!(postCustomerId && currentUserId === postCustomerId);
  }
}
