import { StyleSheet } from 'react-native';
import { useTheme } from '@/src/hooks/useTheme';
import { responsiveFontSize } from '@/lib/theme/colors';

export const createLinkEmailSectionStyles = (theme: ReturnType<typeof useTheme>) => {
  return StyleSheet.create({
    container: {
      backgroundColor: theme.colors.card,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      marginBottom: theme.spacing.md,
    },
    header: {
      padding: theme.spacing.lg,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.sm,
      marginBottom: theme.spacing.sm,
    },
    iconContainer: {
      padding: theme.spacing.sm,
      borderRadius: 8,
      backgroundColor: theme.colors.primary + '20', // 20% opacity
    },
    title: {
      fontSize: responsiveFontSize(18),
      fontWeight: '600',
      color: theme.colors.foreground,
    },
    description: {
      fontSize: responsiveFontSize(14),
      color: theme.colors.mutedForeground,
      lineHeight: responsiveFontSize(20),
    },
    content: {
      padding: theme.spacing.lg,
    },
    messageContainer: {
      backgroundColor: theme.colors.primary + '10', // 10% opacity
      borderRadius: 8,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.colors.primary + '30', // 30% opacity
    },
    messageContent: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.sm,
    },
    messageText: {
      fontSize: responsiveFontSize(14),
      color: theme.colors.primary,
      flex: 1,
    },
    section: {
      gap: theme.spacing.md,
    },
    label: {
      fontSize: responsiveFontSize(14),
      fontWeight: '500',
      color: theme.colors.foreground,
      marginBottom: theme.spacing.xs,
    },
    inputContainer: {
      marginBottom: theme.spacing.sm,
    },
    helperText: {
      fontSize: responsiveFontSize(12),
      color: theme.colors.mutedForeground,
      lineHeight: responsiveFontSize(16),
    },
    readOnlyContainer: {
      backgroundColor: theme.colors.muted,
      borderRadius: 8,
      padding: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    readOnlyText: {
      fontSize: responsiveFontSize(14),
      color: theme.colors.mutedForeground,
    },
    buttonContainer: {
      alignItems: 'flex-end',
      marginTop: theme.spacing.sm,
    },
    submitButton: {
      minWidth: 120,
    },
    otpHeader: {
      alignItems: 'center',
      marginBottom: theme.spacing.lg,
    },
    emailDisplay: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.foreground,
      marginVertical: theme.spacing.xs,
    },
    buttonRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: theme.spacing.md,
    },
    backButton: {
      padding: theme.spacing.sm,
    },
    backButtonText: {
      fontSize: 14,
      color: theme.colors.mutedForeground,
    },
  });
};
