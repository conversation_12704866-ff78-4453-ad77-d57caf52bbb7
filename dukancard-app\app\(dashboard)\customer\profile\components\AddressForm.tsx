import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert, ScrollView } from 'react-native';
import { MapPin, Save, Loader2, Globe, Building2 } from 'lucide-react-native';
import { updateCustomerAddress } from '@/lib/actions/customer/profileActions';
import { useTheme } from '@/src/hooks/useTheme';

interface AddressFormProps {
  initialData?: {
    address?: string | null;
    pincode?: string | null;
    city?: string | null;
    state?: string | null;
    locality?: string | null;
  };
}

export default function AddressForm({ initialData }: AddressFormProps) {
  const [address, setAddress] = useState(initialData?.address || '');
  const [pincode, setPincode] = useState(initialData?.pincode || '');
  const [city, setCity] = useState(initialData?.city || '');
  const [state, setState] = useState(initialData?.state || '');
  const [locality, setLocality] = useState(initialData?.locality || '');
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const theme = useTheme();

  

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!pincode.trim()) {
      newErrors.pincode = 'Pincode is required';
    } else if (!/^\d{6}$/.test(pincode)) {
      newErrors.pincode = 'Must be a valid 6-digit pincode';
    }

    if (!city.trim()) {
      newErrors.city = 'City is required';
    }

    if (!state.trim()) {
      newErrors.state = 'State is required';
    }

    if (!locality.trim()) {
      newErrors.locality = 'Locality is required';
    }

    if (address.length > 100) {
      newErrors.address = 'Address cannot exceed 100 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const formData = new FormData();
      formData.append('address', address);
      formData.append('pincode', pincode);
      formData.append('city', city);
      formData.append('state', state);
      formData.append('locality', locality);

      const initialState = {
        message: null,
        errors: {},
        success: false
      };

      const result = await updateCustomerAddress(initialState, formData);

      if (result.success) {
        Alert.alert('Success', 'Address updated successfully!');
      } else {
        Alert.alert('Error', result.message || 'Failed to update address');
      }
    } catch (error) {
      console.error('Error updating address:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const renderInput = (
    label: string,
    value: string,
    onChangeText: (text: string) => void,
    placeholder: string,
    error?: string,
    icon?: React.ReactNode,
    editable: boolean = true,
    keyboardType: 'default' | 'numeric' = 'default'
  ) => (
    <View style={{ gap: theme.spacing.xs }}>
      <View style={{ flexDirection: 'row', alignItems: 'center', gap: theme.spacing.xs }}>
        {icon}
        <Text style={{
          fontSize: theme.typography.fontSize.sm,
          fontWeight: '500',
          color: theme.colors.textSecondary,
        }}>
          {label}
        </Text>
      </View>
      <TextInput
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor={theme.colors.textSecondary}
        keyboardType={keyboardType}
        editable={editable}
        style={{
          paddingHorizontal: theme.spacing.md,
          paddingVertical: theme.spacing.sm,
          borderRadius: theme.borderRadius.md,
          borderWidth: 1,
          borderColor: error ? theme.colors.error : theme.colors.border,
          backgroundColor: editable ? theme.colors.card : theme.colors.muted,
          color: theme.colors.textPrimary,
          fontSize: theme.typography.fontSize.base,
        }}
      />
      {error && (
        <Text style={{ color: theme.colors.error, fontSize: theme.typography.fontSize.xs }}>
          {error}
        </Text>
      )}
    </View>
  );

  return (
    <ScrollView style={{ gap: theme.spacing.xl }}>
      {/* Header */}
      <View style={{ flexDirection: 'row', alignItems: 'center', gap: theme.spacing.xs, marginBottom: theme.spacing.xl }}>
        <MapPin size={20} color={theme.colors.primary} />
        <Text style={{
          fontSize: theme.typography.fontSize.lg,
          fontWeight: '600',
          color: theme.colors.textPrimary,
        }}>
          Address Information
        </Text>
      </View>

      <View style={{ gap: theme.spacing.lg }}>
        {/* Address Field (Optional) */}
        {renderInput(
          'Address (Optional)',
          address,
          setAddress,
          'e.g., House/Flat No., Street Name',
          errors.address
        )}

        {/* Pincode Field */}
        {renderInput(
          'Pincode *',
          pincode,
          setPincode,
          'e.g., 751001',
          errors.pincode,
          <Globe size={16} color={theme.colors.primary} />,
          true,
          'numeric'
        )}

        {/* City and State Row */}
        <View style={{ flexDirection: 'row', gap: theme.spacing.sm }}>
          <View style={{ flex: 1 }}>
            {renderInput(
              'City *',
              city,
              setCity,
              'Auto-filled from Pincode',
              errors.city,
              <MapPin size={16} color={theme.colors.textSecondary} />,
              false
            )}
          </View>
          <View style={{ flex: 1 }}>
            {renderInput(
              'State *',
              state,
              setState,
              'Auto-filled from Pincode',
              errors.state,
              <MapPin size={16} color={theme.colors.textSecondary} />,
              false
            )}
          </View>
        </View>

        {/* Locality Field */}
        {renderInput(
          'Locality / Area *',
          locality,
          setLocality,
          'Enter your locality',
          errors.locality,
          <Building2 size={16} color={theme.colors.primary} />
        )}

        {/* Submit Button */}
        <View style={{ alignItems: 'flex-end', marginTop: theme.spacing.md }}>
          <TouchableOpacity
            onPress={handleSubmit}
            disabled={isLoading}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingHorizontal: theme.spacing.md,
              paddingVertical: theme.spacing.sm,
              borderRadius: theme.borderRadius.lg,
              borderWidth: 1,
              borderColor: theme.colors.primary + '80',
              backgroundColor: theme.colors.card,
              opacity: isLoading ? 0.7 : 1,
            }}
          >
            {isLoading ? (
              <Loader2 size={16} color={theme.colors.primary} style={{ marginRight: theme.spacing.xs }} />
            ) : (
              <Save size={16} color={theme.colors.primary} style={{ marginRight: theme.spacing.xs }} />
            )}
            <Text style={{
              color: theme.colors.primary,
              fontSize: theme.typography.fontSize.sm,
              fontWeight: '500',
            }}>
              {isLoading ? 'Updating...' : 'Update Address'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}
