import { useToast } from '@/src/components/ui/Toast';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import {
    requestLocationPermission,
    reverseGeocodeCoordinates
} from '@/backend/supabase/services/location/locationService';
import * as Location from 'expo-location';
import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
    ActivityIndicator,
    Text,
    TouchableOpacity
} from 'react-native';

export interface AddressDetails {
  pincode: string;
  city: string;
  state: string;
  locality: string;
}

// Export the interface for use in other components


export interface LocationPickerProps {
  onLocationDetected: (latitude: number, longitude: number) => void;
  onAddressDetected?: (address: AddressDetails) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
}

export function LocationPicker({
  onLocationDetected,
  onAddressDetected,
  onError,
  disabled = false
}: LocationPickerProps) {
  const [isLoading, setIsLoading] = useState(false);
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const toast = useToast();

  const handleLocationRequest = async () => {
    if (disabled || isLoading) return;

    setIsLoading(true);

    try {
      // Request location permission
      const permission = await requestLocationPermission();

      if (!permission.granted) {
        let errorMessage = 'Location permission is mandatory for accurate business location. This ensures your customers can find you easily.';

        if (!permission.canAskAgain) {
          errorMessage += ' Please go to Settings > Apps > DukanCard > Permissions > Location and enable "Allow all the time" or "Allow only while using the app".';
        } else {
          errorMessage += ' Please grant location permission when prompted.';
        }

        toast.error('Location Permission Required', errorMessage);
        onError?.(errorMessage);
        return;
      }

      // Get current location (latitude and longitude)
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      const { latitude, longitude } = location.coords;

      // Always call onLocationDetected with coordinates
      onLocationDetected(latitude, longitude);

      // If onAddressDetected is provided, perform reverse geocoding
      if (onAddressDetected) {
        const addressResult = await reverseGeocodeCoordinates(latitude, longitude);

        if (addressResult.success && addressResult.pincode && addressResult.city && addressResult.state && addressResult.locality) {
          onAddressDetected({
            pincode: addressResult.pincode,
            city: addressResult.city,
            state: addressResult.state,
            locality: addressResult.locality,
          });
          toast.success('Location & Address Detected', 'Your exact location and nearest address details have been auto-filled from our database.');
        } else {
          // GPS coordinates captured but address detection failed
          toast.warning('Location Detected', 'GPS coordinates captured successfully, but could not auto-detect address details. Please enter your pincode manually to continue.');
        }
      } else {
        toast.success('Location Detected', 'Your location coordinates have been captured successfully.');
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred.';

      toast.error('Location Error', `Failed to get your location: ${errorMessage}`);
      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <TouchableOpacity
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 12,
        paddingHorizontal: 16,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: isDark ? '#C29D5B40' : '#C29D5B60',
        backgroundColor: isDark ? '#C29D5B10' : '#C29D5B10',
        opacity: disabled || isLoading ? 0.6 : 1,
      }}
      onPress={handleLocationRequest}
      disabled={disabled || isLoading}
      activeOpacity={0.7}
    >
      {isLoading ? (
        <ActivityIndicator 
          size="small" 
          color="#C29D5B" 
          style={{ marginRight: 8 }} 
        />
      ) : (
        <Ionicons 
          name="location" 
          size={18} 
          color="#C29D5B" 
          style={{ marginRight: 8 }} 
        />
      )}
      
      <Text style={{
        color: '#C29D5B',
        fontSize: 14,
        fontWeight: '600',
        textAlign: 'center',
      }}>
        {isLoading ? 'Getting Location...' : 'Use Current Location'}
      </Text>
    </TouchableOpacity>
  );
}

