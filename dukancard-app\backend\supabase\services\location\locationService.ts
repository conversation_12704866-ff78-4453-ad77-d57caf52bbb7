import { supabase } from "@/lib/supabase";
import * as Location from "expo-location";
import { Alert } from "react-native";
import { Pincode } from "../../../../src/types/database";
import { QueryData } from "@supabase/supabase-js";

export class LocationService {
  static async getPincodeDetails(pincode: string) {
    const query = supabase
      .from("pincodes")
      .select("OfficeName, DivisionName, StateName")
      .eq("Pincode", pincode)
      .order("OfficeName");

    type PincodeDetails = QueryData<typeof query>;

    const { data, error } = await query;

    if (error) {
      throw error;
    }

    return data as PincodeDetails;
  }
}

export async function checkLocationPermission(): Promise<Location.PermissionResponse> {
  try {
    const { status, canAskAgain } =
      await Location.getForegroundPermissionsAsync();
    return {
      granted: status === Location.PermissionStatus.GRANTED,
      canAskAgain,
      status,
      expires: "never",
    };
  } catch (error) {
    return {
      granted: false,
      canAskAgain: false,
      status: Location.PermissionStatus.DENIED,
      expires: "never",
    };
  }
}

export async function requestLocationPermission(): Promise<Location.PermissionResponse> {
  try {
    const { status, canAskAgain } =
      await Location.requestForegroundPermissionsAsync();
    return {
      granted: status === Location.PermissionStatus.GRANTED,
      canAskAgain,
      status,
      expires: "never",
    };
  } catch (error) {
    return {
      granted: false,
      canAskAgain: false,
      status: Location.PermissionStatus.DENIED,
      expires: "never",
    };
  }
}

export async function getCurrentLocation(): Promise<Location.LocationObject | null> {
  try {
    const location = await Location.getCurrentPositionAsync({
      accuracy: Location.Accuracy.Balanced,
    });
    return location;
  } catch (error) {
    console.error("Error getting current location:", error);
    return null;
  }
}

export async function getCoordinatesFromPincodeAndLocality(
  pincode: string,
  locality: string
): Promise<{
  success: boolean;
  latitude?: number;
  longitude?: number;
  error?: string;
}> {
  try {
    const query = supabase
      .from("pincodes")
      .select("Latitude, Longitude")
      .eq("Pincode", pincode)
      .eq("OfficeName", locality)
      .single();

    type Coordinates = QueryData<typeof query>;

    const { data, error } = await query;

    if (error) {
      console.error("Error fetching coordinates from Supabase:", error);
      return { success: false, error: error.message };
    }

    const coordinates = data as Coordinates;

    if (coordinates && coordinates.Latitude && coordinates.Longitude) {
      return {
        success: true,
        latitude: coordinates.Latitude,
        longitude: coordinates.Longitude,
      };
    } else {
      return {
        success: false,
        error: "Coordinates not found for the given pincode and locality.",
      };
    }
  } catch (error) {
    console.error(
      "Unexpected error in getCoordinatesFromPincodeAndLocality:",
      error
    );
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Reverse geocode coordinates to get pincode and address details
 * @param latitude The latitude coordinate
 * @param longitude The longitude coordinate
 * @returns Address details including pincode, city, state, and locality
 */
export async function reverseGeocodeCoordinates(
  latitude: number,
  longitude: number
): Promise<{
  success: boolean;
  pincode?: string;
  city?: string;
  state?: string;
  locality?: string;
  error?: string;
}> {
  try {
    // First try to find the closest pincode from our database using GPS coordinates
    const closestPincodeResult = await findClosestPincodeFromGPS(
      latitude,
      longitude
    );

    if (closestPincodeResult.success) {
      return closestPincodeResult;
    }

    // Fallback to expo-location's reverse geocoding if database lookup fails
    const reverseGeocode = await Location.reverseGeocodeAsync({
      latitude,
      longitude,
    });

    if (!reverseGeocode || reverseGeocode.length === 0) {
      return {
        success: false,
        error: "No address found for the given coordinates.",
      };
    }

    const address = reverseGeocode[0];
    const pincode = address.postalCode;

    if (!pincode || !/^\d{6}$/.test(pincode)) {
      return {
        success: false,
        error: "Could not determine a valid pincode from your location.",
      };
    }

    // Use reverse geocoding data as fallback
    const state = address.region || "";
    const city = address.city || address.district || "";
    const locality = address.name || address.street || "";

    return {
      success: true,
      pincode,
      city,
      state,
      locality,
    };
  } catch (error) {
    console.error("Error in reverse geocoding:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to determine address from location",
    };
  }
}

/**
 * Find the closest pincode and locality from GPS coordinates using our pincodes database
 */
export async function findClosestPincodeFromGPS(
  latitude: number,
  longitude: number
): Promise<{
  success: boolean;
  pincode?: string;
  city?: string;
  state?: string;
  locality?: string;
  distance?: number;
  error?: string;
}> {
  try {
    // Use the Supabase function to find the closest locality efficiently
    const { data: result, error } = await supabase.rpc(
      "find_closest_locality",
      {
        user_lat: latitude,
        user_lng: longitude,
        max_distance_km: 10,
      }
    );

    if (error) {
      console.error("Database error finding closest locality:", error);
      return {
        success: false,
        error: "Database error finding closest location.",
      };
    }

    if (!result || result.length === 0) {
      return {
        success: false,
        error: "No nearby locations found within 10km radius.",
      };
    }

    // Get the closest match
    const closest = result[0];

    return {
      success: true,
      pincode: closest.pincode,
      city: closest.division_name,
      state: closest.state_name,
      locality: closest.office_name,
      distance: closest.distance_km,
    };
  } catch (error) {
    console.error("Error finding closest locality:", error);
    return { success: false, error: "Failed to find closest location." };
  }
}

export async function getLocationStatus(): Promise<boolean> {
  try {
    return await Location.hasServicesEnabledAsync();
  } catch (error) {
    console.error("Error checking location status:", error);
    return false;
  }
}

export function showLocationPermissionDialog(): void {
  Alert.alert(
    "Location Permission Required",
    "This app needs access to your location to provide accurate delivery services. Please enable location access in your device settings.",
    [
      {
        text: "Cancel",
        style: "cancel",
      },
      {
        text: "Open Settings",
        onPress: () => {
          // On React Native, we can't directly open settings
          // This would need to be implemented with a native module
        },
      },
    ]
  );
}
