import { useTheme } from '@/src/hooks/useTheme';
import React from 'react';
import {
    Pressable,
    ScrollView,
    StyleSheet,
    Text,
    View,
    ViewStyle,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface OnboardingContainerProps {
  children: React.ReactNode;
  currentStep: number;
  totalSteps: number;
  onBack?: () => void;
  onNext?: () => void;
  nextButtonText?: string;
  backButtonText?: string;
  isNextDisabled?: boolean;
  showProgress?: boolean;
  scrollable?: boolean;
  contentContainerStyle?: ViewStyle;
  style?: ViewStyle;
}

/**
 * OnboardingContainer - Layout component for onboarding screens
 * Provides step progress, navigation buttons, and consistent styling
 */
export function OnboardingContainer({
  children,
  currentStep,
  totalSteps,
  onBack,
  onNext,
  nextButtonText = 'Next',
  backButtonText = 'Back',
  isNextDisabled = false,
  showProgress = true,
  scrollable = true,
  contentContainerStyle,
  style,
}: OnboardingContainerProps) {
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  const styles = StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollView: {
      flex: 1,
    },
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    progressContainer: {
      marginBottom: theme.spacing.md,
    },
    progressBar: {
      height: 4,
      backgroundColor: theme.colors.muted,
      borderRadius: theme.borderRadius.full,
      overflow: 'hidden',
    },
    progressFill: {
      height: '100%',
      backgroundColor: theme.colors.primary,
      borderRadius: theme.borderRadius.full,
    },
    stepText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginTop: theme.spacing.sm,
    },
    content: {
      flex: 1,
    },
    scrollContainer: {
      flexGrow: 1,
      paddingHorizontal: theme.spacing.md, // Reduced from lg
      paddingVertical: theme.spacing.lg, // Reduced from xl
    },
    contentContainer: {
      minHeight: '100%',
      justifyContent: 'space-between',
    },
    nonScrollableContent: {
      flex: 1,
      paddingHorizontal: theme.spacing.md, // Reduced from lg
      paddingVertical: theme.spacing.lg, // Reduced from xl
    },
    footer: {
      paddingHorizontal: theme.spacing.md, // Reduced from lg
      paddingVertical: theme.spacing.md, // Reduced from lg
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      backgroundColor: theme.isDark ? '#000000' : '#ffffff'
    },
    buttonContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      width: '100%',
      gap: theme.spacing.md, // Add gap between buttons
    },
    button: {
      flex: 1, // Equal width for both buttons
      paddingHorizontal: theme.spacing.xl,
      paddingVertical: theme.spacing.md,
      borderRadius: theme.borderRadius.lg,
      alignItems: 'center',
    },
    backButton: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    nextButton: {
      backgroundColor: theme.colors.primary,
      ...theme.shadows.sm,
    },
    nextButtonDisabled: {
      backgroundColor: theme.colors.muted,
      opacity: 0.6,
    },
    buttonText: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.semibold,
    },
    backButtonText: {
      color: theme.colors.textSecondary,
    },
    nextButtonText: {
      color: theme.colors.textOnPrimary,
    },
    nextButtonTextDisabled: {
      color: theme.colors.textMuted,
    },
  });

  const progressPercentage = (currentStep / totalSteps) * 100;

  const content = scrollable ? (
    <ScrollView
      style={styles.scrollView}
      contentContainerStyle={styles.scrollContainer}
      showsVerticalScrollIndicator={false}
      keyboardShouldPersistTaps="handled"
    >
      <View style={styles.content}>
        <View style={[styles.contentContainer, contentContainerStyle]}>
          {children}
        </View>
      </View>
    </ScrollView>
  ) : (
    <View style={styles.content}>
      <View style={[styles.nonScrollableContent, contentContainerStyle]}>
        {children}
      </View>
    </View>
  );

  const safeAreaStyle = {
    paddingTop: insets.top,
    paddingBottom: insets.bottom,
    paddingLeft: insets.left,
    paddingRight: insets.right,
  };

  return (
    <View style={[styles.safeArea, safeAreaStyle]}>
      <View style={[styles.container, style]}>
        {/* Header with progress */}
        {showProgress && (
          <View style={styles.header}>
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    { width: `${progressPercentage}%` }
                  ]}
                />
              </View>
              <Text style={styles.stepText}>
                Step {currentStep} of {totalSteps}
              </Text>
            </View>
          </View>
        )}

        {/* Main content */}
        {content}

        {/* Footer with navigation buttons */}
        <View style={styles.footer}>
          <View style={styles.buttonContainer}>
            {/* Back button */}
            {onBack ? (
              <Pressable
                style={[styles.button, styles.backButton]}
                onPress={onBack}
              >
                <Text style={[styles.buttonText, styles.backButtonText]}>
                  {backButtonText}
                </Text>
              </Pressable>
            ) : (
              <View style={{ minWidth: 100 }} />
            )}

            {/* Next button */}
            {onNext && (
              <Pressable
                style={[
                  styles.button,
                  styles.nextButton,
                  isNextDisabled && styles.nextButtonDisabled,
                ]}
                onPress={isNextDisabled ? undefined : onNext}
                disabled={isNextDisabled}
              >
                <Text
                  style={[
                    styles.buttonText,
                    isNextDisabled
                      ? styles.nextButtonTextDisabled
                      : styles.nextButtonText
                  ]}
                >
                  {nextButtonText}
                </Text>
              </Pressable>
            )}
          </View>
        </View>
      </View>
    </View>
  );
}

/**
 * OnboardingCard - Card component for onboarding form sections
 */
interface OnboardingCardProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  style?: ViewStyle;
}

export function OnboardingCard({ 
  children, 
  title, 
  subtitle, 
  style 
}: OnboardingCardProps) {
  const theme = useTheme();
  
  const styles = StyleSheet.create({
    card: {
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.xl,
      padding: theme.spacing.xl,
      marginVertical: theme.spacing.md,
      ...theme.shadows.sm,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    header: {
      marginBottom: theme.spacing.lg,
    },
    title: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: theme.typography.fontWeight.bold,
      color: theme.colors.textPrimary,
      marginBottom: theme.spacing.sm,
    },
    subtitle: {
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.textSecondary,
      lineHeight: theme.typography.lineHeight.relaxed * theme.typography.fontSize.base,
    },
  });

  return (
    <View style={[styles.card, style]}>
      {(title || subtitle) && (
        <View style={styles.header}>
          {title && <Text style={styles.title}>{title}</Text>}
          {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
        </View>
      )}
      {children}
    </View>
  );
}