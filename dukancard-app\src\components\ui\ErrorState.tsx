import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
} from 'react-native';
import {
  AlertTriangle,
  Wifi,
  RefreshCw,
  Search,
  Lock,
  Server,
  AlertCircle,
} from 'lucide-react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';

export type ErrorType = 
  | 'network'
  | 'notFound'
  | 'unauthorized'
  | 'server'
  | 'validation'
  | 'generic';

interface ErrorStateProps {
  type?: ErrorType;
  title?: string;
  message?: string;
  actionText?: string;
  onAction?: () => void;
  showRetry?: boolean;
  onRetry?: () => void;
  illustration?: any; // Image source
  style?: any;
}

const errorConfigs = {
  network: {
    icon: Wifi,
    title: 'No Internet Connection',
    message: 'Please check your internet connection and try again.',
    actionText: 'Retry',
  },
  notFound: {
    icon: Search,
    title: 'Not Found',
    message: 'The content you\'re looking for could not be found.',
    actionText: 'Go Back',
  },
  unauthorized: {
    icon: Lock,
    title: 'Access Denied',
    message: 'You don\'t have permission to access this content.',
    actionText: 'Sign In',
  },
  server: {
    icon: Server,
    title: 'Server Error',
    message: 'Something went wrong on our end. Please try again later.',
    actionText: 'Retry',
  },
  validation: {
    icon: AlertCircle,
    title: 'Invalid Data',
    message: 'Please check your input and try again.',
    actionText: 'Try Again',
  },
  generic: {
    icon: AlertTriangle,
    title: 'Something went wrong',
    message: 'An unexpected error occurred. Please try again.',
    actionText: 'Retry',
  },
};

export const ErrorState: React.FC<ErrorStateProps> = ({
  type = 'generic',
  title,
  message,
  actionText,
  onAction,
  showRetry = true,
  onRetry,
  illustration,
  style,
}) => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const config = errorConfigs[type];
  const IconComponent = config.icon;

  const displayTitle = title || config.title;
  const displayMessage = message || config.message;
  const displayActionText = actionText || config.actionText;

  // Theme colors
  const backgroundColor = isDark ? '#000000' : '#FFFFFF';
  const textColor = isDark ? '#FFFFFF' : '#1F2937';
  const mutedTextColor = isDark ? '#A1A1AA' : '#6B7280';
  const iconBackgroundColor = isDark ? '#1F1F1F' : getIconContainerStyle(type).backgroundColor;
  const secondaryButtonBg = isDark ? '#1F1F1F' : '#FFFFFF';
  const secondaryButtonBorder = isDark ? '#374151' : '#D1D5DB';
  const secondaryButtonText = isDark ? '#D1D5DB' : '#6B7280';

  const handleAction = () => {
    if (onAction) {
      onAction();
    } else if (onRetry) {
      onRetry();
    }
  };

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    }
  };

  return (
    <View style={[styles.container, { backgroundColor }, style]}>
      <View style={styles.content}>
        {/* Illustration or Icon */}
        {illustration ? (
          <Image source={illustration} style={styles.illustration} />
        ) : (
          <View style={[styles.iconContainer, { backgroundColor: iconBackgroundColor }]}>
            <IconComponent
              color={getIconColor(type)}
              size={48}
            />
          </View>
        )}

        {/* Title */}
        <Text style={[styles.title, { color: textColor }]}>{displayTitle}</Text>

        {/* Message */}
        <Text style={[styles.message, { color: mutedTextColor }]}>{displayMessage}</Text>

        {/* Action Buttons */}
        <View style={styles.actionsContainer}>
          {/* Primary Action */}
          <TouchableOpacity
            style={[styles.button, styles.primaryButton]}
            onPress={handleAction}
            activeOpacity={0.8}
          >
            <Text style={styles.primaryButtonText}>{displayActionText}</Text>
          </TouchableOpacity>

          {/* Retry Button (if different from primary action) */}
          {showRetry && onRetry && onAction && (
            <TouchableOpacity
              style={[
                styles.button,
                styles.secondaryButton,
                {
                  backgroundColor: secondaryButtonBg,
                  borderColor: secondaryButtonBorder
                }
              ]}
              onPress={handleRetry}
              activeOpacity={0.8}
            >
              <RefreshCw color={secondaryButtonText} size={16} />
              <Text style={[styles.secondaryButtonText, { color: secondaryButtonText }]}>Retry</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </View>
  );
};

const getIconContainerStyle = (type: ErrorType) => {
  switch (type) {
    case 'network':
      return { backgroundColor: '#FEF3C7' };
    case 'notFound':
      return { backgroundColor: '#E0E7FF' };
    case 'unauthorized':
      return { backgroundColor: '#FEE2E2' };
    case 'server':
      return { backgroundColor: '#FECACA' };
    case 'validation':
      return { backgroundColor: '#FED7AA' };
    default:
      return { backgroundColor: '#F3F4F6' };
  }
};

const getIconColor = (type: ErrorType) => {
  switch (type) {
    case 'network':
      return '#F59E0B';
    case 'notFound':
      return '#6366F1';
    case 'unauthorized':
      return '#EF4444';
    case 'server':
      return '#DC2626';
    case 'validation':
      return '#F97316';
    default:
      return '#6B7280';
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 40,
  },
  content: {
    alignItems: 'center',
    maxWidth: 320,
  },
  illustration: {
    width: 200,
    height: 150,
    marginBottom: 24,
    resizeMode: 'contain',
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 12,
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  actionsContainer: {
    width: '100%',
    gap: 12,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    gap: 8,
  },
  primaryButton: {
    backgroundColor: '#D4AF37',
  },
  primaryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    borderWidth: 1,
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
