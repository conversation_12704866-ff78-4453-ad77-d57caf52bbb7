import { StyleSheet } from 'react-native';

export const createBusinessPostCreatorStyles = () => {
  return StyleSheet.create({
    container: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderRadius: 0,
      borderWidth: 0,
    },
    collapsedContent: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    avatar: {
      width: 48,
      height: 48,
      borderRadius: 24,
      borderWidth: 1.5,
      borderColor: '#D4AF37', // Golden border to match brand
      marginRight: 12,
    },
    avatarPlaceholder: {
      width: 48,
      height: 48,
      borderRadius: 24,
      borderWidth: 1.5,
      borderColor: '#D4AF37', // Golden border to match brand
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12,
    },
    textContainer: {
      flex: 1,
    },
    placeholderText: {
      fontSize: 16,
      fontWeight: '400',
    },
    addButton: {
      width: 32,
      height: 32,
      borderRadius: 16,
      justifyContent: 'center',
      alignItems: 'center',
    },

  });
};
