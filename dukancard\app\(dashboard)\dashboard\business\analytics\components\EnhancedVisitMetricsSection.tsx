"use client";

import { motion } from "framer-motion";
import { Users } from "lucide-react";
import { formatIndianNumberShort } from "@/lib/utils";
import EnhancedMetricCard from "./EnhancedMetricCard";

interface VisitMetricsSectionProps {
  totalUniqueVisits: number;
  todayUniqueVisits: number;
  yesterdayUniqueVisits: number;
  currentMonthUniqueVisits: number;
  isVisitUpdated: boolean;
}

export default function EnhancedVisitMetricsSection({
  totalUniqueVisits,
  todayUniqueVisits,
  yesterdayUniqueVisits,
  isVisitUpdated,
  currentMonthUniqueVisits,
}: VisitMetricsSectionProps) {
  // Calculate trend percentage for today vs yesterday
  const calculateTrend = () => {
    if (yesterdayUniqueVisits === 0) return { value: 0, isPositive: true };

    const difference = todayUniqueVisits - yesterdayUniqueVisits;
    const percentage = Math.round((difference / yesterdayUniqueVisits) * 100);

    return {
      value: percentage,
      isPositive: percentage >= 0,
    };
  };

  const todayTrend = calculateTrend();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const headerVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24,
      }
    },
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="space-y-4">
      {/* Section Header */}
      <motion.div
        variants={headerVariants}
      >
        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800">
        <div className="p-2 rounded-lg bg-primary/10 text-primary self-start">
          <Users className="w-4 sm:w-5 h-4 sm:h-5" />
        </div>
        <div className="flex-1">
          <h3 className="text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100">
            Visitor Metrics
          </h3>
          <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
            Key statistics about your card visitors
          </p>
        </div>
        </div>
      </motion.div>

      {/* Total Unique Visits - Standalone */}
      <div className="mb-4">
        <EnhancedMetricCard
          title="Total Unique Visits"
          value={formatIndianNumberShort(totalUniqueVisits)}
          icon={Users}
          description="All-time unique visitors to your card"
          color="blue"
          isUpdated={isVisitUpdated}
        />
      </div>

      {/* Daily and Monthly Metrics Cards */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
        {/* Today's Unique Visits */}
        <EnhancedMetricCard
          title="Today's Unique Visits"
          value={formatIndianNumberShort(todayUniqueVisits)}
          icon={Users}
          description="Unique visitors today"
          color="green"
          isUpdated={isVisitUpdated}
          trend={todayTrend}
        />

        {/* Yesterday's Unique Visits */}
        <EnhancedMetricCard
          title="Yesterday's Unique Visits"
          value={formatIndianNumberShort(yesterdayUniqueVisits)}
          icon={Users}
          description="Unique visitors yesterday"
          color="purple"
          isUpdated={isVisitUpdated}
        />

        {/* Current Month's Unique Visits */}
        <EnhancedMetricCard
          title="This Month's Unique Visits"
          value={formatIndianNumberShort(currentMonthUniqueVisits)}
          icon={Users}
          description="Unique visitors this month"
          color="indigo"
          isUpdated={isVisitUpdated}
        />
      </div>
      </div>
    </motion.div>
  );
}
