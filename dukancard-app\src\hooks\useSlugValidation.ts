import { checkSlugAvailability } from '@/backend/supabase/services/common/onboardingService';
import { useCallback, useRef, useState } from 'react';

export interface SlugValidationState {
  isChecking: boolean;
  isAvailable: boolean | null;
  error: string | null;
}

export function useSlugValidation() {
  const [state, setState] = useState<SlugValidationState>({
    isChecking: false,
    isAvailable: null,
    error: null,
  });

  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const validateSlugFormat = (slug: string): { isValid: boolean; error?: string } => {
    if (!slug || slug.length < 3) {
      return { isValid: false, error: 'URL must be at least 3 characters' };
    }

    if (slug.length > 50) {
      return { isValid: false, error: 'URL must be less than 50 characters' };
    }

    // Use exact same regex as Next.js web version
    if (!/^[a-z0-9]+(?:-[a-z0-9]+)*$/.test(slug)) {
      return {
        isValid: false,
        error: 'Invalid format (lowercase, numbers, hyphens only)'
      };
    }

    // Check for reserved words (same as Next.js web version)
    const reservedWords = [
      'admin', 'api', 'www', 'app', 'mail', 'ftp', 'localhost', 'test',
      'staging', 'dev', 'development', 'prod', 'production', 'beta',
      'alpha', 'demo', 'support', 'help', 'about', 'contact', 'privacy',
      'terms', 'legal', 'blog', 'news', 'login', 'register', 'signup',
      'signin', 'logout', 'dashboard', 'profile', 'settings', 'account',
      'billing', 'payment', 'checkout', 'cart', 'shop', 'store',
      'dukancard', 'dukan', 'card'
    ];

    if (reservedWords.includes(slug.toLowerCase())) {
      return { isValid: false, error: 'This URL is reserved and cannot be used' };
    }

    return { isValid: true };
  };

  const performSlugAvailabilityCheck = useCallback(async (slug: string): Promise<boolean> => {
    try {
      const result = await checkSlugAvailability(slug);
      if (result.error) {
        throw new Error(result.error);
      }
      return result.available;
    } catch (error) {
      console.error('Error in performSlugAvailabilityCheck:', error);
      throw error;
    }
  }, []);

  const validateSlug = useCallback((slug: string, debounceMs: number = 500) => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Reset state immediately
    setState(prev => ({
      ...prev,
      isAvailable: null,
      error: null,
    }));

    // Validate format first
    const formatValidation = validateSlugFormat(slug);
    if (!formatValidation.isValid) {
      setState({
        isChecking: false,
        isAvailable: false,
        error: formatValidation.error || 'Invalid format',
      });
      return;
    }

    // Set checking state
    setState(prev => ({
      ...prev,
      isChecking: true,
      error: null,
    }));

    // Debounce the API call
    timeoutRef.current = setTimeout(async () => {
      try {
        const isAvailable = await performSlugAvailabilityCheck(slug);
        setState({
          isChecking: false,
          isAvailable,
          error: isAvailable ? null : 'This URL is already taken',
        });
      } catch (err) {
        console.error('Error validating slug:', err);
        setState({
          isChecking: false,
          isAvailable: null,
          error: 'Error checking availability. Please try again.',
        });
      }
    }, debounceMs);
  }, [performSlugAvailabilityCheck]);

  const reset = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setState({
      isChecking: false,
      isAvailable: null,
      error: null,
    });
  }, []);

  return {
    ...state,
    validateSlug,
    reset,
    validateSlugFormat,
  };
}
