import { StyleSheet } from 'react-native';

export const createCustomerPostModalStyles = () => {
  return StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    backButton: {
      padding: 8,
      borderRadius: 20,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: '600',
    },
    postButton: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 20,
    },
    postButtonText: {
      color: '#FFFFFF',
      fontSize: 14,
      fontWeight: '600',
    },
    content: {
      flex: 1,
    },
    scrollView: {
      flex: 1,
      paddingHorizontal: 16,
    },
    userInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 16,
    },
    avatar: {
      width: 48,
      height: 48,
      borderRadius: 24,
    },
    avatarPlaceholder: {
      width: 48,
      height: 48,
      borderRadius: 24,
      alignItems: 'center',
      justifyContent: 'center',
    },
    avatarText: {
      color: '#FFFFFF',
      fontSize: 18,
      fontWeight: '600',
    },
    userName: {
      marginLeft: 12,
      fontSize: 16,
      fontWeight: '600',
    },
    textInputContainer: {
      position: 'relative',
      marginBottom: 16,
    },
    textInput: {
      minHeight: 120,
      fontSize: 16,
      lineHeight: 24,
      paddingVertical: 12,
      paddingHorizontal: 16,
      paddingBottom: 32, // Extra padding for character counter
      borderWidth: 1,
      borderRadius: 12,
    },
    characterCounter: {
      position: 'absolute',
      bottom: 8,
      right: 12,
    },
    characterCountText: {
      fontSize: 12,
      fontWeight: '500',
    },
    imageContainer: {
      position: 'relative',
      marginBottom: 16,
      marginHorizontal: -16, // Extend to full width by negating ScrollView padding
    },
    imagePreview: {
      width: '100%',
      minHeight: 200, // Ensure minimum height to prevent square default
      borderRadius: 12,
      resizeMode: 'contain', // Don't crop, show full image in original dimensions
    },
    removeImageButton: {
      position: 'absolute',
      top: 8,
      right: 8,
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
      borderRadius: 16,
      padding: 6,
    },
    bottomActions: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-start',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderTopWidth: 1,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 8,
      paddingHorizontal: 12,
    },
    actionButtonText: {
      marginLeft: 8,
      fontSize: 14,
      fontWeight: '500',
    },
  });
};
