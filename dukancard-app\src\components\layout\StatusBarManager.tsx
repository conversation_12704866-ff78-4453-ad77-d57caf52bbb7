import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/src/hooks/useTheme';

interface StatusBarManagerProps {
  style?: 'auto' | 'inverted' | 'light' | 'dark';
  backgroundColor?: string;
  translucent?: boolean;
  hidden?: boolean;
}

/**
 * Centralized StatusBar management component to prevent conflicts
 * and ensure consistent status bar behavior across the app.
 */
export function StatusBarManager({
  style,
  backgroundColor,
  translucent = false,
  hidden = false,
}: StatusBarManagerProps) {
  const theme = useTheme();

  // Determine status bar style based on theme if not provided
  const finalStyle = style || (theme.isDark ? 'light' : 'dark');

  return (
    <StatusBar
      style={finalStyle}
      backgroundColor={backgroundColor}
      translucent={translucent}
      hidden={hidden}
    />
  );
}

/**
 * Simple hook to get theme-appropriate status bar style
 */
export function useStatusBarStyle() {
  const theme = useTheme();

  return {
    style: theme.isDark ? 'light' as const : 'dark' as const,
    backgroundColor: theme.colors.background,
  };
}
