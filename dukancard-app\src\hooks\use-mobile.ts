import { useState, useEffect } from 'react';
import { Dimensions } from 'react-native';

/**
 * Hook to detect if the device is mobile or tablet
 * Returns true for mobile devices (width < 768px)
 * Returns false for tablets and larger screens (width >= 768px)
 */
export function useIsMobile() {
  const [isMobile, setIsMobile] = useState(true); // Default to mobile for React Native

  useEffect(() => {
    const updateScreenData = () => {
      const { width } = Dimensions.get('window');
      setIsMobile(width < 768);
    };

    // Initial check
    updateScreenData();

    // Listen for orientation changes
    const subscription = Dimensions.addEventListener('change', updateScreenData);

    return () => subscription?.remove();
  }, []);

  return isMobile;
}

/**
 * Hook to get current screen dimensions
 */
export function useScreenDimensions() {
  const [screenData, setScreenData] = useState(Dimensions.get('window'));

  useEffect(() => {
    const onChange = (result: { window: any; screen: any }) => {
      setScreenData(result.window);
    };

    const subscription = Dimensions.addEventListener('change', onChange);

    return () => subscription?.remove();
  }, []);

  return screenData;
}

/**
 * Hook to detect if device is tablet
 * Returns true for tablets (width >= 768px and < 1024px)
 */
export function useIsTablet() {
  const [isTablet, setIsTablet] = useState(false);

  useEffect(() => {
    const updateScreenData = () => {
      const { width } = Dimensions.get('window');
      setIsTablet(width >= 768 && width < 1024);
    };

    // Initial check
    updateScreenData();

    // Listen for orientation changes
    const subscription = Dimensions.addEventListener('change', updateScreenData);

    return () => subscription?.remove();
  }, []);

  return isTablet;
}
