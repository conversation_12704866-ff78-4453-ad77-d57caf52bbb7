const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Disable unstable_enablePackageExports to fix Expo SDK 53 + Supabase issue
config.resolver.unstable_enablePackageExports = false;

// Add resolver configuration for Zod locale files
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// Add source extensions to help resolve Zod files
config.resolver.sourceExts = [...config.resolver.sourceExts, 'js', 'jsx', 'ts', 'tsx', 'json'];

// Security: JavaScript obfuscation for production builds
const isProduction = process.env.NODE_ENV === 'production' || process.env.EXPO_PUBLIC_ENABLE_OBFUSCATION === 'true';

if (isProduction) {
  try {
    const jsoMetroPlugin = require("obfuscator-io-metro-plugin")(
      {
        // Main options
        compact: true,
        controlFlowFlattening: true,
        controlFlowFlatteningThreshold: 0.75,
        deadCodeInjection: true,
        deadCodeInjectionThreshold: 0.4,
        debugProtection: false, // Consider turning this on for production
        debugProtectionInterval: 0,
        disableConsoleOutput: true,
        identifierNamesGenerator: 'hexadecimal',
        log: false,
        numbersToExpressions: true,
        renameGlobals: false, // Crucial for stability
        selfDefending: true,
        simplify: true,
        splitStrings: true,
        splitStringsChunkLength: 10,
        stringArray: true,
        stringArrayEncoding: ['base64'],
        stringArrayThreshold: 0.75,
        transformObjectKeys: true,
        unicodeEscapeSequence: false
      },
      {
        runInDev: false,
        logObfuscatedFiles: false,
        // Filter which files to obfuscate
        filter: (fileName) => {
          // Always obfuscate your own source code
          if (fileName.startsWith('src/') || fileName.startsWith('app/')) {
            return true;
          }
          // It's recommended to not obfuscate libraries
          if (fileName.includes('node_modules')) {
            return false;
          }
          return true;
        }
      }
    );

    // Apply obfuscation plugin
    Object.assign(config, jsoMetroPlugin);
    console.log('🛡️  JavaScript obfuscation enabled for production build');
  } catch (error) {
    console.warn('⚠️  JavaScript obfuscation plugin not available:', error.message);
  }
} else {
  console.log('🔧 Development mode: JavaScript obfuscation disabled');
}

module.exports = config;
