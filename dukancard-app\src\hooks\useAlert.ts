import { useState, useCallback } from 'react';
import { AlertType, AlertButton } from '@/src/components/ui/AlertDialog';

interface AlertOptions {
  type?: AlertType;
  title: string;
  message?: string;
  buttons?: AlertButton[];
  showCloseButton?: boolean;
  customIcon?: React.ReactNode;
}

interface AlertState extends AlertOptions {
  visible: boolean;
}

export function useAlert() {
  const [alertState, setAlertState] = useState<AlertState>({
    visible: false,
    title: '',
  });

  const showAlert = useCallback((options: AlertOptions) => {
    setAlertState({
      ...options,
      visible: true,
    });
  }, []);

  const hideAlert = useCallback(() => {
    setAlertState(prev => ({
      ...prev,
      visible: false,
    }));
  }, []);

  // Convenience methods for common alert types
  const showSuccess = useCallback((title: string, message?: string, onOk?: () => void) => {
    showAlert({
      type: 'success',
      title,
      message,
      buttons: [
        {
          text: 'OK',
          onPress: () => {
            hideAlert();
            onOk?.();
          },
        },
      ],
    });
  }, [showAlert, hideAlert]);

  const showError = useCallback((title: string, message?: string, onOk?: () => void) => {
    showAlert({
      type: 'error',
      title,
      message,
      buttons: [
        {
          text: 'OK',
          onPress: () => {
            hideAlert();
            onOk?.();
          },
        },
      ],
    });
  }, [showAlert, hideAlert]);

  const showWarning = useCallback((title: string, message?: string, onOk?: () => void) => {
    showAlert({
      type: 'warning',
      title,
      message,
      buttons: [
        {
          text: 'OK',
          onPress: () => {
            hideAlert();
            onOk?.();
          },
        },
      ],
    });
  }, [showAlert, hideAlert]);

  const showInfo = useCallback((title: string, message?: string, onOk?: () => void) => {
    showAlert({
      type: 'info',
      title,
      message,
      buttons: [
        {
          text: 'OK',
          onPress: () => {
            hideAlert();
            onOk?.();
          },
        },
      ],
    });
  }, [showAlert, hideAlert]);

  const showConfirm = useCallback((
    title: string,
    message?: string,
    onConfirm?: () => void,
    onCancel?: () => void,
    options?: {
      confirmText?: string;
      cancelText?: string;
      type?: AlertType;
      destructive?: boolean;
    }
  ) => {
    showAlert({
      type: options?.type || 'question',
      title,
      message,
      buttons: [
        {
          text: options?.cancelText || 'Cancel',
          style: 'cancel',
          onPress: () => {
            hideAlert();
            onCancel?.();
          },
        },
        {
          text: options?.confirmText || 'Confirm',
          style: options?.destructive ? 'destructive' : 'default',
          onPress: () => {
            hideAlert();
            onConfirm?.();
          },
        },
      ],
    });
  }, [showAlert, hideAlert]);

  const showLogout = useCallback((onConfirm?: () => void, onCancel?: () => void) => {
    showAlert({
      type: 'warning',
      title: 'Logout',
      message: 'Are you sure you want to logout? You\'ll need to sign in again to access your account.',
      buttons: [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => {
            hideAlert();
            onCancel?.();
          },
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: () => {
            hideAlert();
            onConfirm?.();
          },
        },
      ],
    });
  }, [showAlert, hideAlert]);

  const showDelete = useCallback((
    itemName: string,
    onConfirm?: () => void,
    onCancel?: () => void,
    message?: string
  ) => {
    showAlert({
      type: 'error',
      title: `Delete ${itemName}`,
      message: message || `Are you sure you want to delete this ${itemName.toLowerCase()}? This action cannot be undone.`,
      buttons: [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => {
            hideAlert();
            onCancel?.();
          },
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            hideAlert();
            onConfirm?.();
          },
        },
      ],
    });
  }, [showAlert, hideAlert]);

  return {
    alertState,
    showAlert,
    hideAlert,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showConfirm,
    showLogout,
    showDelete,
  };
}
