import { Stack } from 'expo-router';
import { NotificationsModal } from '@/src/components/business/NotificationsModalNew';
import { AlertProvider } from '@/src/components/providers/AlertProvider';

export default function DashboardLayout() {
  return (
    <AlertProvider>
      <Stack>
        <Stack.Screen
          name="business"
          options={{
            headerShown: false,
            title: 'Business Dashboard'
          }}
        />
        <Stack.Screen
          name="customer"
          options={{
            headerShown: false,
            title: 'Customer Dashboard'
          }}
        />
      </Stack>

      {/* Global Notifications Modal */}
      <NotificationsModal />
    </AlertProvider>
  );
}
