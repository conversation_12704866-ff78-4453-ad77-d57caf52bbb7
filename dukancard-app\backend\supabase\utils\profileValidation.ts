/**
 * Profile validation utilities for React Native
 * Replaces Zod validation with simple validation functions
 */

export interface ProfileData {
  name?: string | null;
}

export interface PhoneData {
  phone?: string | null;
}

export interface EmailData {
  email?: string | null;
}

export interface MobileData {
  mobile?: string | null;
}

export interface AddressData {
  address?: string | null;
  pincode?: string | null;
  city?: string | null;
  state?: string | null;
  locality?: string | null;
}

/**
 * Validates Indian mobile number format
 */
export function isValidIndianMobile(mobile: string): boolean {
  // Remove any spaces or special characters
  const cleanMobile = mobile.replace(/\D/g, '');
  
  // Check if it's exactly 10 digits and starts with 6-9
  return /^[6-9]\d{9}$/.test(cleanMobile);
}

/**
 * Validates email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validates pincode format (6 digits)
 */
export function isValidPincode(pincode: string): boolean {
  return /^\d{6}$/.test(pincode);
}

/**
 * Validates profile name
 */
export function validateProfileName(name: string): {
  isValid: boolean;
  error?: string;
} {
  if (!name || name.trim() === '') {
    return { isValid: false, error: 'Name cannot be empty' };
  }
  
  if (name.trim().length > 100) {
    return { isValid: false, error: 'Name is too long' };
  }
  
  return { isValid: true };
}

/**
 * Validates phone number
 */
export function validatePhone(phone: string): {
  isValid: boolean;
  error?: string;
} {
  if (!phone || phone.trim() === '') {
    return { isValid: false, error: 'Phone number is required' };
  }
  
  if (!isValidIndianMobile(phone)) {
    return { isValid: false, error: 'Please enter a valid Indian mobile number' };
  }
  
  return { isValid: true };
}

/**
 * Validates email address
 */
export function validateEmail(email: string): {
  isValid: boolean;
  error?: string;
} {
  if (!email || email.trim() === '') {
    return { isValid: false, error: 'Email is required' };
  }
  
  if (!isValidEmail(email)) {
    return { isValid: false, error: 'Please enter a valid email address' };
  }
  
  return { isValid: true };
}

/**
 * Validates mobile number
 */
export function validateMobile(mobile: string): {
  isValid: boolean;
  error?: string;
} {
  if (!mobile || mobile.trim() === '') {
    return { isValid: false, error: 'Mobile number is required' };
  }
  
  if (!isValidIndianMobile(mobile)) {
    return { isValid: false, error: 'Please enter a valid Indian mobile number' };
  }
  
  return { isValid: true };
}

/**
 * Validates address data
 */
export function validateAddress(addressData: AddressData): {
  isValid: boolean;
  errors: Record<string, string>;
} {
  const errors: Record<string, string> = {};
  
  // Address is optional, so we don't validate it
  
  if (!addressData.pincode || addressData.pincode.trim() === '') {
    errors.pincode = 'Pincode is required';
  } else if (!isValidPincode(addressData.pincode)) {
    errors.pincode = 'Must be a valid 6-digit pincode';
  }
  
  if (!addressData.city || addressData.city.trim() === '') {
    errors.city = 'City is required';
  } else if (addressData.city.trim().length > 50) {
    errors.city = 'City cannot exceed 50 characters';
  }
  
  if (!addressData.state || addressData.state.trim() === '') {
    errors.state = 'State is required';
  } else if (addressData.state.trim().length > 50) {
    errors.state = 'State cannot exceed 50 characters';
  }
  
  if (!addressData.locality || addressData.locality.trim() === '') {
    errors.locality = 'Locality is required';
  }
  
  if (addressData.address && addressData.address.trim().length > 100) {
    errors.address = 'Address cannot exceed 100 characters';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

/**
 * Clean and format profile data
 */
export function cleanProfileData(data: ProfileData): ProfileData {
  return {
    name: data.name?.trim() || null,
  };
}

/**
 * Clean and format phone data
 */
export function cleanPhoneData(data: PhoneData): PhoneData {
  return {
    phone: data.phone?.replace(/\D/g, '') || null,
  };
}

/**
 * Clean and format email data
 */
export function cleanEmailData(data: EmailData): EmailData {
  return {
    email: data.email?.trim().toLowerCase() || null,
  };
}

/**
 * Clean and format mobile data
 */
export function cleanMobileData(data: MobileData): MobileData {
  return {
    mobile: data.mobile?.replace(/\D/g, '') || null,
  };
}

/**
 * Clean and format address data
 */
export function cleanAddressData(data: AddressData): AddressData {
  return {
    address: data.address?.trim() || null,
    pincode: data.pincode?.trim() || null,
    city: data.city?.trim() || null,
    state: data.state?.trim() || null,
    locality: data.locality?.trim() || null,
  };
}
