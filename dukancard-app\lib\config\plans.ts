/**
 * Central configuration for all plan-related constants
 * This file serves as the single source of truth for plan information
 */

// Plan types
export type PlanType = "free" | "basic" | "growth" | "pro" | "enterprise";
export type PlanCycle = "monthly" | "yearly";

// Plan features
export interface PlanFeature {
  name: string;
  included: boolean;
  limit?: number | "unlimited";
  description?: string;
}

// Plan interface
export interface Plan {
  id: PlanType;
  name: string;
  description: string;
  features: PlanFeature[];
  pricing: {
    monthly: number;
    yearly: number;
  };
  recommended?: boolean;
  available?: boolean;
}

// Plan definitions
export const PLANS: Plan[] = [
  {
    id: "free",
    name: "Free",
    description: "Basic features for individuals and startups",
    pricing: {
      monthly: 0,
      yearly: 0,
    },
    available: true,
    features: [
      {
        name: "Digital Business Card",
        included: true,
        description: "Create and share your digital business card",
      },
      {
        name: "Basic Contact Management",
        included: true,
        limit: 100,
        description: "Store up to 100 contacts",
      },
      {
        name: "QR Code Generation",
        included: true,
        description: "Generate QR codes for easy sharing",
      },
      {
        name: "Basic Analytics",
        included: true,
        description: "View basic card performance metrics",
      },
      {
        name: "Photo Gallery",
        included: true,
        limit: 1,
        description: "Upload and display 1 image in your gallery",
      },
      {
        name: "Dukancard Branding",
        included: true,
        description: "Dukancard branding on your business card",
      },
    ],
  },
  {
    id: "basic",
    name: "Basic",
    description: "Essential features for small businesses",
    recommended: false,
    pricing: {
      monthly: 99,
      yearly: 999,
    },
    available: true,
    features: [
      {
        name: "Everything in Free",
        included: true,
        description: "All features from the Free plan",
      },
      {
        name: "Custom Branding",
        included: true,
        description: "Add your own logo and branding",
      },
      {
        name: "Advanced Contact Management",
        included: true,
        limit: 500,
        description: "Store up to 500 contacts",
      },
      {
        name: "Email Integration",
        included: true,
        description: "Sync with your email contacts",
      },
      {
        name: "Photo Gallery",
        included: true,
        limit: 3,
        description: "Upload and display up to 3 images in your gallery",
      },
      {
        name: "Priority Support",
        included: true,
        description: "Email support with faster response times",
      },
    ],
  },
  {
    id: "growth",
    name: "Growth",
    description: "Advanced features for growing businesses",
    recommended: true,
    pricing: {
      monthly: 499,
      yearly: 4990,
    },
    available: true,
    features: [
      {
        name: "Everything in Basic",
        included: true,
        description: "All features from the Basic plan",
      },
      {
        name: "Unlimited Contacts",
        included: true,
        limit: "unlimited",
        description: "Store unlimited contacts",
      },
      {
        name: "Advanced Analytics",
        included: true,
        description: "Detailed insights and performance metrics",
      },
      {
        name: "Team Collaboration",
        included: true,
        limit: 5,
        description: "Add up to 5 team members",
      },
      {
        name: "Photo Gallery",
        included: true,
        limit: 10,
        description: "Upload and display up to 10 images",
      },
      {
        name: "API Access",
        included: true,
        description: "Integrate with third-party applications",
      },
    ],
  },
  {
    id: "pro",
    name: "Pro",
    description: "Premium features for established businesses",
    pricing: {
      monthly: 1999,
      yearly: 19990,
    },
    available: true,
    features: [
      {
        name: "Everything in Growth",
        included: true,
        description: "All features from the Growth plan",
      },
      {
        name: "White Label Solution",
        included: true,
        description: "Complete white-label branding",
      },
      {
        name: "Advanced Team Management",
        included: true,
        limit: "unlimited",
        description: "Unlimited team members",
      },
      {
        name: "Custom Integrations",
        included: true,
        description: "Custom API integrations and webhooks",
      },
      {
        name: "Photo Gallery",
        included: true,
        limit: 50,
        description: "Upload and display up to 50 images",
      },
      {
        name: "Priority Support",
        included: true,
        description: "Priority email and chat support",
      },
      {
        name: "No Dukancard Branding",
        included: true,
        description: "No Dukancard branding on your business card",
      },
    ],
  },
  {
    id: "enterprise",
    name: "Enterprise",
    description: "Custom solutions for large businesses",
    pricing: {
      monthly: 0, // Will be handled as "Contact Sales"
      yearly: 0, // Will be handled as "Contact Sales"
    },
    available: false, // Coming soon
    features: [
      {
        name: "Everything in Pro",
        included: true,
        description: "All features from the Pro plan",
      },
      {
        name: "Custom Development",
        included: true,
        description: "Custom features and integrations",
      },
      {
        name: "Dedicated Support",
        included: true,
        description: "Dedicated account manager and support",
      },
      {
        name: "On-Premise Deployment",
        included: true,
        description: "Deploy on your own infrastructure",
      },
      {
        name: "Advanced Security",
        included: true,
        description: "Enterprise-grade security features",
      },
      {
        name: "SLA Guarantee",
        included: true,
        description: "99.9% uptime guarantee",
      },
    ],
  },
];

// Helper function to get product/service limits based on plan ID
export const getProductLimit = (planId: PlanType | null | undefined): number => {
  if (!planId) return 0;
  
  const plan = PLANS.find(p => p.id === planId);
  if (!plan) return 0;
  
  // Return contact limit for backward compatibility
  const contactFeature = plan.features.find(f => f.name.includes("Contact"));
  if (contactFeature?.limit === "unlimited") return 999999;
  if (typeof contactFeature?.limit === "number") return contactFeature.limit;
  
  return 100; // Default for free plan
};

// Helper function to get plan details
export const getPlanDetails = (planId: PlanType | null | undefined): Plan | null => {
  if (!planId) return null;
  return PLANS.find(p => p.id === planId) || null;
};
