/**
 * Customer Post Image Upload Service for React Native
 * Handles image compression and upload to Supabase storage
 */

import { supabase } from '@/lib/supabase';
import { compressFileUltraAggressive } from '@/src/utils/client-image-compression';
import { getCustomerPostImagePath } from '@/backend/supabase/utils/storage-paths';

export interface CustomerPostImageUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

/**
 * Upload and process image for customer post
 * Matches the Next.js implementation structure
 */
export async function uploadCustomerPostImage(
  imageUri: string,
  postId: string,
  postCreatedAt?: string
): Promise<CustomerPostImageUploadResult> {
  try {
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: "User not authenticated." };
    }

    const userId = user.id;

    if (!imageUri) {
      return { success: false, error: "No image provided." };
    }

    // Fetch the image and convert to blob
    const response = await fetch(imageUri);
    const blob = await response.blob();

    // Convert blob to file for compression
    const fileName = `customer-post-${Date.now()}.jpg`;
    const file = new File([blob], fileName, {
      type: blob.type || 'image/jpeg',
      lastModified: Date.now()
    });

    // Validate file size (15MB max)
    const maxFileSize = 15 * 1024 * 1024; // 15MB
    if (file.size > maxFileSize) {
      const fileSizeMB = (file.size / (1024 * 1024)).toFixed(1);
      return {
        success: false,
        error: `File size (${fileSizeMB}MB) exceeds the 15MB limit.`
      };
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
      return {
        success: false,
        error: "Invalid file type. Please select JPG, PNG, WebP, or GIF images."
      };
    }

    // Compress image on client-side first (matching Next.js implementation)
    const compressionResult = await compressFileUltraAggressive(file, {
      maxDimension: 1200,
      targetSizeKB: 100
    });

    // Convert compressed blob back to file
    const compressedFile = new File([compressionResult.blob!], fileName, {
      type: compressionResult.blob!.type
    });

    // Create scalable path structure (matching Next.js implementation)
    const timestamp = Date.now() + Math.floor(Math.random() * 1000);
    const bucketName = "customers";
    const imagePath = getCustomerPostImagePath(userId, postId, 0, timestamp, postCreatedAt);

    // Convert file to array buffer for upload
    const fileBuffer = await compressedFile.arrayBuffer();

    // Upload to Supabase Storage
    const { error: uploadError } = await supabase.storage
      .from(bucketName)
      .upload(imagePath, fileBuffer, {
        contentType: compressedFile.type,
        upsert: true
      });

    if (uploadError) {
      console.error("Customer Post Image Upload Error:", uploadError);
      return {
        success: false,
        error: `Failed to upload image: ${uploadError.message}`,
      };
    }

    // Get the public URL
    const { data: urlData } = supabase.storage
      .from(bucketName)
      .getPublicUrl(imagePath);

    if (!urlData?.publicUrl) {
      return {
        success: false,
        error: "Could not retrieve public URL after upload.",
      };
    }

    return {
      success: true,
      url: urlData.publicUrl,
    };

  } catch (error) {
    console.error("Error processing customer post image:", error);
    return {
      success: false,
      error: "Failed to process image. Please try a different image."
    };
  }
}

/**
 * Check if customer has complete address before creating posts
 * Matches the Next.js implementation
 */
export async function checkCustomerAddress(): Promise<boolean> {
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return false;
    }

    const { data: profile, error: profileError } = await supabase
      .from('customer_profiles')
      .select('city, state, pincode, locality')
      .eq('id', user.id)
      .single();

    if (profileError || !profile) {
      return false;
    }

    // Check if all required address fields are present
    const hasCompleteAddress = profile.city && 
                              profile.state && 
                              profile.pincode && 
                              profile.locality;

    return !!hasCompleteAddress;
  } catch (error) {
    console.error('Error checking customer address:', error);
    return false;
  }
}
