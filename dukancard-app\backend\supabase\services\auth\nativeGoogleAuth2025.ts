import { getGoogleConfig } from '@/lib/config/google';
import { supabase } from '@/lib/supabase';
import { Platform } from 'react-native';

// Conditional import for native Google Sign-In
let GoogleSignin: any = null;
let statusCodes: any = null;
let isConfigured = false; // Track if Google Auth is already configured

try {
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  const googleSignInModule = require('@react-native-google-signin/google-signin');
  GoogleSignin = googleSignInModule.GoogleSignin;
  statusCodes = googleSignInModule.statusCodes;
} catch {
  // Native Google Sign-In not available, will fall back to web auth
}

export interface GoogleAuthResponse {
  success: boolean;
  message: string;
  error?: any;
  user?: any;
}

/**
 * Configure Google Sign-In with correct client IDs for Supabase integration
 * This follows 2025 best practices for React Native + Supabase
 */
export const configureGoogleAuth = async (): Promise<void> => {
  try {
    if (!GoogleSignin) {
      return;
    }

    // Skip if already configured
    if (isConfigured) {
      return;
    }

    // Get client IDs from secure configuration
    const config = await getGoogleConfig();

    // CRITICAL: For Supabase integration, webClientId MUST be the Web Application client ID
    // This is the same client ID configured in your Supabase dashboard
    const webClientId = config.webClientId;

    // iOS client ID is only used on iOS for native authentication
    const iosClientId = Platform.OS === 'ios' ? config.iosClientId : undefined;

    if (!webClientId) {
      throw new Error('Missing Web Client ID. Check your configuration in lib/config/google.ts');
    }

    // Configure Google Sign-In
    GoogleSignin.configure({
      webClientId, // MUST be Web Application client ID for Supabase
      iosClientId, // Only used on iOS
      offlineAccess: config.offlineAccess,
      hostedDomain: config.hostedDomain,
      forceCodeForRefreshToken: config.forceCodeForRefreshToken,
    });

    isConfigured = true; // Mark as configured
  } catch (error) {
    console.error('❌ Failed to configure Google Sign-In:', error);
    throw error;
  }
};

/**
 * Check if Google Play Services are available (Android only)
 */
export const checkGooglePlayServices = async (): Promise<boolean> => {
  try {
    await GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: true });
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Sign in with Google using native modal (2025 approach)
 * Uses signInWithIdToken for Supabase integration
 * Falls back to web auth if native isn't available
 */
export const signInWithGoogleNative = async (): Promise<GoogleAuthResponse> => {
  try {
    // Check if native Google Sign-In is available
    if (!GoogleSignin) {
      // Fallback to web-based Google auth
      const { signInWithGoogle } = await import('@/backend/supabase/services/auth/googleAuthService');
      return await signInWithGoogle();
    }

    // Configure Google Sign-In if not already done (should already be configured at app startup)
    if (!isConfigured) {
      await configureGoogleAuth();
    }

    // Check Google Play Services on Android
    if (Platform.OS === 'android') {
      const hasPlayServices = await checkGooglePlayServices();
      if (!hasPlayServices) {
        return {
          success: false,
          message: 'Google services are not available on this device. Please try email login.',
        };
      }
    }

    // IMPORTANT: Sign out first to ensure fresh authentication
    // This prevents using cached credentials and forces the Google modal to appear
    try {
      await GoogleSignin.signOut();
    } catch (signOutError) {
      // Ignore sign out errors - user might not be signed in
    }

    // Perform the native Google Sign-In - this will now show the modal
    const userInfo = await GoogleSignin.signIn();

    if (!userInfo?.data?.idToken) {
      console.error('❌ No ID token received from Google Sign-In');
      return {
        success: false,
        message: 'Unable to complete Google sign-in. Please try again.',
      };
    }

    // Sign in to Supabase using the Google ID token
    const { data, error } = await supabase.auth.signInWithIdToken({
      provider: 'google',
      token: userInfo.data.idToken,
      // nonce: undefined, // Skip nonce for simplicity (configure in Supabase dashboard)
    });

    if (error) {
      console.error('❌ Supabase sign-in error:', error);
      return {
        success: false,
        message: 'Unable to sign in with Google. Please try again.',
        error,
      };
    }
    return {
      success: true,
      message: 'Successfully signed in with Google',
      user: data.user,
    };

  } catch (error: any) {
    console.error('❌ Google Sign-In error:', error);
    console.error('Error details:', {
      code: error.code,
      message: error.message,
      name: error.name,
    });

    // Handle specific Google Sign-In error codes
    if (error.code === statusCodes.SIGN_IN_CANCELLED) {
      return {
        success: false,
        message: 'cancelled', // Special message to indicate cancellation
      };
    } else if (error.code === statusCodes.IN_PROGRESS) {
      return {
        success: false,
        message: 'Google sign-in is already in progress. Please wait.',
      };
    } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
      return {
        success: false,
        message: 'Google services are not available. Please try email login.',
      };
    } else {
      // Log additional details for DEVELOPER_ERROR
      if (error.message?.includes('DEVELOPER_ERROR')) {
        console.error('🚨 DEVELOPER_ERROR detected. Common causes:');
        console.error('1. Incorrect webClientId in configuration');
        console.error('2. Missing SHA-1 fingerprint in Google Cloud Console');
        console.error('3. Wrong client ID type (should be Web Application)');
        console.error('4. Client ID not matching Supabase configuration');
      }

      return {
        success: false,
        message: 'Unable to sign in with Google. Please try again.',
        error,
      };
    }
  }
};

/**
 * Sign out from Google
 */
export const signOutFromGoogle = async (): Promise<void> => {
  try {
    await GoogleSignin.signOut();
  } catch (error) {
    console.error('Error signing out from Google:', error);
  }
};

/**
 * Check if user is signed in to Google
 */
export const isGoogleSignedIn = async (): Promise<boolean> => {
  try {
    const isSignedIn = GoogleSignin.getCurrentUser();
    return isSignedIn !== null;
  } catch (error) {
    console.error('Error checking Google sign-in status:', error);
    return false;
  }
};

/**
 * Get current Google user info
 */
export const getCurrentGoogleUser = async () => {
  try {
    const userInfo = await GoogleSignin.signInSilently();
    return userInfo;
  } catch (error) {
    return null;
  }
};
