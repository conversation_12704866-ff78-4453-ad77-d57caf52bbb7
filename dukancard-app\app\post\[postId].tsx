import React from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { useTheme } from '@/src/hooks/useTheme';
import { responsiveFontSize } from '@/lib/theme/colors';
import SinglePostScreen from '@/src/components/post/SinglePostScreen';
import UnifiedBottomNavigation from '@/src/components/shared/navigation/UnifiedBottomNavigation';

export default function SinglePostPage() {
  const { postId } = useLocalSearchParams<{ postId: string }>();
  const router = useRouter();
  const theme = useTheme();

  // Theme colors
  const backgroundColor = theme.colors.background;
  const textColor = theme.colors.textPrimary;
  const statusBarStyle = theme.isDark ? 'light' : 'dark';

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    backButton: {
      padding: theme.spacing.xs,
      marginLeft: -theme.spacing.xs,
    },
  });

  // Validate postId parameter
  if (!postId || typeof postId !== 'string') {
    // Handle invalid postId - could redirect to error screen
    router.replace('/');
    return null;
  }

  const handleBack = () => {
    if (router.canGoBack()) {
      router.back();
    } else {
      // Fallback navigation
      router.replace('/');
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Post',
          headerShown: true,
          headerStyle: {
            backgroundColor,
          },
          headerShadowVisible: false,
          headerTintColor: textColor,
          headerTitleStyle: {
            color: textColor,
            fontWeight: '600',
            fontSize: theme.typography.fontSize.lg,
          },
          headerLeft: () => (
            <TouchableOpacity
              onPress={handleBack}
              style={styles.backButton}
              activeOpacity={0.7}
            >
              <ArrowLeft size={responsiveFontSize(24)} color={textColor} />
            </TouchableOpacity>
          ),
        }}
      />
      <StatusBar style={statusBarStyle} backgroundColor={backgroundColor} />
      <View style={[styles.container, { backgroundColor }]}>
        <SafeAreaView style={{ flex: 1 }}>
          <SinglePostScreen postId={postId} />
        </SafeAreaView>
        <UnifiedBottomNavigation showQRScanner={true} />
      </View>
    </>
  );
}


