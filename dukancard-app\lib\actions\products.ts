/**
 * Product Actions for React Native
 * Handles product search and selection for business posts
 */

import { supabase } from "@/lib/supabase";
import { ProductService } from "@/backend/supabase/services/products/productService";
import { ProductsServices } from "@/src/types/database";

export interface ProductData {
  id: string;
  name: string;
  slug?: string;
  image_url?: string | null;
  base_price?: number | null;
  discounted_price?: number | null;
  description?: string | null;
  business_id: string;
}

export interface ActionResponse {
  success: boolean;
  message: string;
  error?: string;
  data?: any;
}

/**
 * Search business products by name
 */
export async function searchBusinessProducts(
  query: string
): Promise<ActionResponse> {
  try {
    // Get the current user
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return {
        success: false,
        message: "Authentication required",
        error: "You must be logged in to search products",
      };
    }

    // Search products belonging to the current business user
    const { data, error } = await ProductService.searchBusinessProducts(
      user.id,
      query
    );

    if (error) {
      console.error("Error searching products:", error);
      return {
        success: false,
        message: "Failed to search products",
        error: error.message,
      };
    }

    return {
      success: true,
      message: "Products found successfully",
      data: data || [],
    };
  } catch (error) {
    console.error("Error searching products:", error);
    return {
      success: false,
      message: "Failed to search products",
      error: "An unexpected error occurred",
    };
  }
}

/**
 * Get selected products by IDs
 */
export async function getSelectedProducts(
  productIds: string[]
): Promise<ActionResponse> {
  try {
    if (productIds.length === 0) {
      return {
        success: true,
        message: "No products to fetch",
        data: [],
      };
    }

    // Get the current user
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return {
        success: false,
        message: "Authentication required",
        error: "You must be logged in to fetch products",
      };
    }

    // Fetch products by IDs, ensuring they belong to the current business user
    const { data, error } = await ProductService.getSelectedProducts(
      user.id,
      productIds
    );

    if (error) {
      console.error("Error fetching selected products:", error);
      return {
        success: false,
        message: "Failed to fetch products",
        error: error.message,
      };
    }

    return {
      success: true,
      message: "Products fetched successfully",
      data: data || [],
    };
  } catch (error) {
    console.error("Error fetching selected products:", error);
    return {
      success: false,
      message: "Failed to fetch products",
      error: "An unexpected error occurred",
    };
  }
}

/**
 * Get business products with pagination
 */
export async function getBusinessProducts(
  page: number = 1,
  limit: number = 20
): Promise<ActionResponse> {
  try {
    // Get the current user
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return {
        success: false,
        message: "Authentication required",
        error: "You must be logged in to fetch products",
      };
    }

    const offset = (page - 1) * limit;

    // Fetch products with pagination
    const { data, error, count } = await ProductService.getBusinessProducts(
      user.id,
      offset,
      limit
    );

    if (error) {
      console.error("Error fetching business products:", error);
      return {
        success: false,
        message: "Failed to fetch products",
        error: error.message,
      };
    }

    const hasMore = count ? offset + limit < count : false;

    return {
      success: true,
      message: "Products fetched successfully",
      data: {
        items: data || [],
        hasMore,
        total: count || 0,
      },
    };
  } catch (error) {
    console.error("Error fetching business products:", error);
    return {
      success: false,
      message: "Failed to fetch products",
      error: "An unexpected error occurred",
    };
  }
}

/**
 * Get a single product by ID
 */
export async function getProduct(productId: string): Promise<ActionResponse> {
  try {
    // Get the current user
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return {
        success: false,
        message: "Authentication required",
        error: "You must be logged in to fetch product",
      };
    }

    // Fetch the product
    const { data, error } = await ProductService.getProduct(user.id, productId);

    if (error) {
      console.error("Error fetching product:", error);
      return {
        success: false,
        message: "Failed to fetch product",
        error: error.message,
      };
    }

    return {
      success: true,
      message: "Product fetched successfully",
      data,
    };
  } catch (error) {
    console.error("Error fetching product:", error);
    return {
      success: false,
      message: "Failed to fetch product",
      error: "An unexpected error occurred",
    };
  }
}
