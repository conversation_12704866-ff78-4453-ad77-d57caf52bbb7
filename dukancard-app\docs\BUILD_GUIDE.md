# 🚀 Dukancard App - Build Guide (2025)

## 📱 Build Commands for Expo React Native

### Development Builds
```bash
# Start development server
npm start

# Run on Android (development)
npx expo run:android

# Run on iOS (development) 
npx expo run:ios
```

### Production Builds

#### Method 1: Using React Native CLI (Recommended for AAB)
```bash
# Build production AAB for Google Play Store
npx react-native build-android --mode=release

# Test production build
npm run android -- --mode="release"
```

#### Method 2: Using Gradle Directly
```bash
# Navigate to android directory
cd android

# Build release APK (for testing)
./gradlew assembleRelease

# Build release AAB (for Play Store)
./gradlew bundleRelease

# Return to project root
cd ..
```

#### Method 3: Using Expo CLI (Alternative)
```bash
# Build production APK for Android
npx expo run:android --variant release

# Build production AAB
npx expo run:android --variant release --no-install
```

## 📁 Build Output Locations

### APK Files (for testing):
```
android/app/build/outputs/apk/release/app-release.apk
```

### AAB Files (for Play Store):
```
android/app/build/outputs/bundle/release/app-release.aab
```

## 🔧 Environment Configuration

### Development Build:
- Uses `.env` file
- Debug keystore (auto-generated)
- Development Google OAuth client ID
- Crash reporting disabled

### Production Build:
- Uses `.env.production` file
- Production keystore (`dukancard-upload-key.keystore`)
- Production Google OAuth client ID
- Crash reporting enabled

## 🛠️ Troubleshooting Build Issues

### Common Issues:

#### 1. NDK Error
```
Error: NDK at C:\Users\<USER>\ndk\27.1.12297006 did not have a source.properties file
```
**Solution**: Install Android NDK through Android Studio SDK Manager

#### 2. Keystore Not Found
```
Error: Could not find keystore file
```
**Solution**: Ensure `dukancard-upload-key.keystore` exists in `android/app/`

#### 3. Environment Variables Not Loading
```
Error: Supabase configuration missing
```
**Solution**: Ensure `.env` or `.env.production` files exist and contain required variables

#### 4. Google Sign-In Not Working
**Solution**: Verify correct client IDs for the build type (dev/prod)

## 📋 Pre-Build Checklist

### Before Building for Production:
- [ ] Production keystore generated and configured
- [ ] Environment variables set in `.env.production`
- [ ] Google OAuth production client IDs configured
- [ ] App version and build number updated
- [ ] Privacy policy URL added
- [ ] Notification mode set to "production"

### Build Environment Setup:
- [ ] Android Studio installed
- [ ] Android SDK and NDK installed
- [ ] Java JDK 17+ installed
- [ ] Gradle configured properly

## 🚀 Deployment Steps

### For Google Play Store:

1. **Build AAB (Recommended)**:
   ```bash
   # Using React Native CLI
   npx react-native build-android --mode=release

   # Or using Gradle directly
   cd android && ./gradlew bundleRelease
   ```

2. **Verify Signing**:
   ```bash
   # Check if AAB is signed (if you built APK for testing)
   jarsigner -verify -verbose -certs android/app/build/outputs/apk/release/app-release.apk
   ```

3. **Upload to Play Console**:
   - Go to [Google Play Console](https://play.google.com/console)
   - Upload `android/app/build/outputs/bundle/release/app-release.aab` file
   - Fill in store listing details
   - Submit for review

### For Testing:

1. **Build and Test APK**:
   ```bash
   # Build and install directly
   npm run android -- --mode="release"

   # Or build APK manually then install
   cd android && ./gradlew assembleRelease
   adb install android/app/build/outputs/apk/release/app-release.apk
   ```

2. **Test Features**:
   - Google Sign-In with production credentials
   - Push notifications
   - All app functionality
   - Performance and stability

## 🔍 Build Verification

### Check Build Configuration:
```bash
# Verify keystore is being used
grep -r "dukancard-upload-key" android/

# Check environment variables
cat .env.production

# Verify app config
node -e "console.log(require('./app.config.js').default.expo.extra)"
```

### Verify APK Details:
```bash
# Get APK info
aapt dump badging android/app/build/outputs/apk/release/app-release.apk

# Check signing certificate
keytool -printcert -jarfile android/app/build/outputs/apk/release/app-release.apk
```

## 📊 Build Performance

### Typical Build Times:
- **First build**: 10-15 minutes (downloads dependencies)
- **Subsequent builds**: 2-5 minutes
- **Clean builds**: 5-10 minutes

### Optimization Tips:
- Use `--no-install` flag when building for upload
- Clean build directory if facing issues: `cd android && ./gradlew clean`
- Use Gradle daemon for faster builds (enabled by default)

## 🆘 Getting Help

### If Build Fails:
1. Check the error message carefully
2. Clean and rebuild: `cd android && ./gradlew clean && ./gradlew assembleRelease`
3. Verify all prerequisites are installed
4. Check environment variables and configuration files
5. Ensure keystore file exists and is accessible

### Useful Commands:
```bash
# Check Gradle version
cd android && ./gradlew --version

# List all available tasks
cd android && ./gradlew tasks

# Build with debug info
cd android && ./gradlew assembleRelease --info

# Build with stack trace on error
cd android && ./gradlew assembleRelease --stacktrace
```

## 📝 Notes

- Always test production builds on real devices before Play Store submission
- Keep backup copies of your production keystore file
- Document any custom build configurations for team members
- Monitor build times and optimize as needed
