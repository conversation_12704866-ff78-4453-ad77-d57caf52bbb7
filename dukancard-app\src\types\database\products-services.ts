export interface ProductsServicesRow {
  id: string;
  business_id: string;
  name: string;
  description: string | null;
  base_price: number | null;
  is_available: boolean;
  image_url: string | null;
  created_at: string;
  updated_at: string;
  product_type: string | null;
  discounted_price: number | null;
  slug: string | null;
  images: string[] | null;
  featured_image_index: number | null;
}

export interface ProductsServicesInsert {
  id?: string;
  business_id: string;
  name: string;
  description?: string | null;
  base_price?: number | null;
  is_available?: boolean;
  image_url?: string | null;
  created_at?: string;
  updated_at?: string;
  product_type?: string | null;
  discounted_price?: number | null;
  slug?: string | null;
  images?: string[] | null;
  featured_image_index?: number | null;
}

export interface ProductsServicesUpdate {
  id?: string;
  business_id?: string;
  name?: string;
  description?: string | null;
  base_price?: number | null;
  is_available?: boolean;
  image_url?: string | null;
  created_at?: string;
  updated_at?: string;
  product_type?: string | null;
  discounted_price?: number | null;
  slug?: string | null;
  images?: string[] | null;
  featured_image_index?: number | null;
}

export type ProductsServices = ProductsServicesRow;
