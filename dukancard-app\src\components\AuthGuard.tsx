import { useAuth } from '@/src/contexts/AuthContext';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { getRedirectPath } from '@/src/utils/navigation';
import { router, useSegments } from 'expo-router';
import React, { useEffect, useMemo } from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';

export function AuthGuard({ children }: { children: React.ReactNode }) {
  const { user, loading, profileStatus } = useAuth();
  const segments = useSegments();
  const colorScheme = useColorScheme();

  // Memoize the navigation state to prevent unnecessary recalculations
  const navigationState = useMemo(() => ({
    isAuthenticated: !!user,
    roleStatus: profileStatus.roleStatus,
    currentSegments: segments as string[]
  }), [user, profileStatus.roleStatus, segments]);

  useEffect(() => {
    // Don't do anything while loading
    if (loading || profileStatus.loading) {
      return;
    }

    // Handle profile status errors
    if (profileStatus.error && user) {
      console.error('AuthGuard: Profile status error, redirecting to login:', profileStatus.error);
      router.replace('/(auth)/login');
      return;
    }

    // Use navigation utility to determine redirect path
    const redirectPath = getRedirectPath(navigationState);

    // Perform redirect if needed
    if (redirectPath) {
      try {
        // Use setTimeout to prevent view hierarchy conflicts during rapid navigation
        setTimeout(() => {
          router.replace(redirectPath);
        }, 50);
      } catch (error) {
        console.error('AuthGuard: Error during redirect:', error);
      }
    }
  }, [loading, profileStatus.loading, profileStatus.error, navigationState, user, segments]);

  // Show loading spinner while checking authentication and profile status
  if (loading || profileStatus.loading) {
    const isDark = colorScheme === 'dark';
    const styles = StyleSheet.create({
      container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: isDark ? '#000000' : '#ffffff',
      },
    });

    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#C29D5B" />
      </View>
    );
  }

  return <>{children}</>;
}
