/**
 * CategorySelector component for React Native Discovery Screen
 * Integrates with existing CategoryBottomSheetPicker
 * Based on dukancard/app/(main)/discover/components/CategoryCarousel.tsx
 */

import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/src/hooks/useTheme";

interface CategorySelectorProps {
  selectedCategory: string | null;
  onCategoryPress: () => void;
  onCategoryClear?: () => void; // Add clear functionality
  disabled?: boolean;
}

export const CategorySelector: React.FC<CategorySelectorProps> = ({
  selectedCategory,
  onCategoryPress,
  onCategoryClear,
  disabled = false,
}) => {
  const { colors } = useTheme();

  const handleCategoryPress = () => {
    if (!disabled) {
      onCategoryPress();
    }
  };

  const handleClearPress = () => {
    if (!disabled && onCategoryClear) {
      onCategoryClear();
    }
  };

  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.selectorButton,
          disabled && styles.disabledButton,
          selectedCategory && styles.selectedButton,
        ]}
        onPress={handleCategoryPress}
        disabled={disabled}
        activeOpacity={0.7}
      >
        <View style={styles.buttonContent}>
          <Ionicons
            name="grid"
            size={20}
            color={selectedCategory ? colors.primary : colors.textSecondary}
            style={styles.icon}
          />

          <Text
            style={[
              styles.buttonText,
              selectedCategory && styles.selectedButtonText,
              disabled && styles.disabledButtonText,
            ]}
            numberOfLines={1}
          >
            {selectedCategory || "Select Category"}
          </Text>

          {selectedCategory ? (
            <TouchableOpacity
              onPress={handleClearPress}
              disabled={disabled}
              style={styles.clearIconButton}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Ionicons name="close-circle" size={20} color={colors.primary} />
            </TouchableOpacity>
          ) : (
            <Ionicons
              name="chevron-down"
              size={20}
              color={colors.textSecondary}
            />
          )}
        </View>
      </TouchableOpacity>
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      paddingHorizontal: 20,
      paddingVertical: 4,
      backgroundColor: colors.background,
    },
    selectorButton: {
      backgroundColor: colors.cardBackground,
      borderRadius: 12,
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderWidth: 1,
      borderColor: colors.border,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    selectedButton: {
      borderColor: colors.primary,
      backgroundColor: colors.primary + "10",
    },
    disabledButton: {
      opacity: 0.5,
      backgroundColor: colors.border + "20",
    },
    buttonContent: {
      flexDirection: "row",
      alignItems: "center",
    },
    icon: {
      marginRight: 12,
    },
    buttonText: {
      flex: 1,
      fontSize: 16,
      color: colors.textSecondary,
    },
    selectedButtonText: {
      color: colors.primary,
      fontWeight: "500",
    },
    disabledButtonText: {
      color: colors.textSecondary + "80",
    },
    clearIconButton: {
      padding: 4,
      marginLeft: 8,
    },
    clearButton: {
      flexDirection: "row",
      alignItems: "center",
      paddingVertical: 8,
      paddingHorizontal: 4,
      marginTop: 8,
    },
    clearButtonText: {
      fontSize: 14,
      color: colors.textSecondary,
      marginLeft: 6,
    },
    hintContainer: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 4,
      marginTop: 8,
    },
    hintText: {
      fontSize: 12,
      color: colors.textSecondary,
      marginLeft: 6,
      flex: 1,
      lineHeight: 16,
    },
  });
