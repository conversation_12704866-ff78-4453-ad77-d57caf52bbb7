import { StyleSheet } from 'react-native';
import { useTheme } from '@/src/hooks/useTheme';

export const createDeleteAccountSectionStyles = (theme: ReturnType<typeof useTheme>) => {
  return StyleSheet.create({
    container: {
      backgroundColor: theme.colors.card,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: '#EF4444' + '30', // Red with 30% opacity
      shadowColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.md,
    },
    header: {
      marginBottom: theme.spacing.lg,
      paddingBottom: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: '#EF4444' + '30', // Red with 30% opacity
    },
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.sm,
    },
    iconContainer: {
      padding: theme.spacing.sm,
      borderRadius: 8,
      backgroundColor: '#EF4444' + '20', // Red with 20% opacity
    },
    titleContent: {
      flex: 1,
    },
    title: {
      fontSize: 18,
      fontWeight: '600',
      color: '#EF4444',
    },
    subtitle: {
      fontSize: 12,
      color: '#EF4444' + 'B3', // Red with 70% opacity
      marginTop: 2,
    },
    warningContainer: {
      backgroundColor: '#EF4444' + '10', // Red with 10% opacity
      borderRadius: 8,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.lg,
      borderWidth: 1,
      borderColor: '#EF4444' + '30', // Red with 30% opacity
    },
    warningContent: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      gap: theme.spacing.sm,
    },
    warningTextContainer: {
      flex: 1,
    },
    warningTitle: {
      fontSize: 14,
      fontWeight: '600',
      color: '#EF4444',
      marginBottom: theme.spacing.xs,
    },
    warningDescription: {
      fontSize: 12,
      color: '#EF4444' + 'CC', // Red with 80% opacity
      lineHeight: 16,
    },
    buttonContainer: {
      alignItems: 'flex-start',
    },
    deleteButton: {
      backgroundColor: '#EF4444',
      minWidth: 140,
    },
    // Modal styles
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      padding: theme.spacing.lg,
    },
    modalContent: {
      backgroundColor: theme.colors.card,
      borderRadius: 12,
      padding: theme.spacing.lg,
      width: '100%',
      maxWidth: 400,
      shadowColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    modalHeader: {
      alignItems: 'center',
      marginBottom: theme.spacing.lg,
    },
    modalIconContainer: {
      width: 64,
      height: 64,
      borderRadius: 32,
      backgroundColor: '#EF4444' + '20', // Red with 20% opacity
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: theme.spacing.md,
    },
    modalTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: theme.colors.foreground,
      textAlign: 'center',
      marginBottom: theme.spacing.sm,
    },
    modalDescription: {
      fontSize: 14,
      color: theme.colors.mutedForeground,
      textAlign: 'center',
      lineHeight: 20,
    },
    modalBody: {
      marginBottom: theme.spacing.lg,
    },
    modalWarningContainer: {
      backgroundColor: '#EF4444' + '10', // Red with 10% opacity
      borderRadius: 8,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.lg,
      borderWidth: 1,
      borderColor: '#EF4444' + '30', // Red with 30% opacity
    },
    modalWarningContent: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      gap: theme.spacing.sm,
    },
    modalWarningTextContainer: {
      flex: 1,
    },
    modalWarningTitle: {
      fontSize: 14,
      fontWeight: '600',
      color: '#EF4444',
      marginBottom: theme.spacing.xs,
    },
    modalWarningList: {
      fontSize: 12,
      color: '#EF4444' + 'CC', // Red with 80% opacity
      lineHeight: 16,
      marginBottom: 2,
    },
    confirmationContainer: {
      gap: theme.spacing.sm,
    },
    confirmationLabel: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.foreground,
    },
    confirmationHighlight: {
      fontWeight: '700',
      color: '#EF4444',
    },
    confirmationInput: {
      marginTop: theme.spacing.xs,
    },
    modalFooter: {
      flexDirection: 'row',
      gap: theme.spacing.md,
    },
    cancelButton: {
      flex: 1,
      padding: theme.spacing.md,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.border,
      backgroundColor: theme.colors.muted,
      alignItems: 'center',
      justifyContent: 'center',
    },
    cancelButtonText: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.foreground,
    },
    confirmDeleteButton: {
      flex: 1,
    },
  });
};
