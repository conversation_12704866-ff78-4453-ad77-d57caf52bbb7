import React from 'react';
import { View, ViewStyle } from 'react-native';
import { SafeAreaView, Edge } from 'react-native-safe-area-context';
import { useDynamicSafeArea } from '@/src/hooks/useDynamicSafeArea';

interface SafeAreaWrapperProps {
  children: React.ReactNode;
  edges?: Edge[];
  style?: ViewStyle;
  mode?: 'padding' | 'margin';
  backgroundColor?: string;
}

/**
 * A wrapper component that provides consistent safe area handling across all screens.
 * Uses the dynamic safe area hook to handle layout shifts when app resumes.
 */
export function SafeAreaWrapper({
  children,
  edges = ['top', 'bottom', 'left', 'right'],
  style,
  mode = 'padding',
  backgroundColor = 'transparent',
}: SafeAreaWrapperProps) {
  // Use the dynamic safe area hook to handle app state changes
  const insets = useDynamicSafeArea();

  return (
    <SafeAreaView
      edges={edges}
      mode={mode}
      style={[
        {
          flex: 1,
          backgroundColor,
        },
        style,
      ]}
    >
      {children}
    </SafeAreaView>
  );
}

/**
 * A minimal safe area wrapper that only handles specific edges.
 * Useful for components that need partial safe area handling.
 */
export function PartialSafeAreaWrapper({
  children,
  edges,
  style,
  mode = 'padding',
}: Omit<SafeAreaWrapperProps, 'backgroundColor'>) {
  const insets = useDynamicSafeArea();

  return (
    <View
      style={[
        {
          ...(edges?.includes('top') && { paddingTop: mode === 'padding' ? insets.top : 0, marginTop: mode === 'margin' ? insets.top : 0 }),
          ...(edges?.includes('bottom') && { paddingBottom: mode === 'padding' ? insets.bottom : 0, marginBottom: mode === 'margin' ? insets.bottom : 0 }),
          ...(edges?.includes('left') && { paddingLeft: mode === 'padding' ? insets.left : 0, marginLeft: mode === 'margin' ? insets.left : 0 }),
          ...(edges?.includes('right') && { paddingRight: mode === 'padding' ? insets.right : 0, marginRight: mode === 'margin' ? insets.right : 0 }),
        },
        style,
      ]}
    >
      {children}
    </View>
  );
}
