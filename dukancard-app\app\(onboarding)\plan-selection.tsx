import { OnboardingContainer } from '@/src/components/layout/OnboardingContainer';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/hooks/useTheme';
import { onboardingPlans, PricingPlan } from '@/lib/PricingPlans';
import { completeBusinessOnboarding, saveOnboardingData } from '@/backend/supabase/services/common/onboardingService';
import { router, useLocalSearchParams } from 'expo-router';
import { Check, ChevronDown, ChevronUp, CreditCard, Rocket, Sparkles } from 'lucide-react-native';
import React, { useEffect, useRef, useState } from 'react';
import { Linking,
    Alert,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { responsiveFontSize } from '@/lib/theme/colors';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { planSelectionSchema, PlanSelectionFormData } from '@/src/utils/validationSchemas';


export default function PlanSelectionScreen() {
  const { user, refreshProfileStatus } = useAuth();
  const theme = useTheme();

  const params = useLocalSearchParams<{
    businessName?: string;
    businessCategory?: string;
    memberName?: string;
    title?: string;
    phone?: string;
    businessSlug?: string;
    addressLine?: string;
    pincode?: string;
    locality?: string;
    city?: string;
    state?: string;
    businessStatus?: string;
    redirect?: string;
    message?: string;
  }>();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPlans, setShowPlans] = useState(false);
  const hasSetInitialPlan = useRef(false);

  // React Hook Form setup
  const {
    control,
    handleSubmit,
    formState: { isValid },
    setValue,
    watch,
  } = useForm<PlanSelectionFormData>({
    resolver: yupResolver(planSelectionSchema),
    defaultValues: {
      planId: onboardingPlans.find(plan => plan.id === 'free')?.id || '',
    },
    mode: 'onChange',
  });

  // Set initial plan only once on mount
  useEffect(() => {
    if (!hasSetInitialPlan.current) {
      const freePlan = onboardingPlans.find(plan => plan.id === 'free');
      if (freePlan) {
        setValue('planId', freePlan.id);
        hasSetInitialPlan.current = true;
      }
    }
  }, [setValue]); // Include setValue in dependency array

  // Save onboarding data from URL parameters
  useEffect(() => {
    const saveData = async () => {
      if (params.businessName) {
        await saveOnboardingData({
          businessName: params.businessName,
          businessCategory: params.businessCategory || '',
          memberName: params.memberName || '',
          title: params.title || '',
          phone: params.phone || '',
          email: user?.email || '',
          businessSlug: params.businessSlug || '',
          addressLine: params.addressLine || '',
          pincode: params.pincode || '',
          locality: params.locality || '',
          city: params.city || '',
          state: params.state || '',
          businessStatus: (params.businessStatus as 'online' | 'offline') || 'online',
        });
      }
    };

    saveData();
  }, [params, user]);

  // Handle plan selection
  const handlePlanSelect = (plan: PricingPlan) => {
    setValue('planId', plan.id);
  };

  const handleFormSubmit = async (values: PlanSelectionFormData) => {
    const selectedPlan = onboardingPlans.find(plan => plan.id === values.planId);

    if (!selectedPlan || !user) {
      Alert.alert('Error', 'Please select a plan to continue.');
      return;
    }

    setIsSubmitting(true);
    try {
      // Save the selected plan
      await saveOnboardingData({ planId: selectedPlan.id });

      // Complete the onboarding process
      const result = await completeBusinessOnboarding(selectedPlan.id);

      if (result.success) {
        // Refresh profile status to update authentication context
        // This is crucial - the AuthGuard needs to know onboarding is complete
        await refreshProfileStatus();

        // Navigate to business dashboard
        // The AuthGuard will now see businessOnboardingCompleted = true
        router.replace('/(dashboard)/business');
      } else {
        Alert.alert('Error', result.error || 'Failed to complete onboarding. Please try again.');
      }
    } catch (error) {
      console.error('Error completing onboarding:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderPlanCard = (plan: PricingPlan, selectedPlanId: string, handlePlanSelect: (plan: PricingPlan) => void) => {
    const isSelected = selectedPlanId === plan.id;
    const isDisabled = !plan.available;

    return (
      <TouchableOpacity
        key={plan.id}
        onPress={() => handlePlanSelect(plan)}
        disabled={isDisabled}
        style={{
          backgroundColor: theme.colors.card,
          borderRadius: theme.borderRadius.lg,
          padding: theme.spacing.md,
          marginBottom: theme.spacing.sm,
          borderWidth: isSelected ? 2 : 1,
          borderColor: isSelected ? (theme.isDark ? theme.brandColors.gold : theme.colors.primary) : theme.colors.border,
          opacity: isDisabled ? 0.5 : 1,
        }}
      >
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
          <View style={{ flex: 1 }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: theme.spacing.xs }}>
              <Text style={{
                fontSize: theme.typography.fontSize.lg,
                fontWeight: '600',
                color: isSelected ? (theme.isDark ? theme.brandColors.gold : theme.colors.primary) : theme.colors.textPrimary,
              }}>
                {plan.name}
              </Text>
              {isDisabled && (
                <Text style={{
                  marginLeft: theme.spacing.xs,
                  fontSize: theme.typography.fontSize.xs,
                  color: theme.colors.textMuted,
                  fontWeight: '500',
                }}>
                  (Coming Soon)
                </Text>
              )}
              {plan.recommended && (
                <View style={{
                  marginLeft: theme.spacing.xs,
                  paddingHorizontal: theme.spacing.xs,
                  paddingVertical: responsiveFontSize(4),
                  backgroundColor: (theme.isDark ? theme.brandColors.gold : theme.colors.primary) + '20',
                  borderRadius: theme.borderRadius.md,
                }}>
                  <Text style={{
                    fontSize: theme.typography.fontSize.xs,
                    color: theme.isDark ? theme.brandColors.gold : theme.colors.primary,
                    fontWeight: '500',
                  }}>
                    Recommended
                  </Text>
                </View>
              )}
            </View>
            <Text style={{
              fontSize: theme.typography.fontSize.base,
              fontWeight: '500',
              color: isSelected ? (theme.isDark ? theme.brandColors.gold : theme.colors.primary) : theme.colors.textPrimary,
            }}>
              {plan.price} {plan.period}
            </Text>
          </View>
          {isSelected && !isDisabled && (
            <View style={{
              width: responsiveFontSize(24),
              height: responsiveFontSize(24),
              borderRadius: responsiveFontSize(12),
              backgroundColor: theme.isDark ? theme.brandColors.gold : theme.colors.primary,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
              <Check size={responsiveFontSize(16)} color="white" />
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <>
      <StatusBar
        style={theme.isDark ? 'light' : 'dark'}
        backgroundColor={theme.colors.background}
      />
      <OnboardingContainer
        currentStep={4}
        totalSteps={4}
        onBack={() => router.back()}
        onNext={handleSubmit(handleFormSubmit)}
        nextButtonText={isSubmitting ? "Completing..." : "Complete Setup"}
        isNextDisabled={isSubmitting || !isValid}
        showProgress={false}
      >
        <View style={{ gap: theme.spacing.md, paddingHorizontal: theme.spacing.xs }}>
          <View style={{ alignItems: 'center', marginBottom: theme.spacing.lg }}>
            <Text style={{
              fontSize: theme.typography.fontSize.xxl,
              fontWeight: '700',
              color: theme.colors.textPrimary,
              textAlign: 'center',
              marginBottom: theme.spacing.xs,
            }}>
              Choose Your Plan
            </Text>
            <Text style={{
              fontSize: theme.typography.fontSize.base,
              color: theme.colors.textSecondary,
              textAlign: 'center',
              lineHeight: theme.typography.lineHeight.normal * theme.typography.fontSize.base,
            }}>
              Step 4 of 4 - Select the perfect plan for your business
            </Text>
            <Text style={{
              fontSize: theme.typography.fontSize.sm,
              color: theme.colors.textSecondary,
              textAlign: 'center',
              marginTop: theme.spacing.sm,
            }}>
              For more details on pricing and features, visit our website:{" "}
              <Text
                style={{ color: theme.colors.primary, textDecorationLine: 'underline' }}
                onPress={() => Linking.openURL('http://dukancard.in/pricing')}
              >
                dukancard.in/pricing
              </Text>
            </Text>
          </View>

          {/* Plan Selection Button */}
          <TouchableOpacity
            onPress={() => setShowPlans(!showPlans)}
            style={{
              borderRadius: theme.borderRadius.lg,
              paddingHorizontal: theme.spacing.md,
              paddingVertical: theme.spacing.md,
              backgroundColor: theme.colors.card,
              borderWidth: 1,
              borderColor: theme.colors.border,
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: theme.spacing.md,
            }}
          >
            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: theme.typography.fontSize.sm,
                color: theme.colors.textSecondary,
                marginBottom: theme.spacing.xs,
              }}>
                Selected Plan
              </Text>
              <Text style={{
                fontSize: theme.typography.fontSize.base,
                fontWeight: '600',
                color: watch('planId') ? theme.colors.textPrimary : theme.colors.textSecondary,
              }}>
                {onboardingPlans.find(plan => plan.id === watch('planId'))?.name || 'Select a plan'}
              </Text>
              {watch('planId') && (
                <Text style={{
                  fontSize: theme.typography.fontSize.sm,
                  color: theme.isDark ? theme.brandColors.gold : theme.colors.primary,
                  marginTop: theme.spacing.xs,
                }}>
                  {onboardingPlans.find(plan => plan.id === watch('planId'))?.price} {onboardingPlans.find(plan => plan.id === watch('planId'))?.period}
                </Text>
              )}
            </View>
            {showPlans ? (
              <ChevronUp size={responsiveFontSize(20)} color={theme.colors.textSecondary} />
            ) : (
              <ChevronDown size={responsiveFontSize(20)} color={theme.colors.textSecondary} />
            )}
          </TouchableOpacity>

          {/* Plans List */}
          {showPlans && (
            <View style={{ gap: theme.spacing.sm }}>
              {onboardingPlans.map(plan => renderPlanCard(plan, watch('planId'), (selectedPlan) => {
                if (!selectedPlan.available) return;
                setValue('planId', selectedPlan.id);
                setShowPlans(false);
              }))}
            </View>
          )}

          {/* Selected Plan Summary */}
          {watch('planId') && !showPlans && (
            <View style={{
              backgroundColor: theme.colors.card,
              borderRadius: theme.borderRadius.lg,
              padding: theme.spacing.md,
              borderWidth: 1,
              borderColor: theme.colors.border,
            }}>
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginBottom: theme.spacing.sm,
              }}>
                <CreditCard size={responsiveFontSize(20)} color={theme.isDark ? theme.brandColors.gold : theme.colors.primary} />
                <Text style={{
                  marginLeft: theme.spacing.xs,
                  fontSize: theme.typography.fontSize.lg,
                  fontWeight: '600',
                  color: theme.colors.textPrimary,
                }}>
                  Selected Plan
                </Text>
              </View>

              <View style={{ marginBottom: theme.spacing.md }}>
                <Text style={{
                  fontSize: theme.typography.fontSize.xxl,
                  fontWeight: '600',
                  color: theme.colors.textPrimary,
                  marginBottom: theme.spacing.xs,
                }}>
                  {onboardingPlans.find(plan => plan.id === watch('planId'))?.name}
                </Text>
                <Text style={{
                  fontSize: theme.typography.fontSize.base,
                  fontWeight: '500',
                  color: theme.colors.textPrimary,
                }}>
                  {onboardingPlans.find(plan => plan.id === watch('planId'))?.price} {onboardingPlans.find(plan => plan.id === watch('planId'))?.period}
                </Text>
              </View>

              {watch('planId') === 'free' && (
                      <View style={{
                        padding: theme.spacing.sm,
                        backgroundColor: theme.colors.success + '10',
                        borderRadius: theme.borderRadius.md,
                      }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <Sparkles size={responsiveFontSize(16)} color={theme.colors.success} />
                        <Text style={{
                          marginLeft: theme.spacing.xs,
                          fontSize: theme.typography.fontSize.sm,
                          color: theme.colors.success,
                          fontWeight: '500',
                        }}>
                          Free Forever Plan - No credit card required!
                        </Text>
                      </View>
                      </View>
                    )}

                    {watch('planId') !== 'free' && (
                      <View style={{
                        padding: theme.spacing.sm,
                        backgroundColor: theme.colors.accent + '10',
                        borderRadius: theme.borderRadius.md,
                      }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <Rocket size={responsiveFontSize(16)} color={theme.colors.accent} />
                        <Text style={{
                          marginLeft: theme.spacing.xs,
                          fontSize: theme.typography.fontSize.sm,
                          color: theme.colors.accent,
                          fontWeight: '500',
                        }}>
                          Start with 1 Month Free Trial
                        </Text>
                      </View>
                        <Text style={{
                          fontSize: theme.typography.fontSize.xs,
                          color: theme.colors.accent,
                          marginTop: theme.spacing.xs,
                        }}>
                          No payment required during trial period
                        </Text>
                      </View>
                    )}
            </View>
          )}
        </View>
      </OnboardingContainer>
    </>
  );
}
