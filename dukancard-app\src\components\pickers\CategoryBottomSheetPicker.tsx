import { useTheme } from "@/src/hooks/useTheme";
import { BUSINESS_CATEGORIES } from "@/lib/config/categories";
import BottomSheet, {
  BottomSheetView,
  BottomSheetFlatList,
  BottomSheetTextInput,
} from "@gorhom/bottom-sheet";
import { Briefcase, Search, X } from "lucide-react-native";
import React, {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useState,
} from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";

interface CategoryBottomSheetPickerProps {
  selectedCategory?: string;
  onCategorySelect: (category: string | null) => void; // Allow null for clear
}

export interface CategoryBottomSheetPickerRef {
  present: () => void;
  dismiss: () => void;
}

// Memoized search header component to prevent re-renders

const CategoryBottomSheetPicker = forwardRef<
  CategoryBottomSheetPickerRef,
  CategoryBottomSheetPickerProps
>(({ selectedCategory, onCategorySelect }, ref) => {
  const { colors, isDark } = useTheme();
  const [searchQuery, setSearchQuery] = useState("");
  const bottomSheetRef = React.useRef<BottomSheet>(null);

  // Snap points for the bottom sheet - using only the working snap point
  const snapPoints = useMemo(() => ["85%"], []);

  // Filter categories based on search query
  const filteredCategories = useMemo(() => {
    if (!searchQuery.trim()) return BUSINESS_CATEGORIES;

    return BUSINESS_CATEGORIES.filter(
      (category) =>
        category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (category.description &&
          category.description
            .toLowerCase()
            .includes(searchQuery.toLowerCase()))
    );
  }, [searchQuery]);

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    present: () => {
      bottomSheetRef.current?.expand();
    },
    dismiss: () => {
      bottomSheetRef.current?.close();
    },
  }));

  const handleCategorySelect = useCallback(
    (category: (typeof BUSINESS_CATEGORIES)[0]) => {
      onCategorySelect(category.name);
      bottomSheetRef.current?.close();
      setSearchQuery(""); // Clear search when closing
    },
    [onCategorySelect]
  );

  const handleClearAll = useCallback(() => {
    onCategorySelect(null);
    bottomSheetRef.current?.close();
    setSearchQuery(""); // Clear search when closing
  }, [onCategorySelect]);

  const handleSheetChanges = useCallback((index: number) => {
    // Clear search when sheet is closed
    if (index === -1) {
      setSearchQuery("");
    }
  }, []);

  const renderCategoryItem = useCallback(
    ({ item }: { item: (typeof BUSINESS_CATEGORIES)[0] }) => {
      const isSelected = selectedCategory === item.name;

      return (
        <TouchableOpacity
          style={[
            styles.categoryItem,
            {
              backgroundColor: isSelected
                ? colors.primary + "20"
                : "transparent",
              borderBottomColor: colors.border,
            },
          ]}
          onPress={() => handleCategorySelect(item)}
        >
          <View style={styles.categoryContent}>
            <Text
              style={[
                styles.categoryName,
                {
                  color: isSelected ? colors.primary : colors.textPrimary,
                  fontWeight: isSelected ? "600" : "400",
                },
              ]}
            >
              {item.name}
            </Text>
            {item.description && (
              <Text
                style={[
                  styles.categoryDescription,
                  { color: colors.textSecondary },
                ]}
              >
                {item.description}
              </Text>
            )}
          </View>
          {isSelected && (
            <View
              style={[
                styles.selectedIndicator,
                { backgroundColor: colors.primary },
              ]}
            />
          )}
        </TouchableOpacity>
      );
    },
    [selectedCategory, colors, handleCategorySelect]
  );

  const renderEmptyState = useCallback(
    () => (
      <View style={styles.emptyState}>
        <Briefcase size={48} color={colors.textSecondary} />
        <Text style={[styles.emptyStateTitle, { color: colors.textPrimary }]}>
          No categories found
        </Text>
        <Text
          style={[
            styles.emptyStateDescription,
            { color: colors.textSecondary },
          ]}
        >
          No categories match &quot;{searchQuery}&quot;
        </Text>
      </View>
    ),
    [colors, searchQuery]
  );

  const renderListHeader = useCallback(
    () => (
      <TouchableOpacity
        style={[
          styles.clearAllItem,
          {
            backgroundColor: selectedCategory
              ? colors.destructive + "10"
              : "transparent",
            borderBottomColor: colors.border,
          },
        ]}
        onPress={handleClearAll}
      >
        <View style={styles.categoryContent}>
          <Text style={[styles.clearAllText, { color: colors.destructive }]}>
            Clear All Categories
          </Text>
          <Text
            style={[
              styles.categoryDescription,
              { color: colors.textSecondary },
            ]}
          >
            Remove category filter to view all items
          </Text>
        </View>
        {selectedCategory && (
          <View
            style={[
              styles.selectedIndicator,
              { backgroundColor: colors.destructive },
            ]}
          />
        )}
      </TouchableOpacity>
    ),
    [selectedCategory, colors, handleClearAll]
  );

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={-1}
      snapPoints={snapPoints}
      onChange={handleSheetChanges}
      enablePanDownToClose
      enableDynamicSizing={false}
      enableContentPanningGesture={false}
      enableHandlePanningGesture={true}
      keyboardBehavior="fillParent"
      keyboardBlurBehavior="restore"
      android_keyboardInputMode="adjustResize"
      backgroundStyle={{
        backgroundColor: isDark ? "#000000" : "#ffffff",
      }}
      handleIndicatorStyle={{
        backgroundColor: colors.textSecondary,
      }}
    >
      <BottomSheetView style={styles.container}>
        {/* Fixed Header with Search - Direct in BottomSheetView */}
        <View
          style={[
            styles.header,
            {
              borderBottomColor: colors.border,
              backgroundColor: isDark ? "#000000" : "#ffffff",
            },
          ]}
        >
          <View style={styles.headerTop}>
            <Text style={[styles.headerTitle, { color: colors.textPrimary }]}>
              Select Category
            </Text>
            <TouchableOpacity
              onPress={() => bottomSheetRef.current?.close()}
              style={styles.closeButton}
            >
              <X size={24} color={colors.textPrimary} />
            </TouchableOpacity>
          </View>

          {/* Search Input - Direct BottomSheetTextInput */}
          <View
            style={[
              styles.searchContainer,
              {
                backgroundColor: isDark ? "#ffffff2e" : "#f5f5f5",
                borderColor: isDark ? "#ffffff26" : "#e5e5e5",
              },
            ]}
          >
            <Search
              size={20}
              color={colors.textSecondary}
              style={styles.searchIcon}
            />
            <BottomSheetTextInput
              style={[styles.searchInput, { color: colors.textPrimary }]}
              placeholder="Search categories..."
              placeholderTextColor={colors.textSecondary}
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoCapitalize="none"
              autoCorrect={false}
              returnKeyType="search"
              autoFocus={false}
              submitBehavior="blurAndSubmit"
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity
                onPress={() => setSearchQuery("")}
                style={styles.clearButton}
              >
                <X size={16} color={colors.textSecondary} />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* List Content - FlatList with Clear All header */}
        <BottomSheetFlatList
          data={filteredCategories}
          renderItem={renderCategoryItem}
          keyExtractor={(item) => item.name}
          showsVerticalScrollIndicator={true}
          ListHeaderComponent={renderListHeader}
          ListEmptyComponent={renderEmptyState}
          contentContainerStyle={styles.listContent}
          keyboardShouldPersistTaps="handled"
          keyboardDismissMode="none"
        />
      </BottomSheetView>
    </BottomSheet>
  );
});

CategoryBottomSheetPicker.displayName = "CategoryBottomSheetPicker";

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  flatList: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
  },
  headerTop: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  closeButton: {
    padding: 4,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 12,
    borderWidth: 1,
    paddingHorizontal: 12,
    height: 48,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    height: "100%",
  },
  clearButton: {
    padding: 4,
    marginLeft: 8,
  },
  listContent: {
    paddingBottom: 120, // Increased padding to ensure bottom items are accessible and clear of the nav bar
  },
  clearAllItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  clearAllText: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  categoryItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  categoryContent: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    marginBottom: 2,
  },
  categoryDescription: {
    fontSize: 12,
    lineHeight: 16,
  },
  selectedIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginLeft: 12,
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateDescription: {
    fontSize: 14,
    textAlign: "center",
    lineHeight: 20,
  },
});

export default CategoryBottomSheetPicker;
