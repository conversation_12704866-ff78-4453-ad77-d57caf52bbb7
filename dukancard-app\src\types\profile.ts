
// Form data interface that includes all fields
export interface FormData {
  name: string;
  address?: string;
  pincode: string;
  locality: string;
  city: string;
  state: string;
  latitude: number;
  longitude: number;
  avatarUri?: string;
}

// Partial type for initial form data (before GPS coordinates are set)
export type InitialFormData = Omit<FormData, "latitude" | "longitude"> & {
  latitude?: number;
  longitude?: number;
};
