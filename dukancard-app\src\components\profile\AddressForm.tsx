import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  ScrollView,
} from 'react-native';
import { Input } from '@/src/components/ui/Input';
import { Button } from '@/src/components/ui/Button';
import { LocationPicker } from '@/src/components/ui/LocationPicker';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { MapPin, Save, Building2, Globe } from 'lucide-react-native';
import { updateCustomerAddress, validatePincode } from '@/backend/supabase/services/common/profileService';
import { useAuth } from '@/src/contexts/AuthContext';
import { usePincodeDetails } from '@/src/hooks/usePincodeDetails';

interface AddressFormProps {
  initialData?: {
    address?: string | null;
    pincode?: string | null;
    city?: string | null;
    state?: string | null;
    locality?: string | null;
  };
  onAddressUpdated?: () => void;
}

interface FormData {
  address: string;
  pincode: string;
  city: string;
  state: string;
  locality: string;
}

interface FormErrors {
  address?: string;
  pincode?: string;
  city?: string;
  state?: string;
  locality?: string;
}

export function AddressForm({ initialData, onAddressUpdated }: AddressFormProps) {
  const { user } = useAuth();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [formData, setFormData] = useState<FormData>({
    address: initialData?.address || '',
    pincode: initialData?.pincode || '',
    city: initialData?.city || '',
    state: initialData?.state || '',
    locality: initialData?.locality || '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);

  const {
    isPincodeLoading,
    availableLocalities,
    handlePincodeChange,
  } = usePincodeDetails({
    onPincodeChange: (details) => {
      setFormData(prev => ({
        ...prev,
        pincode: prev.pincode, // Explicitly preserve the pincode
        city: details?.city || '',
        state: details?.state || '',
        locality: details?.localities[0] || '',
      }));
      // Clear related errors
      setErrors(prev => ({
        ...prev,
        pincode: undefined,
        city: undefined,
        state: undefined,
        locality: undefined,
      }));
    },
  });

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.pincode.trim()) {
      newErrors.pincode = 'Pincode is required';
    } else if (!/^\d{6}$/.test(formData.pincode.trim())) {
      newErrors.pincode = 'Must be a valid 6-digit pincode';
    }

    if (!formData.city.trim()) {
      newErrors.city = 'City is required';
    }

    if (!formData.state.trim()) {
      newErrors.state = 'State is required';
    }

    if (!formData.locality.trim()) {
      newErrors.locality = 'Locality is required';
    }

    if (formData.address.trim().length > 100) {
      newErrors.address = 'Address cannot exceed 100 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    if (!user?.id) {
      Alert.alert('Error', 'User not authenticated');
      return;
    }

    setIsLoading(true);
    try {
      const result = await updateCustomerAddress({
        address: formData.address.trim() || undefined,
        pincode: formData.pincode.trim(),
        city: formData.city.trim(),
        state: formData.state.trim(),
        locality: formData.locality.trim(),
      });

      if (result.success) {
        Alert.alert('Success', 'Address updated successfully!');
        onAddressUpdated?.();
      } else {
        Alert.alert('Error', result.error || 'Failed to update address');
      }
    } catch (error) {
      console.error('Address update error:', error);
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePincodeInputChange = (text: string) => {
    // Only allow digits and limit to 6 characters
    const numericText = text.replace(/[^0-9]/g, '').slice(0, 6);
    setFormData(prev => ({ ...prev, pincode: numericText }));
    
    // Clear pincode error when user starts typing
    if (errors.pincode) {
      setErrors(prev => ({ ...prev, pincode: undefined }));
    }

    // Auto-fetch details when pincode is complete
    if (numericText.length === 6) {
      handlePincodeChange(numericText);
    } else {
      // Clear dependent fields when pincode is incomplete
      setFormData(prev => ({
        ...prev,
        city: '',
        state: '',
        locality: '',
      }));
    }
  };

  const handleLocationDetected = (latitude: number, longitude: number) => {
    // You could optionally store these coordinates if needed for the address
  };

  const handleAddressDetected = (address: { pincode: string; city: string; state: string; locality: string }) => {
    // Auto-populate form fields with detected address
    setFormData(prev => ({
      ...prev,
      pincode: address.pincode,
      city: address.city,
      state: address.state,
      locality: address.locality,
    }));

    // Clear any existing errors for these fields
    setErrors(prev => ({
      ...prev,
      pincode: undefined,
      city: undefined,
      state: undefined,
      locality: undefined,
    }));

    // Fetch localities for the detected pincode to ensure consistency
    handlePincodeChange(address.pincode);
  };

  const handleFieldChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: isDark ? '#1a1a1a' : '#fff' }]}>
      <View style={styles.header}>
        <View style={[styles.iconContainer, { backgroundColor: isDark ? '#2a2a2a' : '#f8f9fa' }]}>
          <MapPin size={24} color={isDark ? '#D4AF37' : '#D4AF37'} />
        </View>
        <Text style={[styles.title, { color: isDark ? '#fff' : '#000' }]}>Manage Address</Text>
        <Text style={[styles.subtitle, { color: isDark ? '#999' : '#666' }]}>
          Update your location and delivery address
        </Text>
      </View>

      <View style={styles.form}>
        {/* GPS Location Picker */}
        <View style={styles.locationSection}>
          <Text style={[styles.sectionTitle, { color: isDark ? '#fff' : '#000' }]}>
            Auto-detect Location
          </Text>
          <LocationPicker
            onLocationDetected={handleLocationDetected}
            onAddressDetected={handleAddressDetected}
            onError={(error) => {
              Alert.alert('Location Error', error);
            }}
            disabled={isLoading || isPincodeLoading}
          />
        </View>

        {/* Address Field (Optional) */}
        <View style={styles.inputGroup}>
          <Text style={[styles.label, { color: isDark ? '#fff' : '#000' }]}>
            Address <Text style={styles.optional}>(Optional)</Text>
          </Text>
          <Input
            value={formData.address}
            onChangeText={(text) => handleFieldChange('address', text)}
            placeholder="House/Flat No., Street Name"
            error={errors.address}
            leftIcon={<Building2 size={20} color={isDark ? '#999' : '#666'} />}
            editable={!isLoading}
            maxLength={100}
          />
        </View>

        {/* Pincode Field */}
        <View style={styles.inputGroup}>
          <Text style={[styles.label, { color: isDark ? '#fff' : '#000' }]}>
            Pincode <Text style={styles.required}>*</Text>
          </Text>
          <View style={styles.pincodeContainer}>
            <Input
              value={formData.pincode}
              onChangeText={handlePincodeInputChange}
              placeholder="Enter 6-digit pincode"
              error={errors.pincode}
              leftIcon={<Globe size={20} color={isDark ? '#999' : '#666'} />}
              keyboardType="numeric"
              maxLength={6}
              editable={!isLoading}
              containerStyle={styles.pincodeInput}
            />
            {isPincodeLoading && (
              <ActivityIndicator 
                size="small" 
                color="#D4AF37" 
                style={styles.pincodeLoader}
              />
            )}
          </View>
          <Text style={[styles.helperText, { color: isDark ? '#999' : '#666' }]}>
            6-digit pincode to auto-fill city and state
          </Text>
        </View>

        {/* City Field (Auto-filled) */}
        <View style={styles.inputGroup}>
          <Text style={[styles.label, { color: isDark ? '#fff' : '#000' }]}>
            City <Text style={styles.required}>*</Text>
          </Text>
          <Input
            value={formData.city}
            onChangeText={(text) => handleFieldChange('city', text)}
            placeholder="Auto-filled from Pincode"
            error={errors.city}
            leftIcon={<MapPin size={20} color={isDark ? '#999' : '#666'} />}
            editable={false}
            style={[styles.readOnlyInput, { backgroundColor: isDark ? '#2a2a2a' : '#f5f5f5' }]}
          />
        </View>

        {/* State Field (Auto-filled) */}
        <View style={styles.inputGroup}>
          <Text style={[styles.label, { color: isDark ? '#fff' : '#000' }]}>
            State <Text style={styles.required}>*</Text>
          </Text>
          <Input
            value={formData.state}
            onChangeText={(text) => handleFieldChange('state', text)}
            placeholder="Auto-filled from Pincode"
            error={errors.state}
            leftIcon={<MapPin size={20} color={isDark ? '#999' : '#666'} />}
            editable={false}
            style={[styles.readOnlyInput, { backgroundColor: isDark ? '#2a2a2a' : '#f5f5f5' }]}
          />
        </View>

        {/* Locality Field */}
        <View style={styles.inputGroup}>
          <Text style={[styles.label, { color: isDark ? '#fff' : '#000' }]}>
            Locality <Text style={styles.required}>*</Text>
          </Text>
          <Input
            value={formData.locality}
            onChangeText={(text) => handleFieldChange('locality', text)}
            placeholder="Enter your locality"
            error={errors.locality}
            leftIcon={<Building2 size={20} color={isDark ? '#999' : '#666'} />}
            editable={!isLoading}
          />
          {availableLocalities.length > 0 && (
            <Text style={[styles.helperText, { color: isDark ? '#999' : '#666' }]}>
              Available localities: {availableLocalities.join(', ')}
            </Text>
          )}
        </View>

        <View style={styles.buttonContainer}>
          <Button
            title={isLoading ? 'Updating...' : 'Save Address'}
            onPress={handleSubmit}
            disabled={isLoading || isPincodeLoading || !formData.pincode.trim()}
            variant="primary"
            icon={
              isLoading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Save size={20} color="#fff" />
              )
            }
          />
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
  form: {
    gap: 20,
  },
  locationSection: {
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
  },
  inputGroup: {
    gap: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
  },
  required: {
    color: '#ef4444',
  },
  optional: {
    color: '#999',
    fontWeight: '400',
  },
  pincodeContainer: {
    position: 'relative',
  },
  pincodeInput: {
    flex: 1,
  },
  pincodeLoader: {
    position: 'absolute',
    right: 16,
    top: 20,
  },
  readOnlyInput: {
    opacity: 0.7,
  },
  helperText: {
    fontSize: 12,
    marginTop: 4,
  },
  buttonContainer: {
    marginTop: 8,
  },
});
