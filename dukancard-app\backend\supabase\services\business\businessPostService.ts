import { supabase } from '@/lib/supabase';
import { BusinessPostFormData } from '@/lib/schemas/postSchemas';

export class BusinessPostService {
  /**
   * Fetches a business profile by user ID.
   */
  static async getBusinessProfileById(userId: string) {
    return await supabase
      .from('business_profiles')
      .select('id, city_slug, state_slug, locality_slug, pincode, logo_url')
      .eq('id', userId)
      .single();
  }

  /**
   * Inserts a new business post into the database.
   */
  static async insertBusinessPost(postData: any) {
    return await supabase
      .from('business_posts')
      .insert(postData)
      .select()
      .single();
  }

  /**
   * Checks if a business post exists and belongs to the user.
   */
  static async getBusinessPostByIdAndUserId(postId: string, userId: string) {
    return await supabase
      .from('business_posts')
      .select('id, created_at, image_url')
      .eq('id', postId)
      .eq('business_id', userId)
      .single();
  }

  /**
   * Updates an existing business post.
   */
  static async updateBusinessPost(postId: string, updateData: any) {
    return await supabase
      .from('business_posts')
      .update(updateData)
      .eq('id', postId)
      .select()
      .single();
  }

  /**
   * Deletes a business post.
   */
  static async deleteBusinessPost(postId: string) {
    return await supabase
      .from('business_posts')
      .delete()
      .eq('id', postId);
  }

  /**
   * Fetches business posts for a specific business.
   */
  static async getBusinessPosts(businessId: string, offset: number, limit: number) {
    return await supabase
      .from('business_posts')
      .select(`
        *,
        business_profiles!inner (
          id,
          business_name,
          logo_url,
          business_slug,
          phone,
          whatsapp_number,
          city,
          state
        )
      `)
      .eq('business_id', businessId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);
  }

  /**
   * Fetches a single business post by ID.
   */
  static async getSingleBusinessPost(postId: string) {
    return await supabase
      .from('business_posts')
      .select(`
        *,
        business_profiles!inner (
          id,
          business_name,
          logo_url,
          business_slug,
          phone,
          whatsapp_number,
          city,
          state
        )
      `)
      .eq('id', postId)
      .single();
  }
}
