import React, { useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Animated } from 'react-native';
import { FeedFilterType } from '@/lib/types/posts';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { Colors } from '@/src/constants/Colors';
import {
  Sparkles,
  Users,
  MapPin,
  Hash,
  Building2,
  Globe,
  FileText
} from 'lucide-react-native';

interface FeedFiltersProps {
  activeFilter: FeedFilterType;
  onFilterChange: (filter: FeedFilterType) => void;
  isLoading?: boolean;
}

interface FilterOption {
  value: FeedFilterType;
  label: string;
  icon: React.ComponentType<any>;
}

const filterOptions: FilterOption[] = [
  {
    value: 'smart',
    label: 'Smart Feed',
    icon: Sparkles,
  },
  {
    value: 'subscribed',
    label: 'Following',
    icon: Users,
  },
  {
    value: 'locality',
    label: 'Locality',
    icon: MapPin,
  },
  {
    value: 'pincode',
    label: 'Pincode',
    icon: Hash,
  },
  {
    value: 'city',
    label: 'City',
    icon: Building2,
  },
  {
    value: 'state',
    label: 'State',
    icon: Globe,
  },
  {
    value: 'all',
    label: 'All Posts',
    icon: FileText,
  },
];

export function FeedFilters({ activeFilter, onFilterChange, isLoading = false }: FeedFiltersProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(20)).current;

  useEffect(() => {
    // Animate filter pills entrance
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        delay: 100,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        delay: 100,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, slideAnim]);

  // Theme colors - transparent background to match feed header
  const textColor = isDark ? '#FFFFFF' : '#000000';
  const mutedTextColor = isDark ? '#A1A1AA' : '#71717A';
  const primaryColor = Colors[colorScheme ?? 'light'].primary;
  const cardColor = isDark ? '#1F1F1F' : '#F9FAFB';
  const borderColor = isDark ? '#374151' : '#E5E7EB';

  // Get current filter label
  const currentFilterOption = filterOptions.find(option => option.value === activeFilter);
  const currentFilterLabel = currentFilterOption?.label || 'Smart Feed';

  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        }
      ]}
    >
      <View style={styles.header}>
        <Text style={[styles.title, { color: textColor }]}>{currentFilterLabel}</Text>
        <Text style={[styles.subtitle, { color: mutedTextColor }]}>Tap to change filter</Text>
      </View>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.filtersContainer}
      >
        {filterOptions.map((option) => {
          const isActive = activeFilter === option.value;
          const IconComponent = option.icon;

          return (
            <TouchableOpacity
              key={option.value}
              onPress={() => !isLoading && onFilterChange(option.value)}
              disabled={isLoading}
              style={[
                styles.filterPill,
                {
                  backgroundColor: isActive ? primaryColor : cardColor,
                  borderColor: isActive ? primaryColor : borderColor,
                },
                isLoading && styles.disabledFilter,
              ]}
            >
              <IconComponent
                size={18}
                color={isActive ? '#FFFFFF' : (isDark ? '#E5E7EB' : '#374151')}
              />
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
    paddingHorizontal: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
  },
  subtitle: {
    fontSize: 12,
  },
  filtersContainer: {
    paddingHorizontal: 16,
    gap: 8,
  },
  filterPill: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 44,
    height: 44,
    borderRadius: 22,
    borderWidth: 1,
    marginRight: 8,
  },
  disabledFilter: {
    opacity: 0.5,
  },
});
