/**
 * ViewToggle component for React Native Discovery Screen
 * Based on dukancard/app/(main)/discover/components/ViewToggle.tsx
 */

import React from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/src/hooks/useTheme";

interface ViewToggleProps {
  viewType: "cards" | "products";
  onViewChange: (view: "cards" | "products") => void;
  disabled?: boolean;
}

export const ViewToggle: React.FC<ViewToggleProps> = ({
  viewType,
  onViewChange,
  disabled = false,
}) => {
  const { colors } = useTheme();

  const handleViewChange = (view: "cards" | "products") => {
    if (!disabled && view !== viewType) {
      onViewChange(view);
    }
  };

  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      <View style={styles.toggleContainer}>
        {/* Products Button */}
        <TouchableOpacity
          style={[
            styles.toggleButton,
            styles.leftButton,
            viewType === "products" && styles.activeButton,
            disabled && styles.disabledButton,
          ]}
          onPress={() => handleViewChange("products")}
          disabled={disabled}
          activeOpacity={0.7}
        >
          <View style={styles.buttonContent}>
            <Ionicons
              name="cube"
              size={18}
              color={
                viewType === "products"
                  ? colors.primaryForeground
                  : colors.textSecondary
              }
              style={styles.buttonIcon}
            />
            <Text
              style={[
                styles.buttonText,
                viewType === "products" && styles.activeButtonText,
                disabled && styles.disabledButtonText,
              ]}
            >
              Products
            </Text>
          </View>
        </TouchableOpacity>

        {/* Business Cards Button */}
        <TouchableOpacity
          style={[
            styles.toggleButton,
            styles.rightButton,
            viewType === "cards" && styles.activeButton,
            disabled && styles.disabledButton,
          ]}
          onPress={() => handleViewChange("cards")}
          disabled={disabled}
          activeOpacity={0.7}
        >
          <View style={styles.buttonContent}>
            <Ionicons
              name="business"
              size={18}
              color={
                viewType === "cards"
                  ? colors.primaryForeground
                  : colors.textSecondary
              }
              style={styles.buttonIcon}
            />
            <Text
              style={[
                styles.buttonText,
                viewType === "cards" && styles.activeButtonText,
                disabled && styles.disabledButtonText,
              ]}
            >
              Business Cards
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      paddingHorizontal: 20,
      paddingVertical: 8,
      backgroundColor: colors.background,
      alignItems: "center",
    },
    toggleContainer: {
      flexDirection: "row",
      backgroundColor: colors.cardBackground,
      borderRadius: 12,
      padding: 4,
      borderWidth: 1,
      borderColor: colors.border,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    toggleButton: {
      flex: 1,
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 8,
      alignItems: "center",
      justifyContent: "center",
      minHeight: 44,
    },
    leftButton: {
      marginRight: 2,
    },
    rightButton: {
      marginLeft: 2,
    },
    activeButton: {
      backgroundColor: colors.primary,
      shadowColor: colors.primary,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.2,
      shadowRadius: 4,
      elevation: 4,
    },
    disabledButton: {
      opacity: 0.5,
    },
    buttonContent: {
      flexDirection: "row",
      alignItems: "center",
    },
    buttonIcon: {
      marginRight: 6,
    },
    buttonText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.textSecondary,
    },
    activeButtonText: {
      color: colors.primaryForeground,
      fontWeight: "600",
    },
    disabledButtonText: {
      color: colors.textSecondary + "80",
    },
  });
