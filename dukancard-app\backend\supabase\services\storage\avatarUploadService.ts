import { getCurrentUser } from "@/lib/auth/customerAuth";
import { supabase } from "@/lib/supabase";
import {
  deleteProfileImage,
  extractFilePathFromUrl,
} from "@/backend/supabase/services/storage/storageService";
import { getCustomerAvatarPath } from "@/backend/supabase/utils/storage-paths";
import * as ImagePicker from "expo-image-picker";
import { Alert } from "react-native";

export interface AvatarUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface CameraPermissionResult {
  granted: boolean;
  canAskAgain: boolean;
  status: string;
}

export interface MediaLibraryPermissionResult {
  granted: boolean;
  canAskAgain: boolean;
  status: string;
}

/**
 * Request camera permission using the latest Expo method
 */
export async function requestCameraPermission(): Promise<CameraPermissionResult> {
  try {
    const { status, canAskAgain } =
      await ImagePicker.requestCameraPermissionsAsync();

    return {
      granted: status === ImagePicker.PermissionStatus.GRANTED,
      canAskAgain,
      status: status,
    };
  } catch (error) {
    console.error("Error requesting camera permission:", error);
    return {
      granted: false,
      canAskAgain: false,
      status: "denied",
    };
  }
}

/**
 * Check camera permission status
 */
export async function checkCameraPermission(): Promise<CameraPermissionResult> {
  try {
    const { status, canAskAgain } =
      await ImagePicker.getCameraPermissionsAsync();

    return {
      granted: status === ImagePicker.PermissionStatus.GRANTED,
      canAskAgain,
      status,
    };
  } catch (error) {
    console.error("Error checking camera permission:", error);
    return {
      granted: false,
      canAskAgain: false,
      status: "denied",
    };
  }
}

/**
 * Request media library permission
 */
export async function requestMediaLibraryPermission(): Promise<MediaLibraryPermissionResult> {
  try {
    const { status, canAskAgain } =
      await ImagePicker.requestMediaLibraryPermissionsAsync();

    return {
      granted: status === ImagePicker.PermissionStatus.GRANTED,
      canAskAgain,
      status,
    };
  } catch (error) {
    console.error("Error requesting media library permission:", error);
    return {
      granted: false,
      canAskAgain: false,
      status: "denied",
    };
  }
}

/**
 * Check media library permission status
 */
export async function checkMediaLibraryPermission(): Promise<MediaLibraryPermissionResult> {
  try {
    const { status, canAskAgain } =
      await ImagePicker.getMediaLibraryPermissionsAsync();

    return {
      granted: status === ImagePicker.PermissionStatus.GRANTED,
      canAskAgain,
      status,
    };
  } catch (error) {
    console.error("Error checking media library permission:", error);
    return {
      granted: false,
      canAskAgain: false,
      status: "denied",
    };
  }
}

/**
 * Open camera to take a photo with advanced cropping
 */
export async function openCamera(): Promise<ImagePicker.ImagePickerResult> {
  // Check camera permission first
  const permission = await checkCameraPermission();

  if (!permission.granted) {
    const requestResult = await requestCameraPermission();
    if (!requestResult.granted) {
      throw new Error("Camera permission is required to take photos");
    }
  }

  return await ImagePicker.launchCameraAsync({
    mediaTypes: ["images"], // Updated to use array format instead of deprecated MediaTypeOptions
    allowsEditing: false, // Disable native editing - we'll process images ourselves
    quality: 1.0, // Higher quality for processing
    base64: false,
  });
}

/**
 * Open camera and return processed image
 */
export async function openCameraForAvatar(): Promise<ImagePicker.ImagePickerResult> {
  try {
    // Take a photo using expo-image-picker
    const result = await openCamera();
    return result;
  } catch (error) {
    console.error("Camera error:", error);
    throw error;
  }
}

/**
 * Open image picker to select from gallery
 */
export async function openImagePicker(): Promise<ImagePicker.ImagePickerResult> {
  // Check media library permission first
  const permission = await checkMediaLibraryPermission();

  if (!permission.granted) {
    const requestResult = await requestMediaLibraryPermission();
    if (!requestResult.granted) {
      throw new Error("Media library permission is required to select photos");
    }
  }

  return await ImagePicker.launchImageLibraryAsync({
    mediaTypes: ["images"], // Updated to use array format instead of deprecated MediaTypeOptions
    allowsEditing: false, // Disable native editing - we'll process images ourselves
    quality: 1.0, // Higher quality for processing
    base64: false,
  });
}

/**
 * Open gallery and return processed image
 */
export async function openGalleryForAvatar(): Promise<ImagePicker.ImagePickerResult> {
  try {
    // Select an image from gallery
    const result = await openImagePicker();
    return result;
  } catch (error) {
    console.error("Gallery error:", error);
    throw error;
  }
}

/**
 * Show image picker options (Camera or Gallery) with permission-aware options
 */
export async function showImagePickerOptions(): Promise<
  "camera" | "gallery" | null
> {
  // Check camera permission status
  const cameraPermission = await checkCameraPermission();

  return new Promise((resolve) => {
    const options = [];

    // Only show camera option if permission is granted or can be requested
    if (cameraPermission.granted || cameraPermission.canAskAgain) {
      options.push({
        text: "Camera",
        onPress: () => resolve("camera"),
      });
    }

    // Always show gallery option
    options.push({
      text: "Gallery",
      onPress: () => resolve("gallery"),
    });

    options.push({
      text: "Cancel",
      style: "cancel" as const,
      onPress: () => resolve(null),
    });

    Alert.alert(
      "Select Avatar",
      cameraPermission.granted || cameraPermission.canAskAgain
        ? "Choose how you want to set your profile picture"
        : "Camera permission denied. You can select from gallery.",
      options,
      { cancelable: true, onDismiss: () => resolve(null) }
    );
  });
}

/**
 * Upload avatar image to Supabase storage
 * Note: Image should be compressed on client-side before calling this function
 */
export async function uploadAvatarImage(
  imageUri: string,
  userId?: string
): Promise<AvatarUploadResult> {
  try {
    console.log("[AVATAR_UPLOAD] Starting upload process:", {
      imageUri,
      userId,
    });

    let user;
    if (userId) {
      user = { id: userId };
    } else {
      user = await getCurrentUser();
      if (!user) {
        return { success: false, error: "User not authenticated" };
      }
    }

    console.log("[AVATAR_UPLOAD] User identified:", { userId: user.id });

    // For React Native, we need to handle local file URIs carefully
    // Read the file and upload directly to Supabase

    try {
      // For React Native, use expo-file-system to read the file as base64
      // This is the recommended approach for Supabase uploads from React Native
      const { readAsStringAsync } = await import("expo-file-system");
      const { decode } = await import("base64-arraybuffer");

      // Read the file as base64
      const base64 = await readAsStringAsync(imageUri, {
        encoding: "base64",
      });

      // Convert base64 to ArrayBuffer
      const arrayBuffer = decode(base64);

      // Validate ArrayBuffer
      if (!arrayBuffer || arrayBuffer.byteLength === 0) {
        throw new Error("Empty or invalid ArrayBuffer created from image");
      }

      // Validate file size (should be under 50KB as per compression target)
      if (arrayBuffer.byteLength > 50 * 1024) {
        throw new Error(
          "Image file is too large. Please choose a smaller image or try again."
        );
      }

      // Upload ArrayBuffer directly to Supabase (React Native doesn't support Blob from ArrayBuffer)
      const timestamp = Date.now() + Math.floor(Math.random() * 1000);
      const filePath = getCustomerAvatarPath(user.id, timestamp);

      // Upload directly using Supabase storage
      const { error } = await supabase.storage
        .from("customers")
        .upload(filePath, arrayBuffer, {
          contentType: "image/webp",
          upsert: true,
        });

      if (error) {
        return {
          success: false,
          error: `Failed to upload avatar: ${error.message}`,
        };
      }

      // Get public URL for the uploaded file
      const { data: publicUrlData } = supabase.storage
        .from("customers")
        .getPublicUrl(filePath);

      return { success: true, url: publicUrlData.publicUrl };
    } catch (fetchError) {
      return {
        success: false,
        error:
          "Failed to read image file. Please try a different image or check your internet connection.",
      };
    }
  } catch (error) {
    console.error("Error uploading avatar:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "An unexpected error occurred while uploading avatar",
    };
  }
}

/**
 * Update customer profile with new avatar URL
 */
export async function updateCustomerAvatarUrl(
  avatarUrl: string | null
): Promise<AvatarUploadResult> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: "User not authenticated" };
    }

    const { error } = await supabase
      .from("customer_profiles")
      .update({
        avatar_url: avatarUrl,
        updated_at: new Date().toISOString(),
      })
      .eq("id", user.id);

    if (error) {
      console.error("Error updating avatar URL:", error);
      return {
        success: false,
        error: `Failed to update avatar URL: ${error.message}`,
      };
    }

    return { success: true };
  } catch (error) {
    console.error("Error updating customer avatar URL:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to update avatar URL",
    };
  }
}

/**
 * Delete customer avatar from Supabase storage and update profile
 */
export async function deleteCustomerAvatar(
  avatarUrl: string
): Promise<AvatarUploadResult> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: "User not authenticated" };
    }

    // Extract file path from the public URL
    const filePath = extractFilePathFromUrl(avatarUrl);
    if (!filePath) {
      return { success: false, error: "Invalid avatar URL provided" };
    }

    // Delete the image from storage
    const deleteResult = await deleteProfileImage("customer", filePath);
    if (!deleteResult.success) {
      console.error("Error deleting avatar from storage:", deleteResult.error);
      return {
        success: false,
        error: deleteResult.error || "Failed to delete avatar from storage",
      };
    }

    // Update the customer profile to remove the avatar URL
    const updateResult = await updateCustomerAvatarUrl(null);
    if (!updateResult.success) {
      console.error(
        "Error updating customer profile after avatar deletion:",
        updateResult.error
      );
      return {
        success: false,
        error:
          updateResult.error ||
          "Failed to update profile after avatar deletion",
      };
    }

    return { success: true };
  } catch (error) {
    console.error("Error in deleteCustomerAvatar:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to delete avatar",
    };
  }
}
