export interface BlogsRow {
  id: string;
  title: string;
  slug: string | null;
  content: string;
  excerpt: string | null;
  featured_image_url: string | null;
  author_name: string;
  author_email: string | null;
  status: string;
  meta_title: string | null;
  meta_description: string | null;
  categories: string[] | null;
  tags: string[] | null;
  reading_time_minutes: number | null;
  published_at: string | null;
  created_at: string;
  updated_at: string;
}

export interface BlogsInsert {
  id?: string;
  title: string;
  slug?: string | null;
  content: string;
  excerpt?: string | null;
  featured_image_url?: string | null;
  author_name?: string;
  author_email?: string | null;
  status?: string;
  meta_title?: string | null;
  meta_description?: string | null;
  categories?: string[] | null;
  tags?: string[] | null;
  reading_time_minutes?: number | null;
  published_at?: string | null;
  created_at?: string;
  updated_at?: string;
}

export interface BlogsUpdate {
  id?: string;
  title?: string;
  slug?: string | null;
  content?: string;
  excerpt?: string | null;
  featured_image_url?: string | null;
  author_name?: string;
  author_email?: string | null;
  status?: string;
  meta_title?: string | null;
  meta_description?: string | null;
  categories?: string[] | null;
  tags?: string[] | null;
  reading_time_minutes?: number | null;
  published_at?: string | null;
  created_at?: string;
  updated_at?: string;
}

export type Blogs = BlogsRow;
