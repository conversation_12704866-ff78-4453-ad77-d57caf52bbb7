# Discovery System Complete Restructure PRP

## Project Overview

Complete restructure of React Native discovery system to exactly replicate Next.js backend logic while adapting for mobile-specific location requirements.

## Problem Statement

Current React Native discovery system fails to fetch products and has inconsistent behavior compared to the working Next.js implementation. The core issues are:

1. **Incorrect Query Structure**: Using single-step queries instead of Next.js two-step approach
2. **Missing Security Layer**: No equivalent of `getSecureBusinessProfiles`
3. **Wrong Location Logic**: Filtering by city in wrong places
4. **Inconsistent Field Selection**: Different patterns than Next.js
5. **Broken Pagination**: Database-level pagination not working

## Critical Constraint

**NO FRONTEND CHANGES ALLOWED**: The existing React Native frontend components, contexts, and UI must remain completely unchanged. Only backend service logic will be modified to match Next.js patterns while maintaining identical API interfaces.

## Success Criteria

- [ ] Products and business cards load correctly in all scenarios
- [ ] Search functionality works across all 4 cases (product name, business name, location, no criteria)
- [ ] Location filtering uses stored user location when no explicit location given
- [ ] Database-level pagination works efficiently
- [ ] Performance matches or exceeds current implementation
- [ ] Type safety maintained across all components
- [ ] **Zero frontend component changes required**
- [ ] Existing DiscoveryContext API remains identical

## Technical Requirements

### Core Architecture Changes

1. **File Structure Reorganization**: Mirror Next.js actions structure
2. **Query Logic Replication**: Exact copy of Next.js database queries
3. **Location Adaptation**: Use stored user location instead of all India data
4. **Security Implementation**: React Native equivalent of secure business methods

### Key Differences from Next.js

- **Location Behavior**: When no location filter provided, fetch user's stored location from business/customer profile
- **Data Storage**: Store fetched address in AsyncStorage until manually changed
- **Fallback Logic**: Only show all India data if no user location available

## Implementation Plan

### Phase 1: Foundation Setup (Steps 1-3)

#### Step 1: Clean Current Implementation

**Task**: Remove existing broken discovery backend logic

**Files to Delete**:

- `dukancard-app/src/services/discoveryService.ts` (main service file)
- `dukancard-app/src/services/debouncedDiscoveryService.ts` (debounced wrapper)
- Any other discovery-related service files

**What NOT to touch**:

- `dukancard-app/src/contexts/DiscoveryContext.tsx` (frontend context)
- `dukancard-app/src/types/discovery.ts` (type definitions)
- Any UI components or screens
- Any context providers or hooks

**Detailed Actions**:

1. Delete `discoveryService.ts` completely
2. Delete `debouncedDiscoveryService.ts` completely
3. Update imports in DiscoveryContext to point to new service location
4. Ensure no broken imports remain

#### Step 2: Create New File Structure

**Task**: Set up new discovery service architecture mirroring Next.js

**Reference**: `dukancard/app/(main)/discover/actions/` structure

**New Directory Structure**:

```
dukancard-app/src/services/discovery/
├── index.ts                     # Main exports (replaces discoveryService.ts)
├── combinedActions.ts           # Main search logic (exact copy of Next.js)
├── productActions.ts            # Product queries (exact copy of Next.js)
├── businessActions.ts           # Business queries (adapted from Next.js)
├── locationActions.ts           # Location searches (adapted from Next.js)
├── types.ts                     # Type definitions (exact copy of Next.js)
└── utils/
    ├── secureBusinessProfiles.ts # RN equivalent of getSecureBusinessProfiles
    ├── sortMappings.ts          # Sorting utilities (exact copy of Next.js)
    └── locationUtils.ts         # RN-specific location handling
```

**Detailed Actions**:

1. Create `dukancard-app/src/services/discovery/` directory
2. Create all files listed above (empty initially)
3. Set up proper TypeScript exports in index.ts
4. Ensure directory structure matches exactly

#### Step 3: Copy Type Definitions

**Task**: Replicate Next.js type system exactly

**Reference**: `dukancard/app/(main)/discover/actions/types.ts`

**Detailed Actions**:

1. Copy entire contents of Next.js `types.ts` to new `discovery/types.ts`
2. Verify all type imports work in React Native environment
3. Ensure no conflicts with existing `dukancard-app/src/types/discovery.ts`
4. Update any Supabase type references for React Native client
5. Test TypeScript compilation with new types

### Phase 2: Core Actions Implementation (Steps 4-7)

#### Step 4: Implement combinedActions.ts

**Task**: Create main search orchestration logic exactly like Next.js

**Reference**: `dukancard/app/(main)/discover/actions/combinedActions.ts`

**Function to Implement**: `searchDiscoverCombined`

**Exact Logic to Copy**:

```typescript
// Case 1: Product name search (products view only)
if (viewType === "products" && productName && productName.trim().length > 0) {
  // Call fetchAllProducts with productName filter
}
// Case 2: Business name search
else if (businessName && businessName.trim().length > 0) {
  // Call fetchBusinessesBySearch with businessName filter
}
// Case 3: Location search (pincode or city)
else if (pincode || city) {
  // Call searchDiscoverByLocation
}
// Case 4: No search criteria
else {
  // Call appropriate function based on viewType
}
```

**Critical Requirements**:

1. **Exact same function signature** as Next.js version
2. **Identical parameter handling** and validation
3. **Same return type structure** with data/error pattern
4. **Preserve authentication check** logic
5. **Maintain exact case conditions** (no modifications to if/else logic)

**Detailed Actions**:

1. Copy entire `searchDiscoverCombined` function from Next.js
2. Replace Next.js server imports with React Native equivalents
3. Update Supabase client calls (server vs client syntax)
4. Ensure all function calls match new file structure
5. Test each of the 4 cases individually
6. Verify return types match frontend expectations

#### Step 5: Implement productActions.ts

**Task**: Create product search logic with exact Next.js query patterns

**Reference**: `dukancard/app/(main)/discover/actions/productActions.ts`

**Functions to Implement**:

1. `fetchAllProducts` (main function)
2. `convertToNearbyProduct` (helper function)

**Critical Two-Step Query Pattern**:

```typescript
// Step 1: Get valid business IDs
let businessQuery = supabase
  .from("business_profiles")
  .select("id")
  .eq("status", "online");

// Apply filters ONLY for pincode and locality (NOT city)
if (pincode) businessQuery = businessQuery.eq("pincode", pincode);
if (locality) businessQuery = businessQuery.eq("locality", locality);

// Step 2: Count products from valid businesses
let countQuery = supabase
  .from("products_services")
  .select("id", { count: "exact" })
  .in("business_id", validBusinessIds)
  .eq("is_available", true);

// Step 3: Fetch products with exact field selection
let productsQuery = supabase
  .from("products_services")
  .select(
    `
    id, business_id, name, description, base_price, discounted_price, product_type,
    is_available, image_url, created_at, updated_at, slug,
    business_profiles!business_id(business_slug)
  `
  )
  .in("business_id", validBusinessIds)
  .eq("is_available", true);
```

**Critical Requirements**:

1. **Never filter by city** in fetchAllProducts (that's handled in locationActions)
2. **Use exact field selection** as shown above
3. **Implement exact sorting logic** including price handling
4. **Use convertToNearbyProduct** to process results
5. **Maintain exact pagination** logic

**Detailed Actions**:

1. Copy entire `fetchAllProducts` function from Next.js
2. Copy `convertToNearbyProduct` helper function exactly
3. Ensure two-step query approach is preserved
4. Test with real data to verify products are returned
5. Verify sorting works correctly (especially price sorting)
6. Confirm pagination calculations are accurate

#### Step 6: Implement businessActions.ts

**Task**: Create business search logic using secure business profiles

**Reference**: `dukancard/app/(main)/discover/actions/businessActions.ts`

**Functions to Implement**:

1. `fetchBusinessesBySearch` (main function)
2. Integration with `getSecureBusinessProfiles` equivalent

**Critical Business Query Pattern**:

```typescript
// Use secure business profiles method (to be implemented in utils)
const businessResult = await getSecureBusinessProfilesRN({
  searchTerm: businessName,
  pincode,
  locality,
  city,
  category,
  sortBy,
  page,
  limit,
});
```

**Key Differences from Products**:

1. **Uses secure business method** instead of direct queries
2. **Handles subscription status** and business verification
3. **Includes distance calculation** for location-based results
4. **Different field selection** for business card data

**Detailed Actions**:

1. Copy `fetchBusinessesBySearch` function from Next.js
2. Replace `getSecureBusinessProfiles` calls with RN equivalent
3. Ensure proper error handling and validation
4. Implement distance calculation for user location
5. Test business search functionality
6. Verify business card data structure matches frontend expectations

#### Step 7: Implement locationActions.ts

**Reference**: `dukancard/app/(main)/discover/actions/locationActions.ts`

- Copy searchDiscoverByLocation function
- Adapt location logic for React Native:
  - When no location provided: fetch user's stored location
  - Use stored location for nearby results
  - Store fetched address until manually changed
- Maintain pincode validation logic
- Handle city-to-pincode mapping

### Phase 3: Utility Functions (Steps 8-10)

#### Step 8: Create secureBusinessProfiles.ts

**Reference**: Next.js `getSecureBusinessProfiles` function

- Implement React Native equivalent of secure business method
- Apply proper filtering and sorting
- Return structured BusinessCardData
- Implement pagination logic

#### Step 9: Create locationUtils.ts

**Reference**: Next.js location handling patterns

- Implement fetchUserStoredLocation function
- Handle location validation and mapping
- Manage AsyncStorage for location persistence
- Implement fallback logic for missing locations

#### Step 10: Create sortMappings.ts

**Reference**: Next.js sorting utilities

- Exact copy of sorting functions
- Maintain same sort column mappings
- Handle price sorting special cases
- Ensure compatibility with React Native

### Phase 4: Integration and Testing (Steps 11-13)

#### Step 11: Update DiscoveryContext

**Reference**: Current `dukancard-app/src/contexts/DiscoveryContext.tsx`

- Update imports to use new discovery services
- Maintain existing context API
- Ensure backward compatibility
- Test all context functions

#### Step 12: Update Type Definitions

**Reference**: `dukancard-app/src/types/discovery.ts`

- Ensure type compatibility with new services
- Update any changed interfaces
- Maintain existing component compatibility
- Fix any type errors

#### Step 13: Test and Debug

- Test all 4 search cases
- Verify location handling works correctly
- Check pagination and sorting
- Validate performance improvements
- Fix any remaining issues

## Risk Assessment

### High Risk

- **Breaking Changes**: Incompatible API changes affecting frontend components
- **Performance Regression**: New implementation slower than current
- **Complete System Failure**: Discovery system stops working entirely

### Medium Risk

- **Type Incompatibility**: Type mismatches between Next.js and React Native
- **Location Logic Bugs**: Incorrect location handling causing no results
- **Query Failures**: Database queries not working in React Native environment

### Low Risk

- **Minor UI Issues**: Small display inconsistencies (should be none due to no frontend changes)
- **Sorting Edge Cases**: Minor sorting behavior differences

## Mitigation Strategies

- **Git-based Recovery**: Use git to restore previous implementation if needed
- **Incremental Testing**: Test each phase before proceeding to next
- **API Compatibility**: Maintain exact same function signatures and return types
- **Real Data Testing**: Test with actual database data at each step
- **Performance Monitoring**: Compare response times before and after
- **Frontend Isolation**: Ensure zero frontend changes required

## Success Metrics

- Products load correctly: 100% success rate
- Business cards load correctly: 100% success rate
- Search response time: < 2 seconds
- Location detection accuracy: > 95%
- Zero infinite loops or memory leaks
- Type safety: Zero TypeScript errors

## Timeline

- **Phase 1**: 2-3 hours (Foundation setup)
- **Phase 2**: 4-6 hours (Core implementation)
- **Phase 3**: 2-3 hours (Utilities)
- **Phase 4**: 2-3 hours (Integration and testing)
- **Total**: 10-15 hours

## Dependencies

- Supabase client library
- AsyncStorage for location persistence
- Existing React Native components
- Next.js reference implementation

## Deliverables

1. Complete new discovery service architecture
2. All products and business cards loading correctly
3. Proper location handling for React Native
4. Database-level pagination working
5. Performance optimization achieved
6. Documentation of changes made

## Next Steps After Completion

1. Monitor performance in production
2. Gather user feedback on search functionality
3. Optimize queries based on usage patterns
4. Keep synchronized with Next.js changes
5. Plan additional features based on mobile use cases
