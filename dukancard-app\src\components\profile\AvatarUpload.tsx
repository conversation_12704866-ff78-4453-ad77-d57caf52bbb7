import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Image } from 'expo-image';
import * as ImagePicker from 'expo-image-picker';
import { Camera, User } from 'lucide-react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { useAuth } from '@/src/contexts/AuthContext';
import { updateCustomerAvatar } from '@/backend/supabase/services/common/profileService';
import { uploadAvatarImage } from '@/backend/supabase/services/storage/avatarUploadService';

interface AvatarUploadProps {
  initialAvatarUrl?: string | null;
  userName?: string | null;
  size?: number;
  showLabel?: boolean;
  onAvatarUpdated?: (url: string) => void;
}

export function AvatarUpload({
  initialAvatarUrl,
  userName,
  size = 120,
  showLabel = true,
  onAvatarUpdated,
}: AvatarUploadProps) {
  const { user } = useAuth();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [avatarUrl, setAvatarUrl] = useState<string | null>(initialAvatarUrl || null);
  const [isUploading, setIsUploading] = useState(false);

  useEffect(() => {
    setAvatarUrl(initialAvatarUrl || null);
  }, [initialAvatarUrl]);

  const requestPermissions = async () => {
    const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
    const { status: mediaStatus } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    return {
      camera: cameraStatus === 'granted',
      media: mediaStatus === 'granted',
    };
  };

  const showImagePickerOptions = () => {
    Alert.alert(
      'Select Avatar',
      'Choose how you want to select your profile picture',
      [
        {
          text: 'Camera',
          onPress: () => handleImageSelection('camera'),
        },
        {
          text: 'Gallery',
          onPress: () => handleImageSelection('gallery'),
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  const handleImageSelection = async (source: 'camera' | 'gallery') => {
    try {
      const permissions = await requestPermissions();
      
      if (source === 'camera' && !permissions.camera) {
        Alert.alert('Permission Required', 'Camera permission is required to take photos');
        return;
      }
      
      if (source === 'gallery' && !permissions.media) {
        Alert.alert('Permission Required', 'Media library permission is required to select photos');
        return;
      }

      setIsUploading(true);

      let result: ImagePicker.ImagePickerResult;

      if (source === 'camera') {
        result = await ImagePicker.launchCameraAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: true,
          aspect: [1, 1],
          quality: 0.8,
        });
      } else {
        result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: true,
          aspect: [1, 1],
          quality: 0.8,
        });
      }

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const selectedUri = result.assets[0].uri;
        await handleImageUpload(selectedUri);
      }
    } catch (error) {
      console.error('Image selection error:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleImageUpload = async (imageUri: string) => {
    if (!user?.id) {
      Alert.alert('Error', 'User not authenticated');
      return;
    }

    try {
      // Upload image to storage
      const uploadResult = await uploadAvatarImage(imageUri, user.id);
      
      if (!uploadResult.success || !uploadResult.url) {
        throw new Error(uploadResult.error || 'Failed to upload image');
      }

      // Update customer profile with new avatar URL
      const updateResult = await updateCustomerAvatar(uploadResult.url);
      
      if (!updateResult.success) {
        throw new Error(updateResult.error || 'Failed to update profile');
      }

      // Update local state
      setAvatarUrl(uploadResult.url);
      onAvatarUpdated?.(uploadResult.url);
      
      Alert.alert('Success', 'Profile picture updated successfully!');
    } catch (error) {
      console.error('Avatar upload error:', error);
      Alert.alert('Error', error instanceof Error ? error.message : 'Failed to update profile picture');
    }
  };

  // Generate initials from name
  const getInitials = (name?: string | null) => {
    if (!name) return 'U';

    const parts = name.split(/\s+/);
    if (parts.length === 1) {
      return name.substring(0, 2).toUpperCase();
    }

    return (
      parts[0].charAt(0) + parts[parts.length - 1].charAt(0)
    ).toUpperCase();
  };

  const initials = getInitials(userName);
  const goldColor = '#D4AF37';

  return (
    <View style={styles.container}>
      {showLabel && (
        <Text style={[styles.label, { color: isDark ? '#fff' : '#000' }]}>
          Profile Picture
        </Text>
      )}

      <TouchableOpacity
        style={[
          styles.avatarButton,
          {
            width: size,
            height: size,
            borderRadius: size / 2,
            borderColor: goldColor,
            backgroundColor: isDark ? '#1F2937' : '#F9FAFB',
          },
          isUploading && styles.disabled,
        ]}
        onPress={showImagePickerOptions}
        disabled={isUploading}
        activeOpacity={0.7}
      >
        {avatarUrl ? (
          <>
            <Image
              source={{ uri: avatarUrl }}
              style={[
                styles.avatarImage,
                {
                  width: size - 4,
                  height: size - 4,
                  borderRadius: (size - 4) / 2,
                },
              ]}
              contentFit="cover"
              transition={200}
            />
            {/* Upload overlay */}
            <View
              style={[
                styles.uploadOverlay,
                {
                  width: size,
                  height: size,
                  borderRadius: size / 2,
                },
              ]}
            >
              {isUploading ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Camera size={Math.max(16, size * 0.2)} color="#FFFFFF" />
              )}
            </View>
          </>
        ) : (
          <View
            style={[
              styles.avatarPlaceholder,
              {
                width: size - 4,
                height: size - 4,
                borderRadius: (size - 4) / 2,
                backgroundColor: goldColor + '20',
              },
            ]}
          >
            {isUploading ? (
              <ActivityIndicator size="small" color={goldColor} />
            ) : (
              <>
                <User size={Math.max(24, size * 0.3)} color={goldColor} />
                <Text
                  style={[
                    styles.initialsText,
                    {
                      fontSize: Math.max(16, size * 0.25),
                      color: goldColor,
                    },
                  ]}
                >
                  {initials}
                </Text>
              </>
            )}
          </View>
        )}
      </TouchableOpacity>

      {showLabel && (
        <Text style={[styles.hint, { color: isDark ? '#999' : '#666' }]}>
          {isUploading ? 'Uploading...' : 'Tap to change picture'}
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  avatarButton: {
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    overflow: 'hidden',
  },
  avatarImage: {
    // Image styles are set dynamically
  },
  avatarPlaceholder: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  initialsText: {
    fontWeight: 'bold',
    position: 'absolute',
  },
  uploadOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    opacity: 0.8,
  },
  hint: {
    fontSize: 12,
    marginTop: 8,
    textAlign: 'center',
  },
  disabled: {
    opacity: 0.5,
  },
});
