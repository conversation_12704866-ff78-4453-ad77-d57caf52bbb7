---
description: 
globs: 
alwaysApply: false
---
# Task List Management

Guidelines for managing task lists in markdown files to track progress on completing a PRD

## Task Implementation
- **One sub-task at a time:** Do **NOT** start the next sub‑task until you ask the user for permission and they say “yes” or "y"
- **Completion protocol:**  
  1. When you finish a **sub‑task**, immediately mark it as completed by changing `[ ]` to `[x]`.  
  2. If **all** subtasks underneath a parent task are now `[x]`, also mark the **parent task** as completed.  
- Stop after each sub‑task and wait for the user’s go‑ahead.

## Task List Maintenance

1. **Update the task list as you work:**
   - Mark tasks and subtasks as completed (`[x]`) per the protocol above.
   - Add new tasks as they emerge.

2. **Maintain the “Relevant Files” section:**
   - List every file created or modified.
   - Give each file a one‑line description of its purpose.

## AI Instructions

When working with task lists, the AI must:

1. Regularly update the task list file after finishing any significant work.
2. Follow the completion protocol:
   - Mark each finished **sub‑task** `[x]`.
   - Mark the **parent task** `[x]` once **all** its subtasks are `[x]`.
3. Add newly discovered tasks.
4. Keep “Relevant Files” accurate and up to date.
5. Before starting work, check which sub‑task is next.
6. After implementing a sub‑task, update the file and then continue automatically without waiting for user approval.
7. Make sure there are placeholder or incomplete code for the current sub-task. If there are placeholders or icomplete code, then complete them first before moving to next sub-task.
8. After completing each sub task, run TypeScript and ESLint checks to fix all errors and warnings for React Native app only (exclude dukancard directory):
   - TypeScript: npx tsc --noEmit --project tsconfig.json
   - ESLint: npx eslint . --ext .ts,.tsx
   - Do not move to next task unless you fix all errors and warnings of previous sub-task
   - Only check files in the React Native app directory, exclude dukancard/ folder from checks

Notes:
- If you face import issues and the imports you add are removed automatically, then you should make the changes first in the file and then import so that the imports are in use and won't be removed automatically.